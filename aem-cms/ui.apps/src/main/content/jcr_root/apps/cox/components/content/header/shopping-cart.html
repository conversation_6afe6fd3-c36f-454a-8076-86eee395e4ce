<div data-sly-use.header4ShoppingCart="com.cox.aem.common.components.header.Header" data-sly-unwrap>
	<div data-sly-use.header="${'com.cox.aem.common.models.HeaderPageData'}" data-sly-unwrap>
	
	<div data-sly-test="${header4ShoppingCart.displayShoppingCart}" data-sly-unwrap>
			<sly data-sly-test="${header.newGlobalNav != true || header.lineOfBusiness == 'business'}">	
				<li class="cart">
					<a data-sly-use.getConfig="com.cox.aem.common.models.CoxAppConfigModel" href="${getConfig.cartUrl}">View Cart</a>		
				</li>
			</sly>
			<sly data-sly-test="${header.newGlobalNav == true && header.lineOfBusiness != 'business'}">
						<sly data-sly-set.navLinkClass="pf-cox-menu-tab pf-cox-auth-link pf-cox-menu-link"></sly>
				<sly data-sly-set.navLiClass="pf-cox-parent-menu"></sly>
				<sly data-sly-set.navNoOverlay="pf-no-overlay"></sly>
			<li role="presentation" class="pf-cart-menu pf-cox-parent-menu shopping-cart-menu">
					<sly data-sly-set.navNoOverlay="pf-no-overlay"></sly>
					<sly data-sly-set.navPath="${signin.resOktaUrl}"></sly>
					<a href="${properties.shoppingCart}" class="pf-cox-menu-tab" data-isNav="${properties.newNavigation}" data-nav-path="${signin.resNewNavUrl}" data-okta="${properties.oktaAuthentication}" data-okta-path="${signin.resOktaUrl}" data-cox-menu-name="shopping-cart-section" id="pf-shopping-cart-trigger" aria-haspopup="true" data-onSuccess-url="${signin.resOnSuccessURL}">
						<span class="pf-sr-only">Shopping Cart</span><span></span>
					</a>
					<ul class="pf-main-nav-primary-links pf-cox-navigation-links">
						<sly data-sly-resource="${ @path='shoppingCartNavigation', resourceType='foundation/components/parsys'}" data-sly-unwrap>
						</sly>
					</ul>
			</li>
			</sly>
	</div>
	</div>
</div>