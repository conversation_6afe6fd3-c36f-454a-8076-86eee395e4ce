<!--/* begin headerdata div wrapper */-->
<sly data-sly-set.notificationClass="pf-no-notification"></sly>
<sly data-sly-set.notificationNavigationLink="#"></sly>
<sly data-sly-set.notificationAClass=""></sly>
<sly data-sly-set.notificationServiceUrl=""></sly>
<sly data-sly-set.notificationDispositionUrl=""></sly>
<sly data-sly-set.notificationCount=""></sly>
<sly data-sly-set.notificationContextId=""></sly>
<sly data-sly-set.residentialNotification="false"></sly>
<div data-sly-use.headerPageData="${'com.cox.aem.common.models.HeaderPageData'}" data-sly-use.getConfig="com.cox.aem.common.models.CoxAppConfigModel" data-sly-use.signinheader="com.cox.aem.common.components.signin.SignInHeader" data-sly-use.header="com.cox.aem.common.components.header.Header"
     data-sly-unwrap>

    <script data-sly-test="${(getConfig.cbmyaccountMenuurlEnable && headerPageData.lineOfBusiness == 'business') && (signinheader.loginStatus =='loggedin' || signinheader.loginStatus =='sudologin' )}">
        var CciMenuUrl = "${getConfig.cciMenuUrl @context='html'}";
        var CciMenuApiKey = "${getConfig.cbMenuapikey @context='html'}";
        var CciMenuClienId = "${getConfig.cbMenuclientid @context='html'}";
    </script>

    <script data-sly-test="${(getConfig.cciOktaSessionMeUrl != '')}">
        var CciMenuUrl = "${getConfig.cciMenuUrl @context='html'}";
        var CciMenuApiKey = "${getConfig.cbMenuapikey @context='html'}";
        var CciMenuClienId = "${getConfig.cbMenuclientid @context='html'}";
    </script>    
    
    <sly data-sly-set.pfcoxClass=""></sly>
    <sly data-sly-set.newGlobalNavClassMobile=""></sly>
    <sly data-sly-set.newGlobalNavClass=""></sly>
    <sly data-sly-set.globalNavRebrandClass=""></sly>
    <sly data-sly-test="${headerPageData.newGlobalNav == true && headerPageData.lineOfBusiness != 'business'}">
        <sly data-sly-set.newGlobalNavClass="header-flyoutmenu-rebrand-lg"></sly>
        <sly data-sly-set.newGlobalNavClassMobile="header-flyoutmenu-rebrand"></sly>
        <sly data-sly-set.globalNavRebrandClass="global-nav-rebrand"></sly>
        <sly data-sly-set.pfcoxClass="pf-flyout-overlay"></sly>
    </sly>
    

    <sly data-sly-test="${header.residentialAlertNotification == true && headerPageData.lineOfBusiness != 'business'}">
        <sly data-sly-set.residentialNotification="true"></sly>
    </sly>
    <sly data-sly-test="${residentialNotification == 'true'}">
        <sly data-sly-set.notificationServiceUrl="${header.notificationServiceUrl}"></sly>
        <sly data-sly-test.notificationDispositionUrl="${header.notificationDispositionUrl}"></sly>
        <sly data-sly-set.notificationCount="${header.residentialAlertNotificationCount}"></sly>
        <sly data-sly-set.notificationContextId="${header.residentialAlertNotificationContextId}"></sly>
        <sly data-sly-set.notificationNavigationLink="${header.notificationUrl}"></sly>
    </sly>
</div>



<script>
	var CciFrameworkUrl = "${getConfig.cciFrameworkurl @context='html'}";
	var CciUserInfoUrl = "${headerPageData.cciUserInfoUrl @context='uri'}?webp=${header.webpFlag @context='text'}";
	var CciAutoCompleteUrl = "";
	var CciConfirmAddressUrl = "${headerPageData.cciConfirmAddressUrl @context='html'}";
	var CciFrameworkStrings = ${headerPageData.loginErrorConfig @context='unsafe'};
	var CciOktaUrl = "${getConfig.cciOktaSessionMeUrl @context='html'}";
	var cbsid = "${getConfig.cbsidCookieName @context='html'}";
	var isSetDISPDisable = "${getConfig.getSetDispositionDisableStatus @context='html'}";
</script>

<!--/* Begin Global Nav for Espanol/EasyLink code */-->
<!--/* mp_snippet_begins */-->
<script data-sly-test="${headerPageData.lineOfBusiness != 'business'}">
    var MP = {
        Version: "*******",
        SrcLang: "en",
        UrlLang: "mp_js_current_lang",
        SrcUrl: decodeURIComponent("mp_js_orgin_url"),
        oSite: decodeURIComponent("mp_js_origin_baseUrl"),
        tSite: decodeURIComponent("mp_js_translated_baseUrl"),
        init: function() {
            1 == MP.oSite.indexOf("p_js_") && (MP.SrcUrl = window.top.document.location.href, MP.oSite = MP.tSite = window.top.document.location.host, MP.UrlLang = MP.SrcLang)
        },
        switchLanguage: function(a, b, c) {
            var c = c,
                d = MP.oSite.replace("http://", "").replace("https://", "").replace(/\/?$/, ""),
                e = MP.tSite.replace("http://", "").replace("https://", "").replace(/\/?$/, "");
            if (a = a.replace("http://", "").replace("https://", "").replace(/\/?$/, ""), c && "undefined" != typeof MpStorage && "undefined" != typeof MpStorage.updatePref) {
                if (b) {
                    var f = b.substring(0, 2);
                    b = f == MP.UrlLang ? MP.SrcLang + b.substring(2) : b
                }
                MpStorage.updatePref(a, b)
            }
            return setTimeout(function() {
                var b = document.createElement("SCRIPT");
                a == d ? b.src = location.protocol + "//" + e + "?1023749634;" + encodeURIComponent(location.href) : b.src = location.protocol + "//" + a + "?1023749632;" + encodeURIComponent(MP.SrcUrl);
                var c = document.getElementsByTagName("script")[0];
                c.parentNode.insertBefore(b, c)
            }, 500), !1
        },
        switchToLang: function(a) {
            window.top.location.href == a ? "undefined" != typeof MpStorage && "undefined" != typeof MpStorage.updatePref && MpStorage.updatePref(MP.oSite, MP.SrcLang) : window.top.location.href = a
        }
    };
    MP.UrlLang = 'mp_js_current_lang';
    MP.SrcUrl = decodeURIComponent('mp_js_orgin_url');
    MP.oSite = decodeURIComponent('mp_js_origin_baseUrl');
    MP.tSite = decodeURIComponent('mp_js_translated_baseUrl');
    MP.init();
    window.onload = function() {
        var langlinks = document.querySelectorAll('.langLink');
        for (var i = 0; i < langlinks.length; i++) {
            langlinks.item(i).onclick = function() {
                var lang = this.getAttribute('data-lang');
                var url = this.getAttribute('data-href');
                var tSite = MP.tSite.replace('http://', '').replace('https://', '');
                url = url.replace('http://', '').replace('https://', '');
                MP.switchLanguage(tSite.search(url) != -1 ? MP.oSite : url, lang, true);
                return false;
            }
        }
    }
</script>
<!--/* mp_snippet_ends */-->
<!--/* End Global Nav for Espanol/EasyLink code */-->

<!--/* header */-->
<div id="pf-header" class="noindex ${headerPageData.lineOfBusiness == 'residential' ? 'pf-header-residential' : ''}${headerPageData.lineOfBusiness == 'business' ? 'pf-header-business' : ''} ${headerPageData.lineOfBusiness == 'aboutus' ? 'pf-header-residential' : ''}">
    <!--/* Skip to Main Content */-->
    <div id="pf-skip-nav">
        <a href="#container" class="pf-sr-only" aria-label="Skip to Main Content">Skip to Main Content</a>
    </div>
    <!--/* begin header wrapper */-->
    <div class="pf-header-wrapper">
        <!--/* begin menu panel - left side panel on mobile */-->
        <div class="pf-menu-panel">
            <!--/* begin mobile wrapper */-->
            <div class="pf-mobile-wrapper">
                <!--/* begin top header */-->
                <div class="pf-top-header">
                    <div class="pf-top-nav">
                        <!--/* left side of top nav */-->
                        <sly data-sly-include="line-of-business-tabs.html"></sly>
                        <!--/* right side of top nav */-->
                        <ul class="pf-top-nav-overlays">
                            <!--/* begin cart link */-->
                            <sly data-sly-test="${headerPageData.newGlobalNav != true || headerPageData.lineOfBusiness == 'business'}">
                            	<sly data-sly-include="top-header-shopping-cart.html"></sly>
                            </sly>
                            <!--/* begin contact panel */-->
                            <sly data-sly-include="contact-us.html"></sly>
                            <!--/* end contact panel */-->
                            <!--/* begin geo location panel */-->
                            <sly data-sly-resource="${ @path='geolocation', resourceType='cox/components/content/geolocation'}"></sly>
                            <!--/* end geo location panel */-->
                        </ul>
                    </div>
                </div>
                <!--/* end top header */-->
                <!--/* main header */-->
                <div class="pf-main-header">
                    <div class="pf-main-nav ${globalNavRebrandClass}">
                        <div class="pf-main-left-nav">
                            <ul>
                            <sly data-sly-include="logo.html"></sly>
                            <!--/* Remove below code on code cleanup and and when all migration is done to new header and footer*/-->
                            <sly data-sly-include="mobile-sign-in-out.html"></sly>
                            </ul>

                            <!--/* begin primary link list */-->
                            <sly data-sly-test="${(headerPageData.lineOfBusiness != 'business' && headerPageData.xPGlobalNavResiEnabled) || (headerPageData.lineOfBusiness == 'business' && headerPageData.xPGlobalNavBusiEnabled)}">
                            	<sly data-sly-resource="${ @path='headerNewNavigation/globalNav', resourceType='cox/components/content/globalNav'}"></sly>
                            </sly>
                            <sly data-sly-test="${properties.newNavigation}">
                            	<sly data-sly-resource="${ @path='headerNavigation', resourceType='foundation/components/parsys'}">
                                </sly>
                            </sly>
                        </div>
                        <sly data-sly-test="${headerPageData.lineOfBusiness != 'business'}">
                            <sly data-sly-set.mainNavClass="${notificationClass}"></sly>
                        </sly>
                        <div class="pf-main-right-nav ${newGlobalNavClass} ${mainNavClass}">
                            <ul class="pf-search-cart-tabs pf-cox-navigation-links" role="presentation">
                                <!--/* begin search items */-->
                                <sly data-sly-test="${header.searchCheckbox}">
                                    <sly data-sly-use.headerForSearch="com.cox.aem.common.components.header.Header" data-sly-test="${!headerForSearch.search}" data-sly-resource="${@path='search',resourceType='cox/components/content/yext-search',selectors='headersearch'}"></sly>
                                </sly>
                                <sly data-sly-test="${residentialNotification == 'true'}">
                                    <li class="pf-alert-menu pf-cox-parent-menu pf-d-none" role="presentation" data-url="${notificationServiceUrl}" data-cnt="${notificationCount}" data-set-disp-url="${notificationDispositionUrl}" data-cntxid="${notificationContextId}">
                                        <a href="${notificationNavigationLink}" class="${notificationAClass} pf-cox-menu-tab" data-cox-menu-name="alert-section" aria-haspopup="true" aria-label="My Notification">
                                            <span class="pf-alert-text">Alert</span>
                                            <span class="pf-notification-count pf-d-none"></span>
                                        </a>
                                        <sly data-sly-include="notification.html"></sly>
                                    </li>
                                </sly>

                                <sly data-sly-include="shopping-cart.html"></sly>
                            </ul>
                            <sly data-sly-include="sign-in-out.html"></sly>
                        </div>
                    </div>
                </div>
                <!--/* end main header */-->
            </div>
            <!--/* end mobile wrapper */-->
            <!--/* begin sub header */-->
            <div class="pf-sub-header pf-search-sub-header" data-sly-test="${headerPageData.lineOfBusiness == 'business'}">
                <!--/* begin search items not needed */-->
                <div class="pf-sub-nav pf-search-items">
                    <div class="pf-sub-nav-underlay">
                        <div class="pf-sub-nav-close">
                            <a href="#" role="button" aria-label="Close Menu"><span class="pf-sr-only">Close Menu</span></a>
                        </div>
                    </div>
                    <div class="pf-sub-nav-link"></div>
                </div>
            </div>
          <!--/* end sub header */-->
            </div>
        <!--/* end menu panel - left side panel on mobile */-->        
    </div>
    <!--/* end header wrapper */-->
    <!--/* mobile header */-->
    <sly data-sly-test="${headerPageData.lineOfBusiness != 'business'}">
        <sly data-sly-set.mainNavClass="${notificationClass}"></sly>
    </sly>
    <div class="pf-mobile-menu-bar pf-mobile-only ${mainNavClass}">
        <ul class="pf-mobile-menu-bar-ul ${newGlobalNavClassMobile}">
        <li class="pf-mobile-menu-btn pf-cox-menu-link" data-cox-menu-name="navigation" tabindex="0" role="button">
            <a href="#" aria-label="Menu">Menu</a>
        </li>
        <sly data-sly-use.signin="com.cox.aem.common.components.signin.SignIn">
            <sly data-sly-set.linkClass="pf-trigger"></sly>
            <ul data-sly-test="${headerPageData.lineOfBusiness == 'business'}" data-sly-unwrap>
                <sly data-sly-set.navPath="${signin.cbOktaUrl}"></sly>
                <sly data-sly-set.linkClass=""></sly>
                <li class="header-logo"><a href="${headerPageData.lineOfBussinessBusinessPagepath}" aria-label="Cox Business Homepage logo">Cox Business Homepage logo</a></li>
                <li class="header-search-icon pf-cox-menu-link pf-cox-menu-tab" data-cox-menu-name="search-section"><a href="#" aria-label="Cox Search">Cox Search</a></li>
            </ul>
            <ul data-sly-test="${headerPageData.lineOfBusiness != 'business'}" data-sly-unwrap>
                <sly data-sly-set.navPath="${signin.resOktaUrl}"></sly>
                <sly data-sly-set.linkClass=""></sly>
                <li class="header-logo"><a href="${headerPageData.lineOfBussinessResidentialPagepath}" aria-label="Cox Residential Homepage logo">Cox Residential Homepage logo</a></li>
                
                
    <sly data-sly-test="${headerPageData.newGlobalNav == true && headerPageData.lineOfBusiness != 'business'}">
        <li class="header-flyout"><ul>
    </sly>
    
    
                <li class="header-search-icon ${pfcoxClass} pf-cox-menu-link pf-cox-menu-tab" data-cox-menu-name="search-section"><a href="#" aria-label="Cox Search">Cox Search</a></li>
                <sly data-sly-test="${residentialNotification == 'true'}">
                    <li class="header-alert-icon pf-d-none pf-flyout-overlay ${notificationAClass} pf-cox-menu-tab" data-cox-menu-name="alert-section">
                        <span class="pf-notification-count pf-d-none"></span>
                        <a href="${notificationNavigationLink}" aria-label="My Notification"></a>
                    </li>
                </sly>
                
    			<sly data-sly-test="${headerPageData.newGlobalNav == true && headerPageData.lineOfBusiness != 'business'}">
	                <li class="header-shopping-cart-icon pf-cox-menu-tab" data-cox-menu-name="shopping-cart-section">
	                        <a href="${properties.shoppingCart}">Shopping Cart</a>
	                </li>
    			</sly>
            </ul>

            <!--/* mobile sign in */-->
            <li class="pf-sign-in pf-mobile-signin-btn pf-no-overlay pf-cox-menu-tab" data-cox-menu-name="account">
                   <a href="${navPath}" class="pf-mobile-signin ${linkClass}" aria-label="My Account">My Account</a>
			</li>
			
			
    <sly data-sly-test="${headerPageData.newGlobalNav == true && headerPageData.lineOfBusiness != 'business'}">
        </ul></li>
    </sly>
        </sly>
        <!--/* /mobile sign in */-->
        </ul>
    </div>
    <!--/* /mobile header */-->
    <sly data-sly-use.signin="com.cox.aem.common.components.signin.SignIn">
        <sly data-sly-test="${properties.newNavigation}">
            <script type="text/javascript" src="${signin.cbExtJsUrl}" data-sly-test="${headerPageData.lineOfBusiness == 'business' && signin.cbExtJsUrl !=''}"></script>
            <script type="text/javascript" src="${signin.resExtJsUrl}" data-sly-test="${headerPageData.lineOfBusiness != 'business' && signin.resExtJsUrl !=''}"></script>
        </sly>
    </sly>
</div>
<!--/* /header */-->