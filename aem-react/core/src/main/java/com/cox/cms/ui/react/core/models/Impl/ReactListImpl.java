package com.cox.cms.ui.react.core.models.Impl;

import com.adobe.cq.export.json.ComponentExporter;
import com.adobe.cq.export.json.ExporterConstants;
import com.adobe.cq.wcm.core.components.models.List;
import com.adobe.cq.wcm.core.components.models.ListItem;
import com.cox.cms.ui.react.core.models.ReactListItem;
import com.cox.cms.ui.react.core.models.ReactList;
import com.cox.cms.ui.react.core.service.ReactExternalizerService;
import com.cox.cms.ui.react.core.service.ReadPropertiesService;

import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.models.annotations.*;
import org.apache.sling.models.annotations.injectorspecific.Self;
import org.apache.sling.models.annotations.injectorspecific.SlingObject;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;
import org.apache.sling.models.annotations.via.ResourceSuperType;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import com.day.cq.wcm.api.Page;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Model(	adaptables = SlingHttpServletRequest.class, 
		adapters = { ReactList.class, ComponentExporter.class }, 
		resourceType = ReactListImpl.RESOURCE_TYPE, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
@Exporter(
        name = ExporterConstants.SLING_MODEL_EXPORTER_NAME,
        extensions = ExporterConstants.SLING_MODEL_EXTENSION
)

public class ReactListImpl implements ReactList {
	
	final Logger LOG = LoggerFactory.getLogger(ReactListImpl.class);
    static final String RESOURCE_TYPE = "cox-cms-react/components/list";
    
    static final String CONTEXT_URL = "/content/cox";
    static final String CORPORATE_PAGES_URL = "/corporate/pages";

    @Self
    private SlingHttpServletRequest request;
    
    @Inject
    private ReactExternalizerService externalizerService;
    
    @SlingObject
    private Resource resource;

    @Self
    @Via(type = ResourceSuperType.class)
    private List list;


	@ValueMapValue
	private String variation;

	@ValueMapValue
	private String listType;

	@ValueMapValue
	private boolean top;

	@ValueMapValue
	private boolean bottom;

	@ValueMapValue
	private boolean right;

	@ValueMapValue
	private boolean left;

	@ValueMapValue
	private boolean isHideMCP;
    
    /**
     * @return
     */
    @Override
    public String getIcon() {
        return icon;
    }
    
    /**
     * @return
     */
    @Override
    public String getId() {
        return id;
    }

    @ValueMapValue
    private String icon;

      @ValueMapValue
    private String alticon;
    
    @ValueMapValue
    private String id;
    
    @ValueMapValue
    private String heading;

    @ValueMapValue
    private String subheading;

    @ValueMapValue
    private String listFrom;

	private java.util.List<String> titleList = new ArrayList<String>();

	private java.util.List<String> title = new ArrayList<String>();

	@Inject
	private ReadPropertiesService readPropertiesService;

    @Override
    public String getExportedType() {
        return ReactListImpl.RESOURCE_TYPE;
    }

    @Override
    public Collection<Page> getItems() {
        return null != list ? list.getItems() : null;
    }
    

    public boolean getLinkItems() {
    	return null != list ? list.linkItems() : null;
    }
    
    public boolean getShowDescription() {
    	return null != list ? list.showDescription() : null;
    }
    
    public boolean getShowModificationDate() {
    	return null != list ? list.showModificationDate() : null;
    }
    
    @Override
    public String getDateFormatString() {
    	return null != list ? list.getDateFormatString() : null;
    }
    @Override
	public String getHeading() {
		return heading;
	}

        @Override
	public String getAltIcon() {
		return alticon;
	}

	@Override
	public String getSubheading() {
		try {
			if (StringUtils.isNotEmpty(subheading) && subheading.indexOf("href=") != -1) {
				subheading = subheading.indexOf(CONTEXT_URL) != -1 ? subheading.replace(CONTEXT_URL, "")
						: subheading;
				subheading = subheading.indexOf(CORPORATE_PAGES_URL) != -1
						? subheading.replace(CORPORATE_PAGES_URL, "")
						: subheading;
			}
		} catch (Exception e) {
			LOG.error("Exception while shortening url for the description : {}", e.getMessage());
		}
		return subheading;
	}
  
    @Override
    public String getlistFrom() { 
    	listFrom = "fixedList".equalsIgnoreCase(listFrom) ? "static" : listFrom;
        return listFrom;
    }
    
	@Override
	public java.util.List<ReactListItem> getFixedListItems() {
		return ("static".equalsIgnoreCase(listFrom) || "fixedList".equalsIgnoreCase(listFrom)) ? getLists("fixedListItems") : null;
		
	}
    
	@Override
	public java.util.List<ReactListItem> getCategoryitems() {
		return "featuredCategory".equalsIgnoreCase(listFrom) ? getLists("categoryitems") : null;
		
	}
	@Override
	public java.util.List<ReactListItem> getDifferentIconsList() {
		return "differentIcons".equalsIgnoreCase(listFrom) ? getLists("differentIconsList") : null;
	}

	private java.util.List<ReactListItem> getLists(String listNodeName) {
		if( resource.getChild(listNodeName) != null && resource.getChild(listNodeName).hasChildren()) {
			java.util.List<ReactListItem> listItems = new ArrayList<>();

	        Resource childResource = resource.getChild(listNodeName);

	        if (childResource != null) {
	            for (Resource resource : childResource.getChildren()) {
	                ValueMap props = resource.adaptTo(ValueMap.class);
	                String id = props.get("id", String.class);
	                String icon = props.get("icon", String.class);
	                String iconAlt = props.get("iconAlt", String.class);
					Boolean useRTE = props.get("useRTE", Boolean.class);
//					titleList.add(props.get("title", String.class));
					String description = props.get("description", String.class);
					String anchor = props.get("anchor", String.class);
					String link = props.get("link", String.class);
					try {
						if (null != externalizerService) {
							link = externalizerService.getExternalizerUrl(link);
						}
					} catch (LoginException e) {
						LOG.error("Exception while gettting the externalized url for link : {}", e.getMessage());
					}
					String linkTarget = props.get("linkTarget", String.class);
					if (StringUtils.isNotEmpty(id)) {
						if (null != useRTE && useRTE) {
							String title = props.get("titleRTE", String.class);
							LOG.info("RTE CHECK TRIGGER");
							titleList.add(props.get("titleRTE", String.class));
							try {
								if (StringUtils.isNotEmpty(title) && title.indexOf("href=") != -1) {

									title = title.indexOf(CONTEXT_URL) != -1 ? title.replace(CONTEXT_URL, "") : title;
									title = title.indexOf(CORPORATE_PAGES_URL) != -1 ? title.replace(CORPORATE_PAGES_URL, "") : title;
								}
							} catch (Exception e) {
								LOG.error("Exception while shortening url for the text : {}", e.getMessage());
							}
							listItems.add(new ReactListItemImpl(id, icon, iconAlt, title, description, anchor, link, linkTarget, useRTE));
						}else{
							String title = props.get("title", String.class);
							LOG.info("RTE UNCHECK TRIGGER");
							titleList.add(props.get("title", String.class));
							listItems.add(new ReactListItemImpl(id, icon, iconAlt, title, description, anchor, link, linkTarget));
						}


	                }
	            }
	        }
	        return listItems;
		}else {
			return Collections.emptyList();
		}
		
	}

	@Override
	public String getTokenProperties() {
		getFixedListItems();
		getCategoryitems();
		getDifferentIconsList();
		JsonObject tokenPropsJson = new JsonObject();
		try {
			LOG.info("TokenText :{}", titleList.toString());
			if (StringUtils.isNotEmpty(titleList.toString()) && titleList.toString().indexOf("token.") != -1) {
				String regex = "\\[\\[(.*?)\\]\\]";

				final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
				final Matcher matcher = pattern.matcher(titleList.toString());
				Boolean ruleFound = false;
				while (matcher.find()) {
					LOG.info("found a matcher ...");
					String matchedRule = matcher.group().toString();
					String tokenPropName = matchedRule.replace("[", "").replace("]", "").replaceAll("token.", "");
					LOG.info("tokenPropName : {}", tokenPropName);
					String tokenPropsStr = readPropertiesService.getTokenProperties(tokenPropName);
					if (StringUtils.isNotEmpty(tokenPropsStr)) {
						tokenPropsJson.addProperty(tokenPropName, tokenPropsStr);
					}
				}
			}
		} catch (LoginException e) {
			LOG.error("Exception while getting the token properties. {}", e.getMessage());
		}
		return tokenPropsJson.toString();
	}

	@Override
	public String getListType() {
		return listType;
	}

	@Override
	public String getVariation() {
		return variation;
	}

	@Override
	public boolean isTop() {
		return top;
	}

	@Override
	public boolean isBottom() {
		return bottom;
	}

	@Override
	public boolean isRight() {
		return right;
	}

	@Override
	public boolean isLeft() {
		return left;
	}

	@ValueMapValue
	private String lob;
	
	@Override
	public String getLob() {
		return lob;
	}

	@Override
	public boolean isHideMCP() {
		return isHideMCP;
	}
   
}
