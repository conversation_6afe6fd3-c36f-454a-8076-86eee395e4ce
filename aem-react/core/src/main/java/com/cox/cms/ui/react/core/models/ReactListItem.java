package com.cox.cms.ui.react.core.models;

import com.adobe.cq.export.json.ComponentExporter;

import org.apache.sling.api.resource.LoginException;
import org.osgi.annotation.versioning.ProviderType;

@ProviderType
public interface ReactListItem extends ComponentExporter {

    public String getId();
    
    public String getIcon();
    
    public String getIconAlt();
    
    public String getTitle();
    
    public String getDescription();
 
    public String getAnchor();
    
    public String getLink()throws LoginException;
    
    public String getOpeninnewtab();

	public void setIcon(String string);

	public String getListDescription();

	public void setListDescription(String string);
	
	public String getLob();

    public Boolean getUseRTE();
}