package com.cox.cms.ui.react.core.models.Impl;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.inject.Inject;
import javax.inject.Named;
import javax.jcr.Node;
import javax.jcr.Property;
import javax.jcr.PropertyIterator;
import javax.jcr.PropertyType;
import javax.jcr.RepositoryException;
import javax.jcr.Value;
import javax.jcr.ValueFactory;
import javax.jcr.ValueFormatException;

import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.api.resource.ModifiableValueMap;
import org.apache.sling.api.resource.PersistenceException;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.commons.json.JSONException;
import org.apache.sling.commons.json.jcr.JsonItemWriter;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Exporter;
import org.apache.sling.models.annotations.ExporterOption;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.Via;
import org.apache.sling.models.annotations.injectorspecific.Self;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;
import org.apache.sling.models.annotations.via.ResourceSuperType;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adobe.cq.export.json.ComponentExporter;
import com.adobe.cq.export.json.ExporterConstants;
import com.adobe.cq.wcm.core.components.models.Accordion;
import com.adobe.cq.wcm.core.components.models.ListItem;
import com.adobe.cq.wcm.core.components.models.contentfragment.ContentFragment;
import com.adobe.cq.wcm.core.components.models.datalayer.ComponentData;
import com.cox.cms.ui.react.core.models.ReactAccordion;
import com.cox.cms.ui.react.core.models.ReactListItem;
import com.cox.cms.ui.react.core.service.ReactExternalizerService;
import com.cox.cms.ui.react.core.service.ReadPropertiesService;
import com.cox.cms.ui.react.core.service.ResourceResolverService;
import com.cox.cms.ui.react.core.service.RuleBuilderService;
import com.day.cq.commons.jcr.JcrConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.google.gson.TypeAdapter;


@Model(
        adaptables = SlingHttpServletRequest.class,
        adapters = {
                ReactAccordion.class,
                ComponentExporter.class
        },
        resourceType = ReactAccordionImpl.RESOURCE_TYPE,
        defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL
)
@Exporter(
        name = ExporterConstants.SLING_MODEL_EXPORTER_NAME,
        extensions = ExporterConstants.SLING_MODEL_EXTENSION,
        options = {
                @ExporterOption(name="MapperFeature.SORT_PROPERTIES_ALPHABETICALLY",value = "true")
        }
)

public class ReactAccordionImpl implements ReactAccordion {
    static final String RESOURCE_TYPE = "cox-cms-react/components/accordion";
    final Logger LOG = LoggerFactory.getLogger(ReactAccordionImpl.class);
    
    final String ruleNameIdentifier = "RuleName.";
	final String regex = "("+this.ruleNameIdentifier+".)\\S+";
	
    static final String CONTEXT_URL = "/content/cox";
    static final String CORPORATE_PAGES_URL = "/corporate/pages";
    
    final String EXTERNAL_URL_REG_EXP = "[(http|https|www?):\\/\\/(www\\.)?a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)";

    ObjectMapper mapper = new ObjectMapper();
	
	List<String> tokenList =  new ArrayList<String>();
	
    @Self
    private SlingHttpServletRequest request;
    
    @Inject
    private ResourceResolverService resourceResolverService;
    
    @Inject
    private ReadPropertiesService readPropertiesService;
    
    @Inject
    private ReactExternalizerService externalizerService;
    
    @Inject
    private RuleBuilderService ruleBuilderService;
	
	@ValueMapValue
	private boolean isHideMCP;
	
	@ValueMapValue
	private boolean enableFAQs;
    
    final TypeAdapter<JsonElement> strictAdapter = new Gson().getAdapter(JsonElement.class);

    // With sling inheritance (sling:resourceSuperType) we can adapt the current resource to the Accordion class
    // this allows us to re-use all of the functionality of the Accordion class, without having to implement it ourself
    // see https://github.com/adobe/aem-core-wcm-components/wiki/Delegation-Pattern-for-Sling-Models
    @Self
    @Via(type = ResourceSuperType.class)
    private Accordion accordion;

    @ValueMapValue
    private boolean manualitems;
    
    @ValueMapValue
    private boolean fragmentaccordions;
    
    @ValueMapValue
    private boolean offercardaccordions;

	@Self
	@Named("fragmentPath") //name fro dialog
	private ContentFragment contentFragmentModel;
	
	@Self
    @Named("cfaccordions") //name fro dialog
    private ContentFragment contentFragmentContent;

	@ValueMapValue
	private String fragmentPath;
	
	@ValueMapValue
	private String accordionIcon;

	@ValueMapValue
	private boolean top;

	@ValueMapValue
	private boolean bottom;

	@ValueMapValue
	private boolean right;

	@ValueMapValue
	private boolean left;
	
	public boolean isEnableFAQs() {
		return enableFAQs;
	}

	public void setEnableFAQs(boolean enableFAQs) {
		this.enableFAQs = enableFAQs;
	}
	
   // Re-use the Accordion class for all other methods:   

	@Override
	public boolean isManualitems() {
		return manualitems;
	}
	
	@Override
	public boolean isFragmentitems() {
		return fragmentaccordions;
	}
	
	@Override
	public boolean isOfferCardAccordions() {
		return offercardaccordions;
	}
	
	
	@Override
	public ComponentData getData() {
		return accordion.getData();
	}
	
	@Override
	public String[] getExpandedItems() {
		return accordion.getExpandedItems();
	}
	
	@Override
	public String getHeadingElement() {
		return accordion.getHeadingElement();
	}

	@Override
	public String getId() {
		return accordion.getId();
	}
	
	@Override
	public boolean isSingleExpansion() {
		return accordion.isSingleExpansion();
	}

	@Override
	public String getFragmentPath() {
		return fragmentPath;
	}

	@Override
	public ContentFragment getFragmentContent() {
		return contentFragmentModel;
	}
	
	@Override
	public ContentFragment getContentFragmentContent() {
	    return contentFragmentContent;
	}
	
	@Override
	public List<ReactListItem> getFragmentAccordions()  throws LoginException {
		try {
			Resource mainResource = resourceResolverService.getResourceResolver()
					.getResource(request.getResource().getPath());
			Resource childResource = mainResource.getChild("cfaccordions");
			if (childResource != null && childResource.hasChildren()) {
				java.util.List<ReactListItem> listItems = new ArrayList<>();
				for (Resource resource : childResource.getChildren()) {
					LOG.info("XF path....{}", resourceResolverService.getResourceResolver().getResource(
							resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data/master")
							.adaptTo(ValueMap.class));
					ValueMap props = resourceResolverService.getResourceResolver().getResource(resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data/master").adaptTo(ValueMap.class);
					
					if(props.containsKey("title")) {
						String title = props.get("title", String.class);
						String description = props.get("description", String.class);
						listItems.add(new ReactListItemImpl(title, description, true));
					} else if(props.containsKey("fragmentId")) {
					
						Resource accordResource = resourceResolverService.getResourceResolver().getResource(resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data/master");
						Node accordVarNode = accordResource.adaptTo(Node.class);
						ModifiableValueMap accordVarResMap = accordResource.adaptTo(ModifiableValueMap.class);
						LOG.info("accordVarResMap : {}",accordVarResMap);
						PropertyIterator accordPropsItr = accordVarNode.getProperties();
						
						String fragmentId = null;
						String accordionIcon = null;
						String accordionIconAlttxt = null; 
						String title1 = null;
						String title2 = null;
						String titleColor = null;
						String[] bulletPoints = null;
						String bulletIcon = null;
						while (accordPropsItr.hasNext()) {
							Property varProp = accordPropsItr.nextProperty();
							String key = varProp.getName();
							if (key.equalsIgnoreCase("fragmentId")) {
								fragmentId = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("accordionIcon")) {
								accordionIcon = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("accordionIconAlttxt")) {
								accordionIconAlttxt = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("title1")) {
								title1 = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("title2")) {
								title2 = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("titleColor")) {
								titleColor = varProp.getValue().getString();
							} else if (key.equalsIgnoreCase("bulletPoints")) {
								Value[] values= varProp.getValues();
								int index=0;
								bulletPoints = new String[values.length];
								for(Value value : values) {
									bulletPoints[index] = value.getString();
									index++;
								}
							}  else if (key.equalsIgnoreCase("bulletIcon")) {
								bulletIcon = varProp.getValue().getString();
							}  
							
						}
						listItems.add(new ReactListItemImpl(fragmentId, accordionIcon, accordionIconAlttxt, title1, title2, titleColor, bulletPoints, bulletIcon, ""));
					}
				}
				return listItems;
			} else {
				return Collections.emptyList();
			}
		}catch(RepositoryException ex) {
			LOG.error("Exception while parsing the fragment accordions. {}", ex.getMessage());
		}
		return Collections.emptyList();
	}

    @Override
    public List<ListItem> getItems() {
        return accordion.getItems();
    }
    
    @Override
    public Map<String, ? extends ComponentExporter> getExportedItems() {
        return accordion.getExportedItems();
    }

    @Override
    public String[] getExportedItemsOrder() {
        return accordion.getExportedItemsOrder();
    }
	
	@Override
	public String getExportedType() {
	    return ReactAccordionImpl.RESOURCE_TYPE;
	}

	@Override
	public String getAccordionIcon() {
		return accordionIcon;
	}

	@Override
	public boolean isTop() {
		return top;
	}

	@Override
	public boolean isBottom() {
		return bottom;
	}

	@Override
	public boolean isRight() {
		return right;
	}

	@Override
	public boolean isLeft() {
		return left;
	}
	
	@ValueMapValue
	private String lob;
	
	@Override
	public String getLob() {
		return lob;
	}
	
	@ValueMapValue
	private String tag;
	
	@Override
	public String getTag() {
		if(!isManualitems()) {
			return null;
		}
		return tag;
	}
	
	private Boolean updateXFContent() throws LoginException {
		LOG.info("updateXFContent...");
		JsonParser jsonParser = new JsonParser();
		try {
			//if (null != contentFragmentModel) 
			{
				// Session session =
				// resourceResolverService.getResourceResolver().adaptTo(Session.class);
				Iterator<Resource> resources = resourceResolverService.getResourceResolver()
						.getResource(request.getResource().getPath()).getChildren().iterator();// request.getResource().getChildren().iterator();//.get(0).get("contentfragment_1").getPath();
				//while (resources.hasNext()) 
				{
					//Resource resource = resources.next();
					Resource resource = request.getResource();
					LOG.info("resource path : {}", resource.getPath());

					// Resource resource = resolver.getResource("/content/my-app/my-page");
					//try{
					if (resource != null && null!= resource.adaptTo(ValueMap.class).get("fragmentPath")) {
//						ContentFragment node = resource.adaptTo(ContentFragment.class);
						try {
							LOG.info("XF path....{}", resourceResolverService.getResourceResolver().getResource(
									resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data/master")
									.adaptTo(ValueMap.class));
//							ContentFragment cf = modelFactory.getModelFromWrappedRequest(request,
//									resourceResolverService.getResourceResolver()
//											.getResource(resource.adaptTo(ValueMap.class).get("fragmentPath", String.class)),
//									ContentFragment.class);
							
//							Resource cfResource = resourceResolverService.getResourceResolver().getResource(
//									resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data/master");
							String cfVariationName = "";
							if(null== resource.adaptTo(ValueMap.class).get("variationName") || StringUtils.isEmpty(resource.adaptTo(ValueMap.class).get("variationName").toString())) {
								cfVariationName = "master";
							} else {
								cfVariationName = resource.adaptTo(ValueMap.class).get("variationName").toString();
							}
//							if(null != resource.adaptTo(ValueMap.class).get("variationName")) 
							{
//							String cfVariationName = resource.adaptTo(ValueMap.class).get("variationName").toString();
							Iterator<Resource> cardVarResources = resourceResolverService.getResourceResolver().getResource(
									resource.adaptTo(ValueMap.class).get("fragmentPath") + "/jcr:content/data").getChildren().iterator();// request.getResource().getChildren().iterator();//.get(0).get("contentfragment_1").getPath();
							while (cardVarResources.hasNext()) {
								Resource cardVarResource = cardVarResources.next();
								if(null!=cfVariationName && cardVarResource.getPath().endsWith("/"+cfVariationName)) {
									Node cardVarNode = cardVarResource.adaptTo(Node.class);
									ModifiableValueMap cardVarResMap = cardVarResource.adaptTo(ModifiableValueMap.class);
									LOG.info("cardVarResMap : {}",cardVarResMap);
									PropertyIterator cardPropsItr = cardVarNode.getProperties();
									ValueFactory valueFactory = cardVarNode.getSession()
											.getValueFactory();
									while (cardPropsItr.hasNext()) {
										Property varProp = cardPropsItr.nextProperty();
										//String key = varProp.getName();
										//if (!key.equalsIgnoreCase("jcr:mixinTypes")) 
										try {
											// TODO : handle diff format of values
											// without hardcoding the key name
											/*
											 * if (prop.isMultiple()) { Value[] values = prop.getValues(); //
											 * if(prop.getValue() instanceof String) { LOG.info("key value  : {}-{}", key,
											 * values); ValueFactory valueFactory = cfNode.getSession().getValueFactory();
											 * int index = 0; for (Value value : values) { String valueStr =
											 * value.getString(); if (isValidJson(valueStr) || isValidJsonStrict(valueStr))
											 * { JsonObject xfJsonObject = jsonParser.parse(valueStr).getAsJsonObject(); if
											 * (xfJsonObject.has("ruleExpression")) { String ruleExp =
											 * xfJsonObject.get("ruleExpression").getAsString(); final Pattern pattern =
											 * Pattern.compile(regex, Pattern.MULTILINE); final Matcher matcher =
											 * pattern.matcher(ruleExp); Boolean ruleFound = false; if (matcher.find()) {
											 * String matchedRule = matcher.group().toString(); String newRuleName =
											 * matchedRule .replace(this.ruleNameIdentifier, "").replace(".", "")
											 * .replace(")", ""); ruleExp =
											 * ruleBuilderService.getRuleExpression(newRuleName); }
											 * 
											 * LOG.info("ruleExp : {}", ruleExp); xfJsonObject.addProperty("ruleExpression",
											 * ruleExp); value = valueFactory.createValue(xfJsonObject.toString());
											 * values[index] = value; index++; } }
											 * 
											 * } prop.setValue(values); // map.put(key, prop); } else
											 */ if (!varProp.isMultiple() && null != varProp.getValue()
													&& (varProp.getValue().getString()).startsWith("/content/")
													&& ((varProp.getValue().getString()).indexOf("content-fragments") != -1
															|| (varProp.getValue().getString())
																	.indexOf("contentfragments") != -1)) {
												String fragmentPath = varProp.getValue().getString();
												LOG.info("CF fragmentPath : {}", fragmentPath);
												Resource innerCFResource = resourceResolverService.getResourceResolver()
														.getResource(fragmentPath + "/jcr:content/data/master");
												Node innerCFNode = innerCFResource.adaptTo(Node.class);
												ModifiableValueMap innerMap = innerCFResource
														.adaptTo(ModifiableValueMap.class);
												PropertyIterator innerPropertyIte = innerCFNode.getProperties();
												while (innerPropertyIte.hasNext()) {
													Property innerProp = innerPropertyIte.nextProperty();
													String innerKey = innerProp.getName();
													LOG.info("CF innerKey : {}", innerKey);
													if (!innerKey.equalsIgnoreCase(JcrConstants.JCR_MIXINTYPES)) { // TODO : handle
																										// diff
																										// format of values
																										// without
																										// hardcoding
																										// the key name
														if (innerProp.isMultiple()) {
															Value[] values = innerProp.getValues();
															// if(prop.getValue() instanceof String) {
															LOG.info("key value  : {}-{}", innerKey, values);
															int index = 0;
															JsonArray expArr = new JsonArray();
	//														JsonObject expJsonObj = new JsonObject();
															for (Value value : values) {
																try {
																	String valueStr = value.getString();
																	LOG.info("CF valueStr : {}", valueStr);
																	if (isValidJson(valueStr)
																			|| isValidJsonStrict(valueStr)) {
																		JsonObject xfJsonObject = jsonParser.parseString(valueStr)
																				.getAsJsonObject();
																		if (xfJsonObject.has("ruleExpression")) {
																			String ruleExp = xfJsonObject
																					.get("ruleExpression").getAsString();
																			final Pattern pattern = Pattern.compile(regex,
																					Pattern.MULTILINE);
																			final Matcher matcher = pattern
																					.matcher(ruleExp);
																			Boolean ruleFound = false;
																			if (matcher.find()) {
																				String matchedRule = matcher.group()
																						.toString();
																				String newRuleName = matchedRule
																						.replace(this.ruleNameIdentifier,
																								"")
																						.replace(".", "").replace(")", "");
																				ruleExp = ruleBuilderService
																						.getRuleExpression(newRuleName);
																			}
	
																			LOG.info("ruleExp : {}", ruleExp);
																			xfJsonObject.addProperty("ruleExpression",
																					ruleExp);
																			ObjectNode cfObjectNode = getRestultsTrueNode(
																					"text", xfJsonObject.get("displaytext")
																							.getAsString());
																			xfJsonObject.add("resultsTrue", JsonParser
																					.parseString(cfObjectNode.toString()));
																			xfJsonObject.add("resultsFalse",
																					JsonParser.parseString(
																							mapper.createObjectNode()
																									.toString()));
																			LOG.info("xfJsonObject string : {}",
																					xfJsonObject.toString());
																			//if any variable value is a page link, then it should be changed to the shorturl
																			String xfJsonObjStr = xfJsonObject.toString();
																			if(StringUtils.isNotEmpty(xfJsonObjStr) && (xfJsonObjStr.indexOf(CONTEXT_URL)!=-1 || xfJsonObjStr.indexOf(CORPORATE_PAGES_URL)!=1) && xfJsonObjStr.indexOf("content-fragments") == -1
																					&& xfJsonObjStr.indexOf("contentfragments") == -1) {
																				xfJsonObjStr = getShortUrl(xfJsonObjStr);
																				//innerProp.setValue(valueFactory.createValue(innerPropValue));
																				xfJsonObject = jsonParser.parseString(xfJsonObjStr).getAsJsonObject();
																			}
																			LOG.info("after changing the logurl to shorturl if any in xfJsonObject string : {}",
																					xfJsonObject.toString());
																			expArr.add(xfJsonObject);
																			value = valueFactory
																					.createValue(xfJsonObject.toString());
																			values[index] = value;
																			//expJsonObj.add(index+"", xfJsonObject);
																			if(value.getType() == PropertyType.STRING) {
																				checkForTokens(value.getString());
																			}
																			index++;
																		} else {
																			LOG.info("xfJsonObject string : {}",
																					xfJsonObject.toString());
																			//if any variable value is a page link, then it should be changed to the shorturl
																			String xfJsonObjStr = xfJsonObject.toString();
																			if(StringUtils.isNotEmpty(xfJsonObjStr) && (xfJsonObjStr.indexOf(CONTEXT_URL)!=-1 || xfJsonObjStr.indexOf(CORPORATE_PAGES_URL)!=1) && xfJsonObjStr.indexOf("content-fragments") == -1
																					&& xfJsonObjStr.indexOf("contentfragments") == -1) {
																				xfJsonObjStr = getShortUrl(xfJsonObjStr);
																				//innerProp.setValue(valueFactory.createValue(innerPropValue));
																				xfJsonObject = jsonParser.parseString(xfJsonObjStr).getAsJsonObject();
																			}
																			LOG.info("after changing the logurl to shorturl if any in xfJsonObject string : {}",xfJsonObject.toString());
																			expArr.add(xfJsonObject);
																			value = valueFactory
																					.createValue(xfJsonObject.toString());
																			values[index] = value;
																			//expJsonObj.add(index+"", xfJsonObject);
																			if(value.getType() == PropertyType.STRING) {
																				checkForTokens(value.getString());
																			}
																		}
																	}
																} catch (ValueFormatException ex) {
																	LOG.info("Exception while reading each value : {} - {}",
																			ex.getMessage(), ex);
																}
															}
															LOG.info("expArr.toString().... {}", expArr.toString());
															//Value value = valueFactory.createValue(expArr.toString());
															LOG.info("varProp key values are getting updated : {}",
																	varProp.getName());
															// varProp.setValue(value);
															LOG.info("prop key - value is updated as.... {} <<>> {}",
																	varProp.getName(), varProp.getValue().getString());
															LOG.info("before update ...cardVarResMap, prop key_json - json_value is updated as.... {} -- {} <<>> {}",
																	cardVarResMap, (varProp.getName()+"_json"), cardVarResMap.get(varProp.getName()+"_json"));
															cardVarResMap.put(varProp.getName()+"_json", expArr.toString());
															innerMap.put(varProp.getName()+"_json", expArr.toString());
															LOG.info("cardVarResMap key - value is updated as.... {} <<>> {}",
																	(varProp.getName()+"_json"), cardVarResMap.get(varProp.getName()+"_json"));
															/*
															 * cardVarResMap.put(varProp.getName()+"_json1",
															 * expJsonObj.toString()); LOG.
															 * info("cardVarResMap key - value is updated as.... {} <<>> {}"
															 * , (varProp.getName()+"_json1"),
															 * cardVarResMap.get(varProp.getName()+"_json1"));
															 */
															
														} else if(innerProp.getValue().getType() == PropertyType.STRING) {//else if(innerProp.getValue().getType() == PropertyType.STRING || "text/html".equalsIgnoreCase(innerProp.getValue().getType())) {
															
															String innerPropValue = innerProp.getValue().getString();
															//if any variable value is a page link, then it should be changed to the shorturl
															if(StringUtils.isNotEmpty(innerPropValue) && (innerPropValue.indexOf(CONTEXT_URL)!=-1 || innerPropValue.indexOf(CORPORATE_PAGES_URL)!=-1) && innerPropValue.indexOf("content-fragments") == -1
																	&& innerPropValue.indexOf("contentfragments") == -1) {
																innerPropValue = getShortUrl(innerPropValue);
																innerProp.setValue(valueFactory.createValue(innerPropValue));
																innerMap.put(innerProp.getName(), innerPropValue);
															}
															
															if(StringUtils.isNotEmpty(innerPropValue) && innerPropValue.startsWith(this.ruleNameIdentifier)) {
																LOG.info("innerPropValue is having a rule : {}", innerPropValue);
																innerPropValue = getRuleExpression(innerPropValue);
																innerProp.setValue(valueFactory.createValue(innerPropValue));
																innerMap.put(innerProp.getName(), innerPropValue);
															}
															checkForTokens(innerPropValue);
														}
													}
	
												}
											} else {
												if(varProp.isMultiple()) {
													try {
														Value[] values = varProp.getValues();
														int index = 0;
														JsonArray expArr = new JsonArray();
														List<String> bulletStrArr = new ArrayList<String>();
														for(Value value : varProp.getValues()) {
															if(value.getType() == PropertyType.STRING) {
																String varPropValue = value.getString();
																try {
																	//String valueStr = value.getString();
																	LOG.info("CF varPropValue : {}", varPropValue);
																	if (isValidJson(varPropValue)
																			|| isValidJsonStrict(varPropValue)) {
																		JsonObject xfJsonObject = jsonParser.parseString(varPropValue)
																				.getAsJsonObject();
																		if (xfJsonObject.has("ruleExpression")) {
																			String ruleExp = xfJsonObject
																					.get("ruleExpression").getAsString();
																			final Pattern pattern = Pattern.compile(regex,
																					Pattern.MULTILINE);
																			final Matcher matcher = pattern
																					.matcher(ruleExp);
																			Boolean ruleFound = false;
																			if (matcher.find()) {
																				String matchedRule = matcher.group()
																						.toString();
																				String newRuleName = matchedRule
																						.replace(this.ruleNameIdentifier,
																								"")
																						.replace(".", "").replace(")", "");
																				ruleExp = ruleBuilderService
																						.getRuleExpression(newRuleName);
																			}
	
																			LOG.info("ruleExp : {}", ruleExp);
																			xfJsonObject.addProperty("ruleExpression",
																					ruleExp);
																			ObjectNode cfObjectNode = getRestultsTrueNode(
																					"text", xfJsonObject.get("displaytext")
																							.getAsString());
																			xfJsonObject.add("resultsTrue", JsonParser
																					.parseString(cfObjectNode.toString()));
																			xfJsonObject.add("resultsFalse",
																					JsonParser.parseString(
																							mapper.createObjectNode()
																									.toString()));
																			LOG.info("xfJsonObject string : {}",
																					xfJsonObject.toString());
																			//if any variable value is a page link, then it should be changed to the shorturl
																			String xfJsonObjStr = xfJsonObject.toString();
																			if(StringUtils.isNotEmpty(xfJsonObjStr) && (xfJsonObjStr.indexOf(CONTEXT_URL)!=-1 || xfJsonObjStr.indexOf(CORPORATE_PAGES_URL)!=-1) && xfJsonObjStr.indexOf("content-fragments") == -1
																					&& xfJsonObjStr.indexOf("contentfragments") == -1) {
																				xfJsonObjStr = getShortUrl(xfJsonObjStr);
																				//innerProp.setValue(valueFactory.createValue(innerPropValue));
																				xfJsonObject = jsonParser.parseString(xfJsonObjStr).getAsJsonObject();
																			}
																			LOG.info("after changing the logurl to shorturl if any in xfJsonObject string : {}",
																					xfJsonObject.toString());
//																			expArr.add(xfJsonObject);
																			value = valueFactory
																					.createValue(xfJsonObject.toString());
																			values[index] = value;
																			//expJsonObj.add(index+"", xfJsonObject);
																			if(value.getType() == PropertyType.STRING) {
																				checkForTokens(value.getString());
																			}
																			//index++;
																		} else {
																			LOG.info("xfJsonObject string : {}",
																					xfJsonObject.toString());
																			//if any variable value is a page link, then it should be changed to the shorturl
																			String xfJsonObjStr = xfJsonObject.toString();
																			if(StringUtils.isNotEmpty(xfJsonObjStr) && (xfJsonObjStr.indexOf(CONTEXT_URL)!=-1 || xfJsonObjStr.indexOf(CORPORATE_PAGES_URL)!=-1) && xfJsonObjStr.indexOf("content-fragments") == -1
																					&& xfJsonObjStr.indexOf("contentfragments") == -1) {
																				xfJsonObjStr = getShortUrl(xfJsonObjStr);
																				//innerProp.setValue(valueFactory.createValue(innerPropValue));
																				xfJsonObject = jsonParser.parseString(xfJsonObjStr).getAsJsonObject();
																			}
																			LOG.info("after changing the logurl to shorturl if any in xfJsonObject string : {}",xfJsonObject.toString());
//																			expArr.add(xfJsonObject);
																			value = valueFactory
																					.createValue(xfJsonObject.toString());
																			values[index] = value;
																			//expJsonObj.add(index+"", xfJsonObject);
																			if(value.getType() == PropertyType.STRING) {
																				checkForTokens(value.getString());
																			}
																		}
																	}
																}catch (ValueFormatException ex) {
																	LOG.info("Exception while reading each value : {} - {}",
																			ex.getMessage(), ex);
																}
																
																//if any variable value is a page link, then it should be changed to the shorturl
																if(StringUtils.isNotEmpty(varPropValue) && (varPropValue.indexOf(CONTEXT_URL)!=-1 || varPropValue.indexOf(CORPORATE_PAGES_URL)!=-1) && varPropValue.indexOf("content-fragments") == -1
																		&& varPropValue.indexOf("contentfragments") == -1) {
																	varPropValue = getShortUrl(varPropValue);
																	//varProp.setValue(valueFactory.createValue(varPropValue));
																	values[index] = valueFactory.createValue(varPropValue);
																	//cardVarResMap.put(varProp.getName(), varPropValue);
																}
																
																if(StringUtils.isNotEmpty(varPropValue) && varPropValue.startsWith(this.ruleNameIdentifier)) {
																	LOG.info("multi varPropValue is having a rule : {}", varPropValue);
																	varPropValue = getRuleExpression(varPropValue);
																	//varProp.setValue(valueFactory.createValue(varPropValue));
																	//cardVarResMap.put(varProp.getName(), varPropValue);
																	values[index] = valueFactory.createValue(varPropValue);
																}
//																JsonReader jsonReader = new JsonReader(new StringReader(values[index].getString()));
//															    jsonReader.setLenient(true);
//																//expArr.add(gson.fromJson(values[index].getString(), JsonObject.class));
//															    Gson gson1 = new GsonBuilder().setPrettyPrinting().create();
//															    expArr.add(gson1.fromJson(values[index].getString().trim(), JsonObject.class));
																if (isValidJson(values[index].getString())
																		|| isValidJsonStrict(values[index].getString())) {
																	expArr.add(jsonParser.parseString(values[index].getString()).getAsJsonObject());
																} else {
																	expArr.add(values[index].getString());
																	bulletStrArr.add(values[index].getString());
																}
																checkForTokens(varPropValue);
															}
															index++;
														}
														LOG.info("Before update to the accordion cardVarRespMap : expArr.toString().... {}", expArr.toString());
														//varProp.setValue(varProp.getName()+"_json", values[0].getString());
														//if(StringUtils.isNotEmpty(varPropValue) && (varPropValue.indexOf("content-fragments") == -1 || varPropValue.indexOf("contentfragments") == -1))
														try {
															if(varProp.getValues()[0].toString().indexOf("ruleExpression")!=-1) {
																cardVarResMap.put(varProp.getName()+"_json", expArr.toString());
															} else {
																varProp.setValue(values);
																//cardVarResMap.put(varProp.getName()+"_json", bulletStrArr);
																cardVarResMap.put(varProp.getName(), values);
															}
														}catch(RepositoryException e) {
															LOG.error("Exception while updating the multivalue field value : {}", e.getMessage());
														}
														//cardVarResMap.put(varProp.getName(), values);
													}catch(ValueFormatException e) {
														LOG.error("Exception while parsing the multivale field-- doesnt impact the content fragment scenario. {}", e.getMessage());
													}
												} else if(null != varProp.getValue() && (varProp.getValue().getType() == PropertyType.STRING)) {//else if(null != varProp.getValue() && (varProp.getValue().getType() == PropertyType.STRING || "text/html".equalsIgnoreCase(varProp.getValue().getType()))) {
													String varPropValue = varProp.getValue().getString();
													
													//if any variable value is a page link, then it should be changed to the shorturl
													if(StringUtils.isNotEmpty(varPropValue) && (varPropValue.indexOf(CONTEXT_URL)!=-1 || varPropValue.indexOf(CORPORATE_PAGES_URL)!=1) && varPropValue.indexOf("content-fragments") == -1
															&& varPropValue.indexOf("contentfragments") == -1) {
														LOG.info("Not a CF scenario : {}", varPropValue);
														varPropValue = getShortUrl(varPropValue);
														LOG.info("varPropValue testing : {}", varPropValue);
														varProp.setValue(valueFactory.createValue(varPropValue));
														LOG.info("varProp.getValue testing : {}", varProp.getValue());
														cardVarResMap.put(varProp.getName(), varPropValue);
														LOG.info("cardVarResMap.get(varProp.getName()) testing : {}", cardVarResMap.get(varProp.getName()));
													}
													
													if(StringUtils.isNotEmpty(varPropValue) && varPropValue.startsWith(this.ruleNameIdentifier)) {
														LOG.info("varPropValue is having a rule : {}", varPropValue);
														varPropValue = getRuleExpression(varPropValue);
														LOG.info("varPropValue testing 22 : {}", varPropValue);
														varProp.setValue(valueFactory.createValue(varPropValue));
														LOG.info("varProp.getValue testing 22 : {}", varProp.getValue());
														cardVarResMap.put(varProp.getName(), varPropValue);
														LOG.info("cardVarResMap.get(varProp.getName()) testing 22 : {}", cardVarResMap.get(varProp.getName()));
													}
													checkForTokens(varPropValue);
													
												}
											}
										} catch(RepositoryException ee) {
											LOG.info("Exception 333 while parsing resource details...{} - {}", ee.getMessage(), ee);
//											return Boolean.FALSE;
										}
										try {
											LOG.info("cardVarResMap.get(varProp.getName()) testing 22 : {}", varProp.getValue());
											if(null!=varProp && null!=varProp.getValue()) {
												checkForTokens(varProp.getValue().getString());
											}
										}catch(ValueFormatException e) {
											LOG.info("Exception while check for token.. {} - {}", e.getMessage());
										}
									}
									cardVarNode.getSession().save();
									cardVarResource.getResourceResolver().commit();
								}
							}
						}	

						} catch (RepositoryException | org.apache.sling.api.resource.PersistenceException e) {
							LOG.info("Exception2222 while fetching resource details...{} - {}", e.getMessage(), e);
//							return Boolean.FALSE;
						}
					}
					/*
					 * }catch(RepositoryException e) {
					 * LOG.error("Exception while parsing the fragment details : {} - {}",
					 * e.getMessage(), e); }
					 */
				}
				resourceResolverService.getResourceResolver().commit();
			}
		} catch (org.apache.sling.api.resource.PersistenceException e) {
			LOG.error("Exception while reading tokenPropsJson props ...{}", e.getMessage());
			try {
				resourceResolverService.getResourceResolver().commit();
			} catch (PersistenceException | LoginException e1) {
				 LOG.error("Exception while changing the longurls to shorturls or reading tokens. {}", e.getMessage());
			}
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}
	
	
    private boolean isValidJson(String valueStr) {
    	try {
            JsonParser.parseString(valueStr);
            new JSONObject(valueStr);
            LOG.info("isValidJson -- its a valid json....");
        } catch (JsonSyntaxException e) {
        	LOG.error("JsonSyntaxException ... not a valid json....");
        	return false;
        } catch (org.json.JSONException e1) {
    		LOG.error("JSONException ... not a valid json....");
            return false;
        }
        return true;
    }
    
    private boolean isValidJsonStrict(String valueStr) {
    	try {
            strictAdapter.fromJson(valueStr);
            LOG.info("isValidJsonStrict -- its a valid json....");
        } catch (JsonSyntaxException | IOException e) {
        	LOG.error("JsonSyntaxException | IOException ... not a valid json....");
            return false;
        }
        return true;
    }
    
    
    private String getRuleExpression(String propValue) {
		try {
			if (StringUtils.isEmpty(propValue)) {
				return null;
			}

			final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
			final Matcher matcher = pattern.matcher(propValue);
			if (matcher.find()) {
				String matchedRule = matcher.group().toString();
				String newRuleName = matchedRule.replace(this.ruleNameIdentifier, "").replace(".", "").replace(")", "");
				propValue = ruleBuilderService.getRuleExpression(newRuleName);

				return propValue;
			}
		} catch (LoginException e) {
			LOG.error("Exception while fetching rule expression for the rule : {}", propValue);
		}
		return propValue;
    }
    
    public String getTokenProperties() throws LoginException {
    	JsonObject tokenPropsJson = new JsonObject();
    	try {
    		updateXFContent();
	    	//if (null!=contentFragmentModel) 
	    	{
	    		Iterator<Resource> resources = request.getResource().getChildren().iterator();//.get(0).get("contentfragment_1").getPath();
				while (resources.hasNext()) {
					Resource resource = resources.next();
					
					// Resource resource = resolver.getResource("/content/my-app/my-page");
					if (resource != null && null != resource.getPath() && StringUtils.isNotEmpty(resource.getPath())) {
						LOG.info("resource path : {}", resource.getPath());
						Node node = resource.adaptTo(Node.class);
						StringWriter stringWriter = new StringWriter();
						JsonItemWriter jsonWriter = new JsonItemWriter(null);

						jsonWriter.dump(node, stringWriter, -1);
						LOG.info("stringWriter string : {}", stringWriter.toString());
						String jsonStr = stringWriter.toString();
						Gson gson = new Gson();
						JsonObject cfJsonObject1 = gson.fromJson(jsonStr, JsonObject.class);
						String regex = "(?<=\\[\\[)([^\\[\\]]*)(?=\\]\\])"; //"\\[([^\\]\\[\\r\\n]*)\\]";// (RuleName.)\\S+
						//getContentFragmentModel(cfJsonObject1);
						if (cfJsonObject1.has("text")) {
							String text = cfJsonObject1.get("text").getAsString();
							final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
							final Matcher matcher = pattern.matcher(text);
							Boolean ruleFound = false;
							while (matcher.find()) {
								LOG.info("found a matcher ...");
								String matchedToken = matcher.group().toString();
								LOG.info("matched token ...{}", matchedToken);
								if (!tokenList.contains(matchedToken)) {
									tokenList.add(matchedToken);
								}
							}
						}
					}

				} 
	    		
	    		if(tokenList.size()>0) {
	    			Iterator<String> tokens = tokenList.iterator(); 
	    			while(tokens.hasNext()) {
	    				tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, tokens.next());
	    			}
	    		}
	    	}
	    	LOG.info("tokenPropsJson str : {}",tokenPropsJson.toString());
    	} catch (RepositoryException e) {
    		LOG.error("Repository Exception while reading tokenPropsJson props ...{}", e.getMessage());
		} catch (JSONException e) {
			LOG.error("JSON Exception while reading tokenPropsJson props ...{}", e.getMessage());
		} catch (Exception e) {
			LOG.error("Exception while reading tokenPropsJson props ...{}", e.getMessage());
		}
    	
    	return tokenPropsJson.toString();
    }

    private JsonObject getTokenFromPropsBlock(JsonObject tokenPropsJson, String tokenText) {
		LOG.info("before tokenText : {}",tokenText);
		tokenText = tokenText.replaceAll("%5B%5B", "[[").replaceAll("%5D%5D", "]]");
		LOG.info("tokenText : {}",tokenText);
		String regex = "\\[(.*?)\\]";
		try {
			final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
			final Matcher matcher = pattern.matcher(tokenText);
			while (matcher.find()) {
				LOG.info("found a matcher ...");
				String matchedRule = matcher.group().toString();
				String tokenPropName = matchedRule.replace("[", "").replace("]", "").replaceAll("token.", "");
				LOG.info("tokenPropName : {}", tokenPropName);
				String tokenPropsStr = readPropertiesService.getTokenProperties(tokenPropName);
				LOG.info("tokenPropsStr : {}", tokenPropsStr);
				if (StringUtils.isNotEmpty(tokenPropsStr)) {
					tokenPropsJson.addProperty(tokenPropName, tokenPropsStr);
				}
			}
		} catch (LoginException e) {
			LOG.error("Exception while getting the token properties from the properties blocks . {}", e.getMessage());
		} 
		
		return tokenPropsJson;
	}
    
    /**
     * This method checks for the token in the given text and adds to the list
     * @param text
     */
    private void checkForTokens(String text) {
    	try {
	    	String regex = "(?<=\\[\\[)([^\\[\\]]*)(?=\\]\\])"; //"\\[([^\\]\\[\\r\\n]*)\\]";// (RuleName.)\\S+
	    	final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
			final Matcher matcher = pattern.matcher(text);
			Boolean ruleFound = false;
			while (matcher.find()) {
				LOG.info("found a matcher ...");
				String matchedToken = matcher.group().toString();
				LOG.info("matched token ...{}", matchedToken);
				if (!tokenList.contains(matchedToken)) {
					tokenList.add(matchedToken);
				}
			}
    	}catch(Exception e) {
    		LOG.error("Exception while checking for the tokens in the text. {} , {}", e.getMessage(), e);
    	}
    }
    
    
    private ObjectNode getRestultsTrueNode(String pattern, String content) {
    	ObjectNode itemsObj = mapper.createObjectNode();
		ObjectNode rootObj = mapper.createObjectNode();
		ObjectNode items2Obj = mapper.createObjectNode();
		ObjectNode resultsTrueObj = mapper.createObjectNode();
		ArrayNode itemsOrderArrNode = JsonNodeFactory.instance.arrayNode();
		items2Obj.put(":type", "cox-cms-react/components/container");
    	// String content = getBlockdetail();
		if ("text".equalsIgnoreCase(pattern)) {
			items2Obj.set("text", getTextContent(content));
			itemsOrderArrNode.add("text");
		} else if ("button".equalsIgnoreCase(pattern)) {
			items2Obj.set("button", getTextContent(content));
			itemsOrderArrNode.add("button");
		} else if ("image".equalsIgnoreCase(pattern)) {
			items2Obj.set("image", getTextContent(content));
			itemsOrderArrNode.add("image");
		}
		// JsonElement items2Element = new JsonPrimitive(items2JsonObj.toString());

		rootObj.put("layout", "responsiveGrid");
		rootObj.set(":items", items2Obj); // JsonElement
		rootObj.set(":itemsOrder", itemsOrderArrNode);
		
		itemsObj.set("root", rootObj); // JsonElement itemsElement = new

		resultsTrueObj.set(":items", itemsObj);
		resultsTrueObj.set(":itemsOrder", JsonNodeFactory.instance.arrayNode().add("root"));
		
		return resultsTrueObj;
    }

    private ObjectNode getTextContent(String content) {
    	ObjectMapper mapper = new ObjectMapper();
		ObjectNode textObj = mapper.createObjectNode();
    	try {
    		textObj.put("text", content);
    		textObj.put("richText", Boolean.TRUE);
    		textObj.put(":type", "cox-cms-react/components/text");
    	}catch(Exception e) {
    		LOG.error("Exception while getting text content :{}", e.getMessage());
    	}
        return textObj;
    }
    
    private String getShortUrl(String longUrl) throws LoginException {
		if (StringUtils.isNotEmpty(longUrl)) {
			if(longUrl.indexOf("href=") != -1) {
				longUrl = longUrl.indexOf(CONTEXT_URL) != -1 ? longUrl.replace(CONTEXT_URL, "") : longUrl;
				longUrl = longUrl.indexOf(CORPORATE_PAGES_URL) != -1 ? longUrl.replace(CORPORATE_PAGES_URL, "") : longUrl;
				return longUrl;
			}
			
			longUrl = (longUrl.indexOf(CONTEXT_URL) != -1) ? longUrl.replace(CONTEXT_URL, "") : longUrl;
			longUrl = (longUrl.indexOf(CORPORATE_PAGES_URL) != -1) ? (longUrl.replace(CORPORATE_PAGES_URL, "")+".html") : longUrl;
			return longUrl;
		}
		 
		/*
		 * if(null!=externalizerService) { longUrl =
		 * externalizerService.getExternalizerUrl(longUrl); }
		 */
        return longUrl;
    }
    
	
	//to check the path is an external url
	private boolean isExternalUrl(String path) {
		final Pattern pattern = Pattern.compile(EXTERNAL_URL_REG_EXP,
				Pattern.MULTILINE);
		final Matcher matcher = pattern
				.matcher(path);
		if (matcher.find()) {
			return true;
		}
		return false;
	}

	@Override
	public boolean isHideMCP() {
		return isHideMCP;
	}
}
