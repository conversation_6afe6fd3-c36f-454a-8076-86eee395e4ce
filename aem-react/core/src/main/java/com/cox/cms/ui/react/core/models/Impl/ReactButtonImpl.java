package com.cox.cms.ui.react.core.models.Impl;



import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.inject.Inject;

import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Exporter;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.Via;
import org.apache.sling.models.annotations.injectorspecific.Self;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;
import org.apache.sling.models.annotations.via.ResourceSuperType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adobe.cq.export.json.ComponentExporter;
import com.adobe.cq.export.json.ExporterConstants;
import com.adobe.cq.wcm.core.components.commons.link.Link;
import com.adobe.cq.wcm.core.components.models.Button;
import com.cox.cms.ui.react.core.models.ReactButton;
import com.cox.cms.ui.react.core.service.ReactExternalizerService;
import com.cox.cms.ui.react.core.service.ReadPropertiesService;
import com.google.gson.JsonObject;

@Model(
        adaptables = SlingHttpServletRequest.class,
        adapters = {ReactButton.class, ComponentExporter.class}, // Adapts to the CC model interface
        resourceType = ReactButtonImpl.REOURCE_TYPE, // Maps to OUR component, not the CC component
        defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL // No properties? No problem!
)
@Exporter(
        name = ExporterConstants.SLING_MODEL_EXPORTER_NAME,
        extensions = ExporterConstants.SLING_MODEL_EXTENSION
)
public class ReactButtonImpl implements ReactButton
{

    static final String REOURCE_TYPE = "cox-cms-react/components/button";
    
    static final String CONTEXT_URL = "/content/cox";
    static final String CORPORATE_PAGES_URL = "/corporate/pages";
    static final String TELEPHONE_LINK = "tel:";
    
    
    final Logger LOG = LoggerFactory.getLogger(ReactButtonImpl.class);

    @Self
    private SlingHttpServletRequest request;
    
    @Inject
    private ReadPropertiesService readPropertiesService;
    
    @Inject
    private ReactExternalizerService externalizerService;
    
    @Self
    @Via(type = ResourceSuperType.class)
    private Button button;

    @ValueMapValue
    private String linkType;
    
    @ValueMapValue
    private String modalUrl;
    
    @ValueMapValue
    private String modalId;

	@ValueMapValue
    private String offerId;

	@ValueMapValue
    private String dispositionList;
    
    @ValueMapValue
    private String accordionUrl;
    
    @ValueMapValue
    private String accordionId;

    @ValueMapValue
    private String tabUrl;
    
    @ValueMapValue
    private String tabId;

    @ValueMapValue
    private String carouselUrl;
    
    @ValueMapValue
    private String carouselId;
    
    @ValueMapValue
    private String chatText;
    
    @ValueMapValue
    private String pageUrl;
    
    @ValueMapValue
    private String pageId;
    
    private String linkUrl;
    
    private String linkId;
    
    @ValueMapValue
    private boolean openInNewTab;
    
    @ValueMapValue
    private String buttonstates;

    @ValueMapValue
    private String buttontypes;

    @ValueMapValue
    private String anchor;

    @ValueMapValue
    private String simplified;

	@ValueMapValue
    private String navigation;

    @ValueMapValue
    private String lob;

    @ValueMapValue
    private String id;

    @ValueMapValue
    private String alignment;

    @ValueMapValue
    private String activeIcon;

    @ValueMapValue
    private String hoverIcon;

    @ValueMapValue
    private String disabledIcon;

	@ValueMapValue
	private boolean isHideMCP;

	@ValueMapValue
	private boolean setDisposition;


    @Override
    public String getText() {
        return null != button.getText() ? button.getText() : null;
    }

   // @SuppressWarnings("deprecation")
	@Override
    public Link getButtonLink() {
        return null != button.getButtonLink() ? button.getButtonLink() : null;
    }

    @Override
    public String getIcon() {
        return null != button.getIcon() ? button.getIcon() :  null;
    }
    
    @Override
    public String getAccessibilityLabel() {
        return null != button.getAccessibilityLabel() ? button.getAccessibilityLabel() : null;
    }

    @Override
	public String getButtonStates() {
		return this.buttonstates;
	}

    @Override
	public String getButtonTypes() {
		return this.buttontypes;
	}
  

    @Override
	public String getSimplified() {
		return this.simplified;
	}

	@Override
	public String getNavigation() {
		return this.navigation;
	}

    @Override
    public String getLob() {
        return this.lob;
    }

    @Override
	public String getAnchor() {
		return this.anchor;
	}

    @Override
	public String getId() {
		return this.id;
	}

    @Override
    public String getExportedType() {
        return ReactButtonImpl.REOURCE_TYPE;
    }
    
    @Override
	public String getLinkType() {
		return this.linkType;
	}
    
    @Override
	public String getModalUrl() {
		/*
		 * if(StringUtils.isNotEmpty(this.modalUrl)) { this.modalUrl =
		 * this.modalUrl.indexOf(CONTEXT_URL)!=1 ? this.modalUrl.replace(CONTEXT_URL,
		 * "") : this.modalUrl; this.modalUrl =
		 * this.modalUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * this.modalUrl.replace(CORPORATE_PAGES_URL, "") : this.modalUrl; }
		 */
		return this.modalUrl;
	}
    
    @Override
	public String getModalId() {
		return this.modalId;
	}

	@Override
	public String getOfferId() {
		return this.offerId;
	}

	@Override
	public String getDispositionList() {
		return this.dispositionList;
	}
    
    @Override
	public String getAccordionUrl() {
		/*
		 * if(StringUtils.isNotEmpty(this.accordionUrl)) { this.accordionUrl =
		 * this.accordionUrl.indexOf(CONTEXT_URL)!=1 ?
		 * this.accordionUrl.replace(CONTEXT_URL, "") : this.accordionUrl;
		 * this.accordionUrl = this.accordionUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * this.accordionUrl.replace(CORPORATE_PAGES_URL, "") : this.accordionUrl; }
		 */
		return this.accordionUrl;
	}
    
    @Override
	public String getAccordionId() {
		return this.accordionId;
	}
    
    @Override
	public String getTabUrl() {
		/*
		 * if(StringUtils.isNotEmpty(this.tabUrl)) { this.tabUrl =
		 * this.tabUrl.indexOf(CONTEXT_URL)!=1 ? this.tabUrl.replace(CONTEXT_URL, "") :
		 * this.tabUrl; this.tabUrl = this.tabUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * this.tabUrl.replace(CORPORATE_PAGES_URL, "") : this.tabUrl; }
		 */
		return this.tabUrl;
	}
    
    @Override
	public String getTabId() {
		return this.tabId;
	}
    
    @Override
	public String getCarouselUrl() {
		/*
		 * if(StringUtils.isNotEmpty(this.tabUrl)) { this.tabUrl =
		 * this.tabUrl.indexOf(CONTEXT_URL)!=1 ? this.tabUrl.replace(CONTEXT_URL, "") :
		 * this.tabUrl; this.tabUrl = this.tabUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * this.tabUrl.replace(CORPORATE_PAGES_URL, "") : this.tabUrl; }
		 */
		return this.carouselUrl;
	}
    
    @Override
	public String getCarouselId() {
		return this.carouselId;
	}
    
    @Override
	public String getChatText() {
		return this.chatText;
	}
    
    @Override
	public String getPageUrl() {
		/*
		 * if(StringUtils.isNotEmpty(this.pageUrl)) { this.pageUrl =
		 * this.pageUrl.indexOf(CONTEXT_URL)!=1 ? this.pageUrl.replace(CONTEXT_URL, "")
		 * : this.pageUrl; this.pageUrl = this.pageUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * this.pageUrl.replace(CORPORATE_PAGES_URL, "") : this.pageUrl; }
		 */
		return this.pageUrl;
	}
    
    @Override
	public String getPageId() {
		return this.pageId;
	}
    
    @Override
	public String getLinkUrl() throws LoginException {
    	String linkUrl = null;
    	LOG.info("linkType : {}", linkType);
    	if(StringUtils.isNotEmpty(this.linkType)) {
    		switch (linkType) {
	    		case "modal":
	    			linkUrl = modalUrl;
	    			break;
	    		case "accordion":
	    			linkUrl = accordionUrl;
	    			break;
	    		case "page":
	    			linkUrl = pageUrl;
	    			break;
	    		case "tab":
	    			linkUrl = tabUrl;
	    			break;
				case "carousel":
	    			linkUrl = carouselUrl;
	    			break;
	    		default : 
	    			linkUrl = null;
    		}
    	}
		/*
		 * if(StringUtils.isNotEmpty(linkUrl)) { linkUrl =
		 * linkUrl.indexOf(CONTEXT_URL)!=1 ? linkUrl.replace(CONTEXT_URL, "") : linkUrl;
		 * linkUrl = linkUrl.indexOf(CORPORATE_PAGES_URL)!=1 ?
		 * linkUrl.replace(CORPORATE_PAGES_URL, "") : linkUrl; }
		 */
    	if(StringUtils.isNotEmpty(linkUrl) && null!=externalizerService && linkUrl.indexOf(TELEPHONE_LINK)==-1) {
    		linkUrl = externalizerService.getExternalizerUrl(linkUrl);
    	}
    	
    	this.linkUrl = linkUrl;
    	LOG.info("this.linkUrl : {}", this.linkUrl);
		return linkUrl;
	}
    
    @Override
	public String getLinkId() {
    	String linkId = null;
    	if(StringUtils.isNotEmpty(this.linkType)) {
    		switch (linkType) {
	    		case "modal":
	    			linkId = modalId;
	    			break;
	    		case "accordion":
	    			linkId = accordionId;
	    			break;
	    		case "page":
	    			linkId = pageId;
	    			break;
	    		case "tab":
	    			linkId = tabId;
	    			break;
				case "carousel":
	    			linkId = carouselId;
	    			break;
	    		default : 
	    			linkId = null;
    		}
    	}
    	this.linkId = linkId;
		return linkId;
	}

	@Override
    public boolean isOpenInNewTab() {
    	return openInNewTab;
    }
    
 //   @Override
	//public boolean isOpenInNewTab() {
    //	if(Strings.isNullOrEmpty(this.linkType) || "page"!=this.linkType) {
    //		return false;
    //	}
	//	return this.openInNewTab;
	//}

	public String getTokenProperties() {
		JsonObject tokenPropsJson = new JsonObject();
		LOG.info("button.getText() : {}", button.getText());
		if (StringUtils.isNotEmpty(button.getText()) && button.getText().indexOf("token.") != -1) {
			tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, button.getText());
		}
		
		if(StringUtils.isNotEmpty(this.anchor) && this.anchor.indexOf("token.") != -1) {
			tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, this.anchor);
		}
		
		if(StringUtils.isNotEmpty(button.getIcon()) && button.getIcon().indexOf("token.") != -1) {
			tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, button.getIcon());
		}
		
		if(StringUtils.isNotEmpty(linkUrl) && linkUrl.indexOf("token.") != -1) {
			tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, this.linkUrl);
		}
		
		if(StringUtils.isNotEmpty(linkId) && linkId.indexOf("token.") != -1) {
			tokenPropsJson = getTokenFromPropsBlock(tokenPropsJson, this.linkId);
		}
		return tokenPropsJson.toString();
	}
	
	private JsonObject getTokenFromPropsBlock(JsonObject tokenPropsJson, String tokenText) {
		LOG.info("before tokenText : {}",tokenText);
		tokenText = tokenText.replaceAll("%5B%5B", "[[").replaceAll("%5D%5D", "]]");
		LOG.info("tokenText : {}",tokenText);
		String regex = "\\[(.*?)\\]";
		try {
			final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
			final Matcher matcher = pattern.matcher(tokenText);
			while (matcher.find()) {
				LOG.info("found a matcher ...");
				String matchedRule = matcher.group().toString();
				String tokenPropName = matchedRule.replace("[", "").replace("]", "").replaceAll("token.", "");
				LOG.info("tokenPropName : {}", tokenPropName);
				String tokenPropsStr = readPropertiesService.getTokenProperties(tokenPropName);
				LOG.info("tokenPropsStr : {}", tokenPropsStr);
				if (StringUtils.isNotEmpty(tokenPropsStr)) {
					tokenPropsJson.addProperty(tokenPropName, tokenPropsStr);
				}
			}
		} catch (LoginException e) {
			LOG.error("Exception while getting the token properties from the properties blocks . {}", e.getMessage());
		} 
		
		return tokenPropsJson;
	}

	@Override
	public String getAlignment() {
		return alignment;
	}

	@Override
	public String getActiveIcon() {
		return activeIcon;
	}

	@Override
	public String getDisabledIcon() {
		return disabledIcon;
	}

	@Override
	public String getHoverIcon() {
		return hoverIcon;
	}

	@Override
	public boolean isHideMCP() {
		return isHideMCP;
	}

	@Override
	public boolean isSetDisposition() {
		return setDisposition;
	}
}
