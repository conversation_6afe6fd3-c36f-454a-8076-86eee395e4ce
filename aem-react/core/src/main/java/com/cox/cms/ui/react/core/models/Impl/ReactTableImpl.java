package com.cox.cms.ui.react.core.models.Impl;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.cox.cms.ui.react.core.service.ReadPropertiesService;
import org.apache.commons.lang3.StringUtils;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Exporter;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.injectorspecific.Self;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adobe.cq.export.json.ComponentExporter;
import com.adobe.cq.export.json.ExporterConstants;
import com.adobe.cq.wcm.style.ComponentStyleInfo;
import com.cox.cms.ui.react.core.models.ReactTable;
import com.google.gson.JsonObject;

import javax.inject.Inject;


/**
 * React Table
 * <AUTHOR>
 */

@Model(
        adaptables = SlingHttpServletRequest.class,
        adapters = {ReactTable.class, ComponentExporter.class},
        resourceType = ReactTableImpl.RESOURCE_TYPE,
        defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL
)
@Exporter(
        name = ExporterConstants.SLING_MODEL_EXPORTER_NAME,
        extensions = ExporterConstants.SLING_MODEL_EXTENSION
)
public class ReactTableImpl implements ReactTable {

    static final String RESOURCE_TYPE = "cox-cms-react/components/react-rte-table/reacttable";
    
    static final String CONTEXT_URL = "/content/cox";
	static final String CORPORATE_PAGES_URL = "/corporate/pages";
	
    final Logger LOG = LoggerFactory.getLogger(ReactTableImpl.class);

    
    @ValueMapValue
    private String tableid;

    @ValueMapValue
    private String text;

    @ValueMapValue
    private String id;

    @ValueMapValue
    private String buttoncollapse;
    
    @ValueMapValue
    private String buttonexpand;

    @ValueMapValue
    private String isSecondary;

    @ValueMapValue
	private boolean spaceBetweenSections;

    @ValueMapValue(name = "cq:styleIds")
    private String[] cqStyleIds;

	@ValueMapValue
	private boolean top;

	@ValueMapValue
	private boolean bottom;

	@ValueMapValue
	private boolean right;

	@ValueMapValue
	private boolean left;

    @ValueMapValue
	private String lob;

    @ValueMapValue
	private String tableVariation;

    @ValueMapValue
	private String alignment;

    @ValueMapValue
	private String headerTheme;

	@Inject
	private ReadPropertiesService readPropertiesService;
    
	
	@Override 
	public String getButtonExpand() { 
		return buttonexpand; 
	}

    @Override 
	public String getButtonCollapse() { 
		return buttoncollapse; 
	}
	 
    @Override
    public String getTableId() {
        return tableid;
    }

    @Override
    public String getText() {
    	try {
			if (StringUtils.isNotEmpty(text) && text.indexOf("href=") != -1) {

				text = text.indexOf(CONTEXT_URL) != -1 ? text.replace(CONTEXT_URL, "") : text;
				text = text.indexOf(CORPORATE_PAGES_URL) != -1 ? text.replace(CORPORATE_PAGES_URL, "") : text;
			}
		} catch (Exception e) {
			LOG.error("Exception while shortening url for the text : {}", e.getMessage());
		}
        return text;
    }

    @Override
    public String getID() {
        return id;
    }
    @Override
    public String getIsSecondary() {
        return isSecondary;
    }
    
    @Override
    public String[] getCqStyleIds() {
    	return (cqStyleIds != null) ? Arrays.copyOf(cqStyleIds, cqStyleIds.length) : null;
    }
    @Self
    private SlingHttpServletRequest request;

     public String getAppliedCssDetails() {
        JsonObject appliedCssClassNamesJson = new JsonObject();
        appliedCssClassNamesJson.addProperty("appliedCssClassNames", this.request.getResource().adaptTo(ComponentStyleInfo.class).getAppliedCssClasses());
        return appliedCssClassNamesJson.toString();
    }
     
    @Override
	public boolean isSpaceBetweenSections() {
		return spaceBetweenSections;
	}

	@Override
	public boolean isTop() {
		return top;
	}

	@Override
	public boolean isBottom() {
		return bottom;
	}

	@Override
	public boolean isRight() {
		return right;
	}

	@Override
	public boolean isLeft() {
		return left;
	}
   
    @Override
    public String getExportedType() {
        return ReactTableImpl.RESOURCE_TYPE;
    }
    
    @Override
	public String getLob() {
		return lob;
	}

    @Override
	public String getTableVariation() {
		return tableVariation;
	}

    @Override
	public String getAlignment() {
		return alignment;
	}

    @Override
	public String getHeaderTheme() {
		return headerTheme;
	}

	public String getTokenProperties() {
		JsonObject tokenPropsJson = new JsonObject();
		try {
			LOG.info("Text :{}", text);
			if (StringUtils.isNotEmpty(text) && text.indexOf("token.") != -1) {
				String regex = "\\[(.*?)\\]";

				final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
				final Matcher matcher = pattern.matcher(text);
				Boolean ruleFound = false;
				while (matcher.find()) {
					LOG.info("found a matcher ...");
					String matchedRule = matcher.group().toString();
					String tokenPropName = matchedRule.replace("[", "").replace("]", "").replaceAll("token.", "");
					LOG.info("tokenPropName : {}", tokenPropName);
					String tokenPropsStr = readPropertiesService.getTokenProperties(tokenPropName);
					if (StringUtils.isNotEmpty(tokenPropsStr)) {
						tokenPropsJson.addProperty(tokenPropName, tokenPropsStr);
					}
				}
			}
		} catch (LoginException e) {
			LOG.error("Exception while getting the token properties. {}", e.getMessage());
		}
		return tokenPropsJson.toString();
	}



}