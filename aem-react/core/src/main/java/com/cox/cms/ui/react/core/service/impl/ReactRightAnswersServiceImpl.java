package com.cox.cms.ui.react.core.service.impl;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.jcr.AccessDeniedException;
import javax.jcr.InvalidItemStateException;
import javax.jcr.ItemExistsException;
import javax.jcr.Node;
import javax.jcr.NodeIterator;
import javax.jcr.PathNotFoundException;
import javax.jcr.ReferentialIntegrityException;
import javax.jcr.RepositoryException;
import javax.jcr.Session;
import javax.jcr.ValueFormatException;
import javax.jcr.lock.LockException;
import javax.jcr.nodetype.ConstraintViolationException;
import javax.jcr.nodetype.NoSuchNodeTypeException;
import javax.jcr.query.InvalidQueryException;
import javax.jcr.query.Query;
import javax.jcr.query.QueryManager;
import javax.jcr.query.QueryResult;
import javax.jcr.version.VersionException;

import org.apache.commons.lang3.StringUtils;
//import org.apache.felix.scr.annotations.PropertyUnbounded;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.api.resource.ModifiableValueMap;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ResourceResolverFactory;
import org.apache.sling.api.resource.ValueMap;
import org.apache.sling.distribution.DistributionRequest;
import org.apache.sling.distribution.DistributionRequestType;
import org.apache.sling.distribution.DistributionResponse;
import org.apache.sling.distribution.Distributor;
import org.apache.sling.distribution.SimpleDistributionRequest;
import org.apache.sling.event.jobs.JobManager;
import org.json.JSONException;
import org.json.JSONObject;
import org.osgi.framework.Constants;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Modified;
import org.osgi.service.component.annotations.Reference;
import org.osgi.service.metatype.annotations.AttributeDefinition;
import org.osgi.service.metatype.annotations.Designate;
import org.osgi.service.metatype.annotations.ObjectClassDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cox.cms.ui.react.core.service.ReactRightAnswersService;
import com.cox.cms.ui.react.core.service.ResourceResolverService;
import com.cox.cms.ui.react.core.service.impl.rightanswers.ReactRightAnswersArticleUtil;

import com.cox.cms.ui.react.core.service.rightanswers.Article;
import com.cox.cms.ui.react.core.service.rightanswers.ArticleLocations;
import com.cox.cms.ui.react.core.service.rightanswers.Articleinfo;
import com.cox.cms.ui.react.core.service.rightanswers.Attribute;
import com.cox.cms.ui.react.core.service.rightanswers.RaResult;
import com.cox.cms.ui.react.core.service.rightanswers.RightAnswersApiInfo;
import com.cox.cms.ui.react.core.service.rightanswers.Solution;
import com.cox.cms.ui.react.core.service.rightanswers.SolutionArticle;
//import com.cox.aem.common.utils.CoxUtils;
import com.cox.cms.ui.react.core.utils.Base64Utils;
//import com.adobe.acs.commons.replication.dispatcher.DispatcherFlusher;
//import com.cox.aem.common.coxException.CoxException;
import com.cox.cms.ui.react.core.utils.CoxConstants;
import com.day.cq.commons.Externalizer;
import com.day.cq.commons.jcr.JcrConstants;
import com.day.cq.mcm.emailprovider.EmailService;
import com.day.cq.replication.ReplicationActionType;
import com.day.cq.replication.ReplicationException;
import com.day.cq.replication.ReplicationOptions;
import com.day.cq.replication.ReplicationStatus;
import com.day.cq.replication.ReplicationStatusProvider;
import com.day.cq.replication.Replicator;
import com.day.cq.wcm.api.NameConstants;
import com.day.cq.wcm.api.Page;
import com.day.cq.wcm.api.PageManager;
import com.day.cq.wcm.api.WCMException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
//import org.codehaus.jackson.map.ObjectMapper;
//import org.codehaus.jackson.map.SerializationConfig.Feature;
import com.google.common.collect.Lists;
//import com.rightanswers.portal.ApiUser;
//import com.rightanswers.portal.Portal;

/**
 * 
 * <AUTHOR>
 *
 */

@Component(
        immediate = true,
        service = ReactRightAnswersService.class,
        property = {
                Constants.SERVICE_ID + "= COX React Right Answers Service",
                Constants.SERVICE_DESCRIPTION + "= COX React Right Answers Service"
        }
)
@Designate(ocd = ReactRightAnswersServiceImpl.ReactRightAnswersConfig.class)
public class ReactRightAnswersServiceImpl implements ReactRightAnswersService {
	
		private static final Logger LOG = LoggerFactory.getLogger(ReactRightAnswersServiceImpl.class);

		private static final String DEFAULT_KEYCLOUD_EXT_APIGATEWAY  = "https://api-load.cox.com";
		private static final String KEYCLOUD_EXT_APIGATEWAY  = "ra.keycloudexternalapigateway";

		private static final String DEFAULT_KEYCLOUD_OAUTH2_PATH  = "/auth/oauth/v2/token?grant_type=client_credentials";
		private static final String KEYCLOUD_OAUTH2_PATH  = "ra.keycloudexternalapigatewayoauthpath";

		private static final String DEFAULT_KEYCLOUD_PATH  = "/key/knowledgebase/v1/";
		private static final String KEYCLOUD_PATH  = "ra.keycloudpath";

		private static final String DEFAULT_APIKEY  = "donotrun";
		private static final String APIKEY  = "ra.apikey";

		private static final String DEFAULT_CLIENTID  = "donotrun";
		private static final String CLIENTID  = "ra.clientid";

		private static final String DEFAULT_PATH  = "/portal/api/rest/";
		private static final String PATH  = "ra.path";

		private static final String DEFAULT_USERNAME  = "donotrun";
		private static final String DEFAULT_EXT_APIGATEWAY_CLIENTID  = "AEMUSER";
		private static final String USERNAME  = "ra.username";

		private static final String CHUNK_SIZE_RA  = "ra.chunksize";
		private static final int CHUNK_SIZE_RA_VALUE  = 100;
		
		private static final boolean REDIRECTS_UPDATE_ENABLED = true;

		private static final String EXT_APIGATEWAY_CLIENTID  = "ra.keycloudextapigatewayoauthclientid";
		private static final String EXT_APIGATEWAY_CLIENT_SECRET  = "ra.keycloudextapigatewayoauthclientsecret";
		
		private static final String DEFAULT_REACT_RA_SUPPORT_PAGES_PATH  = "content/cox/residential/corporate/pages";

		@SuppressWarnings("squid:S2068")
		private static final String DEFAULT_PASSWORD  = "donotrun";
		@SuppressWarnings("squid:S2068")
		private static final String PASSWORD  = "ra.password";

		private static final String RA_RESIDENTIAL_PATH  = "ra.residential.path";
		private static final String RA_RESIDENTIAL_DEFAULT_PATH  = "/content/cox/residential/corporate/pages/aeminternalsecurepages/default_page";
		
		private static final String RA_RESIDENTIAL_REDIRECT_PATH  = "ra.residential.redirect";
		private static final String RA_RESIDENTIAL_REDIRECT_DEFAULT_PATH  = "/content/cox/application/si/resourcebundles/residential_support_redirect/jcr:content";
		
		private static final String RA_BUSINESS_PATH  = "ra.business.path";
		private static final String RA_BUSINESS_DEFAULT_PATH  = "/content/cox/business/corporate/pages/aeminternalsecurepages/default_page";
		
		private static final String RA_BUSINESS_REDIRECT_PATH  = "ra.business.redirect";
		private static final String RA_BUSINESS_REDIRECT_DEFAULT_PATH  = "/content/cox/application/si/resourcebundles/business_support_redirect/jcr:content";
		
		private static final String REATTEMPT  = "ra.rettempt";
		private static final String DEFAULT_REATTEMPT  = "1";
		
		private static final String SPECIALCHARS  = "ra.specialchars";
		private static final String DEFAULT_SPECIALCHARS = "[.,'#()\\/ï¿½ï¿½]";
		
		private static final String RA_CSS_CONFIG  = "ra.css.path";
		
		private static final String TEMPLATETYPE  = "ra.template.type";
		private static final String DEFAULT_TEMPLATETYPE  = "react";
		
		/** The Constant DISTRIBUTION_LIST. */
		private static final String DISTRIBUTION_LIST = "ra.distributionlist";
		
		/** The Constant DEFAULT_WEBAPI_URL. */
		private static final String[] DEFAULT_DISTRIBUTION_LIST = {"<EMAIL>"};
		
		
		private ArrayList<Articleinfo> articleinfo  	= new ArrayList<>();
		private ArrayList<Articleinfo> articleinfodelta = new ArrayList<>();
		
		Map<String, String> aemarticles = new HashMap<String, String>();
		Map<String, String> deletearticles = new HashMap<String, String>();
		Map<String, String> modifiedarticles = new HashMap<String, String>();
		Map<String, String> newarticles = new HashMap<String, String>();
		Map<String, ArrayList<String>> aemralocations = new HashMap<String, ArrayList<String>>();
		Map<String, ArticleLocations> articleLocationMap = new HashMap<String, ArticleLocations>();
		Set<String> createdArticlePath = new HashSet<>();
		Set<String> deleteArticlePaths = new HashSet<>();
//
//		private Portal port;
//		private ApiUser testingUser = new ApiUser();
		int		totalhits = 0;
		
		private String keycloudextapigateway = KEYCLOUD_EXT_APIGATEWAY;

		private String keycloudpath = KEYCLOUD_PATH;
	
		private String keycloudoauth2path = KEYCLOUD_OAUTH2_PATH;
		
		private String apikey = APIKEY;

		private String clientid = CLIENTID;

		private String path = PATH;

		private String username = USERNAME;

		private String pswd = PASSWORD;
		
		private String extapigatewayclientid = EXT_APIGATEWAY_CLIENTID;
		
		private String extapigatewayclientsecret = EXT_APIGATEWAY_CLIENT_SECRET;
	
		public static final String KEYCLOUDFLAG= "ra.hitkeycloudendpoint";

		private boolean keycloudflag;

		private String raResidentialDefaultPath = RA_RESIDENTIAL_PATH;
		
		private String raResidentialRedirectDefaultPath = RA_RESIDENTIAL_REDIRECT_PATH;

		private String raBusinessDefaultPath = RA_BUSINESS_PATH;
		
		private String raBusinessRedirectDefaultPath = RA_BUSINESS_REDIRECT_PATH;
		
		private String reattempt = REATTEMPT;
		
		private String csspath = RA_CSS_CONFIG;
		
		private String regex = SPECIALCHARS;
		
		private String templateType = TEMPLATETYPE;
		
		private String reactRASupportPagesPath = "";
		private String raArticleSleepTime = "";
		
		
		//TODO : Need to check this issue later
		public String[] distributionlist = DEFAULT_DISTRIBUTION_LIST;
		  
		private int chunkSizeRa = CHUNK_SIZE_RA_VALUE;
		
		private boolean isAddrewrite = REDIRECTS_UPDATE_ENABLED;		
		
		@Reference
		private ResourceResolverFactory resolverFactory;
		
		//@Reference
		//private RightAnswersArticleUtil raUtil = new RightAnswersArticleUtil();
		
//		@Reference
	    private ResourceResolverService resourceResolverService;
		
		@Reference
		Replicator replicator;

		@Reference
		ReplicationStatusProvider replicationStatusProvider;
		
//		@Reference
//	    private DispatcherFlusher dispatcherFlusher;

		@Reference
		private Distributor distributor;
		
//		@Inject
//	    private EmailService emailService;

		@Reference
		private transient JobManager jobManager;
		private static final String COLLECTIONSTEXT="collections";
		private static final String STATUSES_TEXT="statuses";
		private static final String SEARCHTEXT="search";
		private static final String UTF8TEXT="UTF-8";
		private static final String AUTHORIZATION="Authorization";
		private static final String BASIC="Basic ";
		private static final String BEARER="Bearer "; 
		private static final String CLIENT_ID="clientId";
		private static final String APP_INTERFACE="appInterface";
		private static final String COMPANY_CODE="companyCode";
		private static final String HTTPS="https";
		private static final String JCR_SQL2="JCR-SQL2";
		private static final String JCR_CONTENT="/jcr:content";
		private static final String DATE_FORMAT="MM/dd/yyyy";
		private static final String DOT_HTML=".html";
		private static final String SUPPORT="/support/";
		private static final String API_KEY="apikey";
		private static final String agentName = "publish";
		private static final String MASTER = "/master";
		
		@ObjectClassDefinition (
	            name = "COX React Right Answers Service Configuration",
	            description = "COX React Right Answers Service Configuration"
	    )
		@interface ReactRightAnswersConfig {
			@AttributeDefinition (
	                name = "cloud api gateway",
	                description = "Cloud api gateway"
	        )
	        String getKeycloudextapigateway() default DEFAULT_KEYCLOUD_EXT_APIGATEWAY;
			
			@AttributeDefinition (
	                name = "Cloud auth api path",
	                description = "Cloud auth api path"
	        )
	        String getKeycloudoauth2path() default DEFAULT_KEYCLOUD_OAUTH2_PATH;

	        @AttributeDefinition(
	                name = "External API gateway client id",
	                description = "External API gateway client id"
	        )
	        String getExtapigatewayclientid() default DEFAULT_EXT_APIGATEWAY_CLIENTID;
	        
	        @AttributeDefinition (
	                name = "External API gateway client secret",
	                description = "External API gateway client secret"
	        )
	        String getExtapigatewayclientsecret() default DEFAULT_PASSWORD;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getKeycloudpath() default DEFAULT_KEYCLOUD_PATH;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getApikey() default DEFAULT_APIKEY;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getClientid() default DEFAULT_CLIENTID;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getPath() default DEFAULT_PATH;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getRaResidentialDefaultPath() default RA_RESIDENTIAL_PATH;
	        
	        @AttributeDefinition (
	                name = "Cloud path",
	                description = "Cloud path"
	        )
	        String getRaResidentialRedirectDefaultPath() default RA_RESIDENTIAL_REDIRECT_PATH;
	        
	        @AttributeDefinition (
	                name = "Distribution list",
	                description = "Distribution list"
	        )
	        String[] getDistributionlist() default "";
	        
	        @AttributeDefinition (
	                name = "Defaulkt chunk size",
	                description = "chunk size"
	        )
	        int getChunkSizeRa() default CHUNK_SIZE_RA_VALUE;
	        
	        @AttributeDefinition (
	                name = "Redirects updates enabled",
	                description = "Redirects updates enabled"
	        )
	        boolean getRedirectsUpdateEnabled() default REDIRECTS_UPDATE_ENABLED;
	        
	        @AttributeDefinition (
	                name = "Distribution list",
	                description = "Distribution list"
	        )
	        String getCsspath() default RA_CSS_CONFIG;
	        
	        @AttributeDefinition (
	                name = "Distribution list",
	                description = "Distribution list"
	        )
	        String getRegex() default DEFAULT_SPECIALCHARS;
	        
	        @AttributeDefinition (
	                name = "Distribution list",
	                description = "Distribution list"
	        )
	        String getUsername() default DEFAULT_USERNAME;
	        
	        @AttributeDefinition (
	                name = "Distribution list",
	                description = "Distribution list"
	        )
	        String getPassword() default DEFAULT_PASSWORD;
	        
	        @AttributeDefinition (
	                name = "React RA articles support pages path",
	                description = "React RA articles support pages path"
	        )
	        String getReactSupportPagesPath() default DEFAULT_REACT_RA_SUPPORT_PAGES_PATH;
	        
	        @AttributeDefinition (
	                name = "React RA articles support pages creation sleep time",
	                description = "React RA articles support pages creation sleep time"
	        )
	        String getSleepTime() default "60000";
	    }

/*		@Activate
		protected void activate(final Map<String, String> config){
				LOG.debug("RA Feed OSGI Service - Initializing");
				this.keycloudextapigateway = PropertiesUtil.toString(config.get(KEYCLOUD_EXT_APIGATEWAY), DEFAULT_KEYCLOUD_EXT_APIGATEWAY);
				this.keycloudpath = PropertiesUtil.toString(config.get(KEYCLOUD_PATH), DEFAULT_KEYCLOUD_PATH);
				this.keycloudoauth2path = PropertiesUtil.toString(config.get(KEYCLOUD_OAUTH2_PATH), DEFAULT_KEYCLOUD_OAUTH2_PATH);
				this.apikey = PropertiesUtil.toString(config.get(APIKEY), DEFAULT_APIKEY);
				this.clientid = PropertiesUtil.toString(config.get(CLIENTID), DEFAULT_CLIENTID);
				this.path = PropertiesUtil.toString(config.get(PATH), DEFAULT_PATH);
				this.username = PropertiesUtil.toString(config.get(USERNAME), DEFAULT_USERNAME);
				this.password = PropertiesUtil.toString(config.get(PASSWORD), DEFAULT_PASSWORD);

				this.extapigatewayclientid = PropertiesUtil.toString(config.get(EXT_APIGATEWAY_CLIENTID), DEFAULT_EXT_APIGATEWAY_CLIENTID);
				this.extapigatewayclientsecret = PropertiesUtil.toString(config.get(EXT_APIGATEWAY_CLIENT_SECRET), DEFAULT_PASSWORD);
				
				this.keycloudflag = PropertiesUtil.toBoolean(config.get(KEYCLOUDFLAG), true);
				this.raResidentialDefaultPath = PropertiesUtil.toString(config.get(RA_RESIDENTIAL_PATH), RA_RESIDENTIAL_DEFAULT_PATH);
				this.raResidentialRedirectDefaultPath = PropertiesUtil.toString(config.get(RA_RESIDENTIAL_REDIRECT_PATH), RA_RESIDENTIAL_REDIRECT_DEFAULT_PATH);
				
				this.raBusinessDefaultPath = PropertiesUtil.toString(config.get(RA_BUSINESS_PATH), RA_BUSINESS_DEFAULT_PATH);
				this.raBusinessRedirectDefaultPath = PropertiesUtil.toString(config.get(RA_BUSINESS_REDIRECT_PATH), RA_BUSINESS_REDIRECT_DEFAULT_PATH);
				
				this.reattempt = PropertiesUtil.toString(config.get(REATTEMPT), DEFAULT_REATTEMPT);
				this.csspath = PropertiesUtil.toString(config.get(RA_CSS_CONFIG), CoxConstants.RA_CSS_PATH);
				this.regex = PropertiesUtil.toString(config.get(SPECIALCHARS), DEFAULT_SPECIALCHARS);
				this.templateType = PropertiesUtil.toString(config.get(TEMPLATETYPE), DEFAULT_TEMPLATETYPE);
				this.distributionlist = PropertiesUtil.toStringArray(config.get(DISTRIBUTION_LIST));
				this.chunkSizeRa=PropertiesUtil.toInteger(config.get(CHUNK_SIZE_RA),CHUNK_SIZE_RA_VALUE);
				this.isAddrewrite = PropertiesUtil.toBoolean(config.get(""), false);
			
		}
*/
		
		@Activate
		@Modified
		protected void activate(ReactRightAnswersConfig config){
				LOG.debug("RA Feed OSGI Service - Initializing");
				this.keycloudextapigateway = config.getKeycloudextapigateway();
				this.keycloudpath = config.getKeycloudpath();
				this.keycloudoauth2path = config.getKeycloudoauth2path();
				this.apikey = config.getApikey();
				this.clientid = config.getClientid();
				this.path = config.getPath();
				this.username = "";//PropertiesUtil.toString(config.get(USERNAME), DEFAULT_USERNAME);
				this.pswd = "";//PropertiesUtil.toString(config.get(PASSWORD), DEFAULT_PASSWORD);
				this.reactRASupportPagesPath = config.getReactSupportPagesPath();
				
				this.extapigatewayclientid = config.getExtapigatewayclientid();
				this.extapigatewayclientsecret = config.getExtapigatewayclientsecret();
				
				this.keycloudflag = true; //PropertiesUtil.toBoolean(config.get(KEYCLOUDFLAG), true);
				this.raResidentialDefaultPath = config.getRaResidentialDefaultPath();
				this.raResidentialRedirectDefaultPath = config.getRaResidentialRedirectDefaultPath();
				this.raArticleSleepTime = config.getSleepTime();
				
				//this.raBusinessDefaultPath = PropertiesUtil.toString(config.get(RA_BUSINESS_PATH), RA_BUSINESS_DEFAULT_PATH);
				//this.raBusinessRedirectDefaultPath = PropertiesUtil.toString(config.get(RA_BUSINESS_REDIRECT_PATH), RA_BUSINESS_REDIRECT_DEFAULT_PATH);
				
				this.reattempt = DEFAULT_REATTEMPT;//PropertiesUtil.toString(config.get(REATTEMPT), DEFAULT_REATTEMPT);
				this.csspath = config.getCsspath();
				this.regex = config.getRegex();
				this.templateType = DEFAULT_TEMPLATETYPE;//PropertiesUtil.toString(config.get(TEMPLATETYPE), DEFAULT_TEMPLATETYPE);
				this.distributionlist = config.getDistributionlist();
				this.chunkSizeRa= CHUNK_SIZE_RA_VALUE;//PropertiesUtil.toInteger(config.get(CHUNK_SIZE_RA),CHUNK_SIZE_RA_VALUE);
				this.isAddrewrite = config.getRedirectsUpdateEnabled();
		
				resourceResolverService = new ResourceResolverServiceImpl();
		}
		
		/*
		 * Fetch Articles per Line of Business from RA Portal
		 * @getArticles()
		 */
		public boolean getArticles(String lob) throws MalformedURLException {
				String endpointLoc=keycloudextapigateway;
				LOG.info("Right Answers - GetTotalResults for {} : Endpoint used {} ", lob,endpointLoc);
				ArrayList<String> failureLists = new ArrayList<String>();
				String articleId = "";
				long processingStartTime = System.currentTimeMillis();
				StringBuilder notifymessage = new StringBuilder(); 
				boolean sendemail = false;
				Map<String, String> modifiedtitles = new HashMap<String, String>();
				StringBuilder redirectentry = new StringBuilder();
				String oAuthAccessToken = "";
				
				//Clear all Lists before fetching articles
				newarticles.clear();
				modifiedarticles.clear();
				articleinfo.clear();
				articleinfodelta.clear();
				aemarticles.clear();
				deletearticles.clear();
				articleLocationMap.clear();
				modifiedtitles.clear();
				createdArticlePath.clear();
			    deleteArticlePaths.clear();
		
				boolean allarticlesimported = true;
				
				//added for EMKT-14781
				boolean isArticleCreated = false;
				List<String> createdArticlesList = new ArrayList<String>();
				
				if(this.regex == null || regex.isEmpty()){
					regex = DEFAULT_SPECIALCHARS;
				}
				LOG.info("Regular Expressions to escape characters ",regex);
				
				if(this.templateType == null || templateType.isEmpty()){
					templateType = DEFAULT_TEMPLATETYPE;
				}
				LOG.info("template type for article creation  ",templateType);
				
				
				try{
					//get the oAuth access token from (RA) external apigateway
					oAuthAccessToken = getAccessToken();
					LOG.info("RA Access Token : {}", oAuthAccessToken);
					
					//return/quit if the auth access token is empty or null 
					if(StringUtils.isEmpty(oAuthAccessToken)) {
						return false;
					}
					
					//Get all article details from RA portal
					if(getSearchResults(lob, oAuthAccessToken)){
						LOG.debug("Get existing Articles from AEM");						
					    getaemArticles(lob);
					    
					    //Create Article Deletion Lists from Delta
					    for (Map.Entry<String, String> mentry : aemarticles.entrySet()) {
					    	if(!articlefound(mentry.getKey(),articleinfo)){
					    		LOG.info("Article Not found in RA but present in AEM : ID {} name {}", mentry.getKey(),mentry.getValue());
					    		deletearticles.put(mentry.getKey(),mentry.getValue());
					    	}							
					    }			    
					}
					
					//Get delta article from RA portal
					if(getSearchResultsDelta(lob, oAuthAccessToken)){
						for(Articleinfo ainfo : articleinfodelta) {
							
								isArticleCreated = writeArticle(ainfo,lob, oAuthAccessToken ,"true");
								if(isArticleCreated) {
									LOG.info("Articles {} created with Title {} for {} ",ainfo.getArticleID(),ainfo.getArticleTitle(),lob);
								}
							//added for EMKT-14781
							if(isArticleCreated && ainfo != null) {
								LOG.debug("article added to the createdArticlesList : {}", ainfo.getArticleID());
								createdArticlesList.add(ainfo.getArticleID());
							}
					    }
					    
						LOG.info("no.of articles created : {}", createdArticlesList.size());
						
						allarticlesimported = true;
					    int attempt = Integer.parseInt(reattempt);
					    
					    //Reattempt all failed articles
					    for(int i=0; i < attempt ;i++){
					    	LOG.debug("Right Answers - Reattempting {} Time ", i);
					    	for (String failedarticle : failureLists) {
					    		LOG.info("Right Answers - Retrying to fetch Article {} ", failedarticle);
					    		Articleinfo ainfo = getArticleinfo(articleinfodelta, failedarticle);
					    			if(ainfo != null) {
					    				isArticleCreated = writeArticle(ainfo,lob, oAuthAccessToken , "true");
					    			}
		
					    		//added for EMKT-14781
								if(isArticleCreated && ainfo != null) {
									LOG.debug("article added to the createdArticlesList in reattempt : {}", ainfo.getArticleID());
									createdArticlesList.add(ainfo.getArticleID());
								}
					    	}
					    }
					    
					    LOG.info("no.of articles created after retry : {}", createdArticlesList.size());
					    
					    if(allarticlesimported){
					    	LOG.info("Right Answers - Articles are imported successfully so updating date");
					    	updateSuccessdate(lob);
					    }
					    else{
					    	LOG.info("Few Articles are not imported into AEM due to issues {} ",lob);
					    }

					    String querySolutionListPages = "select * from [nt:unstructured] as node where ISDESCENDANTNODE(node, \"/content/cox/" + lob.toLowerCase() + "/corporate/\") and [sling:resourceType] = \"cox/components/content/solution-list\" order by [cq:lastModified] desc";
					    flushSolutionListPages(querySolutionListPages);
					}
					else{
						if(totalhits == 0){
							LOG.info("Right Answers - Skipping getSearchResultDelta for {} since {} articles retrieved : No Articles to import!!!", lob, totalhits);
						}
						else{
							LOG.error("Right Answers - getSearchResultDelta for {} FAILED and got only {} articles ID out of total {} articles ", lob, articleinfodelta.size(),totalhits);							
							return false;
						}						
					}

					//threadSleep();
					// Moving Replication Logic before deleting RA Articles
					long replicationStartTime = System.currentTimeMillis();
					LOG.debug("RA Article Pages created, which needs to be replicated - size {} , processed Time {} ",createdArticlePath.size(),(replicationStartTime-processingStartTime));
					
					initiateUnpublishDeletedArticle();
					prepareForReplication(createdArticlePath,ReplicationActionType.ACTIVATE );
					
					
					LOG.debug("Total Time Execution in Write Article Replication of Lob {} :: {} ",lob,(System.currentTimeMillis()-replicationStartTime));
					
					//Delete Articles
					deleteRAarticles(deletearticles,lob);
					
					//Copy Default Corporate Pages
					copyDefaultcorporatepages(lob);
					
					LOG.debug(" ****************************** Articles Modified List - {} ****************************** ",lob);
					//Create New and Modified Articles Lists 
				    for(Articleinfo ainfo : articleinfodelta) {
					    String aid = ainfo.getArticleID();
					    if(aemarticles.get(ainfo.getArticleID()) != null){
					    	modifiedarticles.put(aid,aemarticles.get(ainfo.getArticleID()));
					    	LOG.debug("{} {}",aid, aemarticles.get(ainfo.getArticleID()));
					    	String escapespecialchars = "";
					    	
					    	//Logic to find if Title is modified or not in delta
					    	if(regex != null && !regex.isEmpty()){
								escapespecialchars = ainfo.getArticleTitle().replaceAll(regex,CoxConstants.EMPTYTEXT);
							}
							else{
								escapespecialchars = ainfo.getArticleTitle();
							}
							String fileName = StringUtils.replace(escapespecialchars.toLowerCase(), " ", "-");
							if(!aemarticles.get(aid).equalsIgnoreCase(fileName)) {
					    		LOG.info("Title is changed for article ID {} Filename {}",aid,fileName);
					    		modifiedtitles.put(aemarticles.get(aid),fileName);
					    	}
					    	else {
					    		LOG.debug("Title is not changed for article id {} {} {}",aid,aemarticles.get(ainfo.getArticleID()), fileName);
					    	}
					    }
					    else{
					    	LOG.debug("Is articleId {} found in the createdArticlesList {}",aid, createdArticlesList.contains(aid));
					    	if(createdArticlesList.contains(aid)) { //added for EMKT-14781
					    		newarticles.put(aid,ainfo.getArticleTitle());
					    	}
					    }
					}
				    
				    //Send Notification to DMAS for article titles modified or deleted articles
				    notifymessage.append("************* Below RA support articles are Deleted *************<br>");
				    if(deletearticles.size() > 0) {
				    	sendemail = true;
						for (Map.Entry<String, String> mentry : deletearticles.entrySet()) {
							LOG.info("Article is deleted and redirect will be added : {} -- {}",mentry.getKey(),mentry.getValue());
					    	notifymessage.append("/").append(lob).append(SUPPORT).append(mentry.getValue()).append(DOT_HTML).append("<br>");
					    	redirectentry.append("/").append(lob).append(SUPPORT).append(mentry.getValue()).append(DOT_HTML).append("=");
					    	redirectentry.append("/").append(lob).append("/support/home.html").append("\n");
					    }	
					}
				    else {
				    	notifymessage.append("    NONE <br>");
				    }
					
				    notifymessage.append("<br> ************* Below RA support article name is changed *************<br>");
				    if(modifiedtitles.size() > 0) {
				    	sendemail = true;
						for (Map.Entry<String, String> mentry : modifiedtitles.entrySet()) {
							notifymessage.append("/").append(lob).append(SUPPORT).append(mentry.getKey()).append(DOT_HTML).append("=");
					    	notifymessage.append("/").append(lob).append(SUPPORT).append(mentry.getValue()).append(DOT_HTML).append("<br>");
					    	redirectentry.append("/").append(lob).append(SUPPORT).append(mentry.getKey()).append(DOT_HTML).append("=");
					    	redirectentry.append("/").append(lob).append(SUPPORT).append(mentry.getValue()).append(DOT_HTML).append("\n");
					    }	
					}
				    else {
				    	notifymessage.append("    NONE <br>");
				    }

					LOG.info("isAddrewrite : {} and sendemail : {} and redirectentry : {}", isAddrewrite, sendemail, redirectentry);
				    if(isAddrewrite && sendemail) {
						//ResourceResolver resourceResolver = resourceResolverService.getResourceResolver("cox-rulebuilder-user");//CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
                      	ResourceResolver resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
						Externalizer externalizer = resourceResolver.adaptTo(Externalizer.class);
						String authorLink = externalizer.authorLink(resourceResolver,"/");			
				    	notifymessage.append("<br> Below redirect properties for expired articles are updated <br>");
				    	notifymessage.append("<a href=\"").append(authorLink + "editor.html/content/cox/application/si/resourcebundles/").append(lob).append("_support_redirect.html\">");
				    	notifymessage.append("Update Redirect File for Expired Articles </a> <br>");
				    	LOG.info(" Article Notification Message : {} ",notifymessage);
				    	updateRedirectProperties(redirectentry,lob, resourceResolver);
				    	sendEmail(notifymessage);
				    }
				    	
				    //END - Send Notification to DMAS for article titles modified or deleted articles
				    
				    LOG.info(" ****************************** Newly Created Articles - {} ****************************** ",lob);
				    logreport(newarticles);
				    LOG.info(" ****************************** Articles Modified - {} ****************************** ",lob);
				    logreport(modifiedarticles);
				    LOG.info(" ****************************** Articles Deleted  - {} ****************************** ",lob);
				    logreport(deletearticles);
				    
				    LOG.info("Right Answers - {} Articles Created {} Modified and {} Deleted for {}",newarticles.size(),modifiedarticles.size(),deletearticles.size(),lob);
				    
					long processingStopTime = System.currentTimeMillis();
					
					if(lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS)){
						LOG.info("Right Answers - Time taken to process RA IMPORT :  {} MilliSeconds for collection {} ", (processingStopTime - processingStartTime),CoxConstants.BUSINESS_COLLECTIONS);
					}
					else{
						LOG.info("Right Answers - Time taken to process RA IMPORT :  {} MilliSeconds for collection {} ", (processingStopTime - processingStartTime),CoxConstants.RESIDENTIAL_COLLECTIONS);
					}
				} catch (LoginException e) {
					LOG.error("Right Answers - Exception occured.. Error Trace: ", e);
					return false;
				} 	
								
			return true;
		}
		
	private void initiateUnpublishDeletedArticle() {
		Set<String> commonSet = deleteArticlePaths.parallelStream().filter(createdArticlePath::contains).collect(Collectors.toSet());
		LOG.debug("RA Deleted Article List :: {} .",deleteArticlePaths.size());
		LOG.debug("RA Common/Modified Article :: {} common article which need to activate.",commonSet.size());
		LOG.debug("RA Article Map size, which did not find in AEM :: {} ",deletearticles.size());
		Set<String> unpublishArticleList = deleteArticlePaths.parallelStream().filter(item -> !createdArticlePath.contains(item)).collect(Collectors.toSet());
		LOG.debug("RA Articles List which need to be UnPublished :: {}",unpublishArticleList.size());
		prepareForReplication(unpublishArticleList,ReplicationActionType.DEACTIVATE);
		LOG.debug("Deactivated articles : {}", unpublishArticleList.toString());
	}

	/**
	 * Start to Replicate Write Article Pages
	 * @param articlePath
	 * @param action
	 */
	private void prepareForReplication(Set<String> articlePath, ReplicationActionType action) {
		LOG.info("inside prepareForReplication");
		ResourceResolver resourceResolver = null;
		int chunkSize = this.chunkSizeRa;
		List<List<String>> chopedList = Lists.partition(new ArrayList<>(articlePath), chunkSize);
		LOG.info("chopedList size {}" , chopedList.size());
		try {
			resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
			Session session = resourceResolver.adaptTo(Session.class);
			LOG.debug("session in prepareForReplication {}" , session);
			int noOfChunk = 0;
			
			for (List<String> item : chopedList) {
				LOG.debug("Replicating Paths Start");
				for(String paths : item) {
					LOG.debug("{}" , paths);
				}
				LOG.debug("Replicating Paths End");
				 processInReplicationQ(item, resourceResolver, session,action);
				if(action.equals(ReplicationActionType.DEACTIVATE)){
					for (String delArticle :item) {
						session.removeItem(delArticle);
					}
					session.save();
				}else {
					for (String writeArticle :item) {
						Node jcrContent = session.getNode(writeArticle).getNode(JcrConstants.JCR_CONTENT);
						//jcrContent.addMixin("mix:lockable");
						session.save();
						//session.getWorkspace().getLockManager().lock(jcrContent.getPath(), true, false, 10000, "admin");
					}
					session.save();
				}
				 noOfChunk++;
				 LOG.info("RA Choped List Running Slot :: {}",noOfChunk);
			}
			LOG.debug("Activated articles : {}", articlePath.toString());
		} catch (AccessDeniedException e) {
			LOG.error("AccessDeniedException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (VersionException e) {
			LOG.error("VersionException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (LockException e) {
			LOG.error("LockException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (ConstraintViolationException e) {
			LOG.error("ConstraintViolationException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (RepositoryException e) {
			LOG.error("RepositoryException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (LoginException e) {
			LOG.error("LoginException occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (Exception e) {
			LOG.error("Exception occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} finally {
			if(resourceResolver!=null && resourceResolver.isLive()){
				resourceResolver.close();
			}
		}
	}

	/**
	 * Start RA Article Pages in Replication Queue.
	 * @param chopList
	 * @param resourceResolver
	 * @param session
	 * @param action
	 */
	private void processInReplicationQ(List<String> chopList, ResourceResolver resourceResolver, Session session, ReplicationActionType action) {
		long startTime = System.currentTimeMillis();
		try {
			LOG.info("inside processInReplicationQ");
			ReplicationOptions options = new ReplicationOptions();
			options.setSynchronous(true);
			options.setSuppressVersions(true);
			//Modified as part of EMKT-15266
			//String[] chunkArticlePaths = chopList.stream().toArray(String[]::new);
			List<String> chunkArticlePathsList = chopList.stream()
				      //.filter(Objects::nonNull)
				      .filter(str -> null != str && str.trim().length() > 0)
				      .collect(Collectors.toList());
//			replicator.replicate(session,action,chunkArticlePaths,options);
			replicator.replicate(session,action,chunkArticlePathsList.stream().toArray(String[]::new),options);
			long totalTime = System.currentTimeMillis() - startTime;
			if(ReplicationActionType.ACTIVATE.equals(action)) {
				List<Resource> createResurceLst = new ArrayList<>();
				chopList.parallelStream().forEach(item -> createResurceLst.add(resourceResolver.getResource(item)));
				Resource[] resources = new Resource[createResurceLst.size()];
				createResurceLst.toArray(resources);
				AtomicBoolean checkStatus = new AtomicBoolean(true);
				LOG.info("checkStatus RA {}" , checkStatus);
				do {
					Map<String, ReplicationStatus> batchReplicationStatus = replicationStatusProvider.getBatchReplicationStatus(resources);
					LOG.info("batchReplicationStatus RA {}" , batchReplicationStatus);
					batchReplicationStatus.forEach((path, status) -> {
						boolean activated = status.isActivated();
						LOG.info("RA Article Path {} - Status :: {}", path, status);
						if (activated) {
							checkStatus.set(false);
						} else {
							checkStatus.set(true);
						}
					});
				} while (checkStatus.get());
			}
			LOG.info("In RA single batch - Total Time taken in Replication process :: {} ",totalTime);
		} catch (ReplicationException e) {
			LOG.error("Error occurred during replication of RA Article {} ",e.getMessage());
		}
	}

	/**
		 * Update Redirect Properties for RA Modified and Deleted Articles
		 * @param redirectentry
		 * @param lob
		 */
		private void updateRedirectProperties(StringBuilder redirectentry, String lob, ResourceResolver resourceResolver) {
			String redirectprops = this.raResidentialRedirectDefaultPath;
			
			/*
			 * if(lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS)) {
			 * redirectprops = this.raBusinessRedirectDefaultPath; }
			 */
			LOG.info("updateRedirectProperties redirectprops : {} and redirectentry : {}", redirectprops, redirectentry);
			String propertiesnode = redirectprops + "/parcontent/properties_code_bloc";
			
			DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DATE_FORMAT);
			LocalDateTime now = LocalDateTime.now();
			String dateModified = "###" + (dtf.format(now)).toString();
			String newpropstext = "";
			
			//ResourceResolver resourceResolver = null;
			Node jcrNode = null;
			Node jcrProps = null;
			String propstext = "";
			try {
				//resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
				//resourceResolver = resourceResolverService.getResourceResolver("cox-rulebuilder-user");//CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
				Resource jcrResource =  resourceResolver.getResource(redirectprops);
				Resource propResource =  resourceResolver.getResource(propertiesnode);
				Session session = ((Session) resourceResolver.adaptTo(Session.class));
              	LOG.info("propResource : {}", propResource);
				if(propResource != null){
	            	jcrProps = propResource.adaptTo(Node.class);
	            	jcrNode = jcrResource.adaptTo(Node.class);
	            	if(jcrProps != null) {
	            		DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
	                	Calendar calobj = Calendar.getInstance();
	                	LOG.info(" Calendar DATE TIME : " + df.format(calobj.getTime()));
						propstext = jcrProps.getProperty("propstext").getString();
						newpropstext = propstext + "\n" + dateModified + "\n" + redirectentry;
						jcrProps.setProperty("propstext", newpropstext);
						session.save();
						jcrNode.setProperty(NameConstants.PN_PAGE_LAST_MOD,df.format(calobj.getTime()));
						jcrNode.setProperty(NameConstants.PN_PAGE_LAST_MOD_BY, "admin");
						jcrNode.setProperty(ReplicationStatus.NODE_PROPERTY_LAST_REPLICATED, df.format(calobj.getTime()));
						jcrNode.setProperty(ReplicationStatus.NODE_PROPERTY_LAST_REPLICATED_BY, "admin");
			            session.save();
                      	LOG.info("update the jcrNode path : {}", jcrNode.getPath());
			            //Added as part of EMKT-15266
						if(jcrNode.getPath()!=null && !jcrNode.getPath().trim().isEmpty()) {
							replicator.replicate(session, ReplicationActionType.ACTIVATE, jcrNode.getPath());
						}
		            }
				}
				LOG.debug("Property Text : {} ", propstext);
				LOG.debug("New Property Text : {} ", newpropstext);
			} catch (ValueFormatException e) {
				LOG.error("Exception occurred in updateRedirectProperties {} for {}",e,lob);
			} catch (PathNotFoundException e) {
				LOG.error("Exception occurred in updateRedirectProperties {} for {}",e,lob);
			} catch (RepositoryException e) {
				LOG.error("Exception occurred in updateRedirectProperties {} for {}",e,lob);
			} catch (ReplicationException e) {
				LOG.error("Exception occurred in updateRedirectProperties {} for {}",e,lob);
			} catch (Exception e) {
				LOG.error("Exception occurred in updateRedirectProperties {} for {}",e,lob);
			}			
			finally
			{
					resourceResolver.close();
			}
		}
		
		/**
		 * Copy Default Pages if it's modified 
		 */		
		private void copyDefaultcorporatepages(String lob) {
			
			ResourceResolver resourceResolver = null;
			LOG.info("Right Answers - Copy Default Corporate Page for RA that's not authored in RA portal for {} ",lob);
			try {
				//resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
				resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
				Session session = ((Session) resourceResolver.adaptTo(Session.class));
				Node page = null;
				String defaultPath = "";
				
				/*
				 * if(lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS)) {
				 * defaultPath = raBusinessDefaultPath; } else
				 */
				{
					defaultPath = raResidentialDefaultPath;
				}
				
				page = session.getNode(defaultPath + JCR_CONTENT);
				
				String defaultTitle = "";
				String aemModifiedDate = "";
				String raModifiedDate = "";
				
				raModifiedDate = getSuccessdate(lob);
				
				if(null != raModifiedDate && raModifiedDate.isEmpty()){
					LOG.info("Right Answrers - ra.successfulimport date not found for {} ",lob);
					return;
				}
				
				long processingStartTime = System.currentTimeMillis();
				
				defaultTitle = page.hasProperty(CoxConstants.JCR_TITLE) ? page.getProperty(CoxConstants.JCR_TITLE).getString() : "";
				aemModifiedDate = page.hasProperty(NameConstants.PN_PAGE_LAST_MOD) ? page.getProperty(NameConstants.PN_PAGE_LAST_MOD).getString() : "";
				
				SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				 
			    String aemDateFormat = aemModifiedDate.substring(0,10);
			    String[] temp = aemDateFormat .split("-");
			    aemDateFormat  = temp[1] + "/" + temp[2] + "/"  + temp[0];
			    
			    Date aemdate = formatter.parse(aemDateFormat);
				Date radate  = null;
				if(null != raModifiedDate){
					radate  = formatter.parse(raModifiedDate);
				}

				
				 if(null !=radate && radate.compareTo(aemdate) <= 0){
					 LOG.info("Right Answers - Copy corporate page since modified date on default is {} and RA import date is {}",aemdate,radate);
					 PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
					 Page sourcepage = pageManager.getPage(defaultPath);
						
					if(!defaultTitle.isEmpty()){
						updatePages(lob,defaultTitle,session,sourcepage,pageManager);
					}
				 }
				 else{
				    	LOG.info("Right Answers - Do NOT copy corporate page for {} since modified date on default is {} and RA import date is {}",lob,aemdate,radate);				    	
				 }
			
				long processingStopTime = System.currentTimeMillis();
				long processingElapsedTime = processingStopTime - processingStartTime;
				LOG.debug("Time taken to process the query in copyDefaultcorporatepages:  {}", processingElapsedTime);
			} catch (ParseException e) {
				LOG.error("Exception occurred in copyDefaultcorporatepages {} for {}",e,lob);
			} catch (LoginException e) {
				LOG.error("Exception occurred in copyDefaultcorporatepages {} for {}",e,lob);
			} catch (PathNotFoundException e) {
				LOG.error("Exception occurred in copyDefaultcorporatepages {} for {}",e,lob);
			} catch (RepositoryException e) {
				LOG.error("Exception occurred in copyDefaultcorporatepages {} for {}",e,lob);
			}		
			finally {
				if(resourceResolver != null){
					resourceResolver.close();
				}
			}
		}

		/**
		 * Delete Articles from AEM if it's removed from RA Portal
		 * @param articlearray
		 */
		private void deleteRAarticles(Map<String, String> articlearray,String lob) {
			for (Map.Entry<String, String> mentry : articlearray.entrySet()) {
				deleteArticlePages(lob.toLowerCase(),mentry.getKey());
			}
			//added to handle the EMKT-14939 scenario of deleting the articles, when there no new articles but articles archived at RA but available in AEM
			LOG.debug("deleting the pages which are not present in the AEM. deleteArticlePaths.size() : {}" , deleteArticlePaths.size()>0);
			if(deleteArticlePaths.size()>0) {
				try {
					ResourceResolver resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
					Session session = ((Session) resourceResolver.adaptTo(Session.class));
					session.getRootNode();
					for (Iterator<String> iterator = deleteArticlePaths.iterator(); iterator.hasNext();) {
						String deletePage = iterator.next();
						if(session.itemExists(deletePage)) {
							PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
							Page page = pageManager.getPage(deletePage);
							if (page.isLocked()) {
								page.unlock();
							}
							//Added as part of EMKT-15266
							if(page.getPath()!=null && !page.getPath().trim().isEmpty()) {
								replicator.replicate(session, ReplicationActionType.DEACTIVATE, page.getPath());
							}
							LOG.debug("-- in deleteRAarticles is page {} deactivated before delete : {}" ,  deletePage, replicator.getReplicationStatus(session, page.getPath()));
							session.save();
							session.removeItem(deletePage);
							session.save();
							LOG.info("::in deleteRAarticles is page {} deleted : {}" , deletePage, !session.itemExists(deletePage));
							//deleteArticlePaths.remove(deletePage);//to remove the article paths once they are removed, otherwise they are getting deleted again during further processing.
							iterator.remove();
							LOG.debug("In deleteArticlePaths deleteArticlePaths size immidiate after delete : {}" , deleteArticlePaths.size());
						} else {
							LOG.debug("in deleteRAarticles page is already deleted : {}" , deletePage);
						}
					}
					LOG.debug("in deleteRAarticles deleteArticlePaths size before return from initiateDeletedArticle() : {}" , deleteArticlePaths.size());
				} catch (AccessDeniedException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (ItemExistsException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (ReferentialIntegrityException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (ConstraintViolationException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (InvalidItemStateException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (VersionException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (LockException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (NoSuchNodeTypeException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (RepositoryException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (WCMException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (ReplicationException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				} catch (LoginException e) {
					LOG.error("Exception while deleting the article (article not found in AEM scenario) {}",e.getMessage());
				}
			}
		}


		/**
		 * logreport on all newly created, modified and deleted RA articles
		 * @param articlearray
		 */
		private void logreport(Map<String, String> articlearray) {
			for (Map.Entry<String, String> mentry : articlearray.entrySet()) {
				LOG.info(mentry.getKey());				
			}
		}


		/**
		 * Get Article object matching with linked ArticleID
		 * @param articleinfo
		 * @param articleID
		 * @return Articleinfo
		 */
		private Articleinfo getArticleinfo(ArrayList<Articleinfo> articleinfo, String articleID) {		
			LOG.debug("In Get Article for {} ", articleID);
			for(Articleinfo ainfo : articleinfo) {
				if(ainfo.getArticleID().equalsIgnoreCase(articleID)){
					return ainfo;
				}
			}
			LOG.error("Right Answers - Article link did NOT Matched with GetSearchResults : ArticleID {} doesn't exists", articleID);
			return null;
		}


		/*
		 * Fetch Article with ArticleID and LOB from RA Portal
		 * @getArticles()
		 */
		public StringBuilder getArticle(String lob,String articleID) throws MalformedURLException {
			deleteArticlePaths.clear();
			createdArticlePath.clear();
			StringBuilder sb = new StringBuilder();
			long processingStartTime = System.currentTimeMillis();
			boolean articlefound = false;
			boolean isArticleImported = false;
			getaemArticles(lob.toLowerCase());
			Articleinfo ainfo = new Articleinfo();
			//articleID = "170613164251799";
				String oAuthAccessToken = getAccessToken();
				if(StringUtils.isNotEmpty(oAuthAccessToken)) {
					ainfo.setArticleID(articleID);
					ainfo.setArticleSummary(getSummary(articleID, lob, oAuthAccessToken));

					isArticleImported = writeArticle(ainfo,lob, oAuthAccessToken ,"true");
					if(isArticleImported) {
						sb.append("Article is imported successfully for ").append(lob);
						articlefound = true;
					}
				}
			



			long processingStopTime = System.currentTimeMillis();
			if(!articlefound){
				sb.append("Article ID ").append(articleID);
				sb.append(" not found in RA portal in ");
				sb.append(lob).append(" collections");
			}
			LOG.debug("RA Article Pages created, which need to be replicated - size {} ",createdArticlePath.size());
			LOG.debug("RA Article Pages deleted, which need to be deleted - size {} ",deleteArticlePaths.size());
			prepareForReplicationUtility(createdArticlePath);

			LOG.info("Right Answers - {}",sb.toString());
			if(CoxConstants.LINE_OF_BUSINESS_BUSINESS.equalsIgnoreCase(lob)){
				LOG.info("Right Answers - Time taken to process single article  {}  is {} MilliSeconds for collection {} ",articleID, (processingStopTime - processingStartTime),CoxConstants.BUSINESS_COLLECTIONS);
			}
			else{
				LOG.info("Right Answers - Time taken to process single article  {}  is {} MilliSeconds for collection {} ",articleID, (processingStopTime - processingStartTime),CoxConstants.RESIDENTIAL_COLLECTIONS);
			}
			return sb;
		}

	private void prepareForReplicationUtility(Set<String> articlePath) {
		LOG.debug("inside prepareForReplication Utility");
		ResourceResolver resourceResolver = null;
		try {
			resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
			Session session = resourceResolver.adaptTo(Session.class);
			LOG.debug("session in prepareForReplicationUtility {}" , session);
			for (String writeArticle : articlePath) {
				LOG.info("writeArticle : {}", writeArticle);
				if(session.itemExists(writeArticle)) {
					PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
					Page page = pageManager.getPage(writeArticle);
					if(page.isLocked()){
						page.unlock();
					}
					//Added as part of EMKT-15266
					if(page.getPath()!=null && !page.getPath().trim().isEmpty()) {
						LOG.info("Adding page path for replication : {}",page.getPath());
						replicator.replicate(session,ReplicationActionType.ACTIVATE,page.getPath());
					}
					session.save();
					LOG.info("Replication request is done for : {}", articlePath);
					//page.lock();
				}
			}
			session.save();	
		} catch (LoginException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (AccessDeniedException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (ItemExistsException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (ReferentialIntegrityException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (ConstraintViolationException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (InvalidItemStateException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (VersionException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (LockException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (NoSuchNodeTypeException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (RepositoryException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (WCMException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		} catch (ReplicationException e) {
			LOG.error("Error occurred during getting chunk replication of RA Article cause :: {} ",e.getMessage());
		}finally {
			if(resourceResolver!=null && resourceResolver.isLive()){
				resourceResolver.close();
			}
		}
	}

	private void initiateDeletedArticle(ResourceResolver resourceResolver , Session session) throws ReplicationException,RepositoryException ,WCMException {
		LOG.debug("RA Deleted Article Set Size :: {} .",deleteArticlePaths.size());
		//LOG.debug("resourceResolver delete {}" , resourceResolver);
		//LOG.debug("session delete {}" , session);
//		for(String deletePage : deleteArticlePaths){
		for (Iterator<String> iterator = deleteArticlePaths.iterator(); iterator.hasNext();) {
			String deletePage = iterator.next();
			if(session.itemExists(deletePage)) {
				PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
				Page page = pageManager.getPage(deletePage);
				if (page.isLocked()) {
					LOG.debug("3 check page locked");
					String lockOwner = page.getProperties().get(JcrConstants.JCR_LOCKOWNER,String.class);
					LOG.debug("lockowner {}" , lockOwner);
					LOG.debug("user id {}" , resourceResolver.getUserID());
					page.unlock();
				}
				//Added as part of EMKT-15266
				if(page.getPath()!=null && !page.getPath().trim().isEmpty()) {
					replicator.replicate(session, ReplicationActionType.DEACTIVATE, page.getPath());
				}
				LOG.debug("-- is page {} deactivated before delete : {}" ,  deletePage, replicator.getReplicationStatus(session, page.getPath()));
				session.save();
				session.removeItem(deletePage);
				session.save();
				LOG.info(":: is page {} deleted : {}" , deletePage, !session.itemExists(deletePage));
				//deleteArticlePaths.remove(deletePage);//to remove the article paths once they are removed, otherwise they are getting deleted again during further processing.
				iterator.remove();
				LOG.debug("deleteArticlePaths size immidiate after delete : {}" , deleteArticlePaths.size());
			} else {
				LOG.debug("page is already deleted : {}" , deletePage);
			}
		}
		LOG.debug("deleteArticlePaths size before return from initiateDeletedArticle() : {}" , deleteArticlePaths.size());
		//LOG.debug("resourceResolver delete after loop{}" , resourceResolver);
		//LOG.debug("session delete after loop{}" , session);
	}
		
		/**
		 * Call API to write Simple or DecisionTree Articles to AEM
		 * @param portVar
		 * @param ainfo
		 * @param userVar
		 * @param lob 
		 */
	private boolean writeArticle(Articleinfo ainfo, String lob, String oAuthAccessToken , String flag) {
		long startTimeWrite = System.currentTimeMillis();
		try (CloseableHttpClient httpClient = HttpClients.createSystem()) {
//			ResourceResolver resourceResolver = resourceResolverService.getResourceResolver("cox-rulebuilder-user");//CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
			ResourceResolver resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
			Session session = ((Session) resourceResolver.adaptTo(Session.class));
			session.getRootNode();

			boolean articleCreated = false;
			String articleID = "";

			articleID = ainfo.getArticleID();

			String solutionResponse = "";
			URIBuilder builder = new URIBuilder();
			String pathLoc=keycloudpath;
			String endpointLoc=keycloudextapigateway;
			builder.setScheme(HTTPS).setHost(endpointLoc).setPath(pathLoc +"solution/"+ articleID)
					.setParameter(COMPANY_CODE, "NA").setParameter(APP_INTERFACE, "sa");
			URI uri = builder.build();
			HttpGet httpGet = new HttpGet(uri.toString());
			LOG.info("Right Answers Get URL in writeArticle - " + uri.toString());
			httpGet.addHeader(API_KEY, apikey);
			httpGet.addHeader(CLIENT_ID, clientid);
			httpGet.addHeader(AUTHORIZATION, BEARER + oAuthAccessToken);
			LOG.info("writeArticle APIKey : {} ClientId : {}  oathaccesstoken : {}", apikey, clientid, oAuthAccessToken);

			CloseableHttpResponse httpResponse;
			LOG.info("Making call to Rest API:: ");
			httpResponse = httpClient.execute(httpGet);
			long totalRes = System.currentTimeMillis()-startTimeWrite;
			LOG.info("GET Response Status in writeArticle:: {} , Time Execution :: {}", httpResponse.getStatusLine().getStatusCode(), totalRes);
            long startTime = System.currentTimeMillis();
			solutionResponse = EntityUtils.toString(httpResponse.getEntity(), UTF8TEXT);
			ObjectMapper objectMapper = new ObjectMapper();
			//TODO : check the requiredness of this below field
//			objectMapper.configure(Feature.INDENT_OUTPUT, true);
			/*
			 * ignore extra fields,
			 */
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			SolutionArticle wsArticle = objectMapper.readValue(solutionResponse, SolutionArticle.class);
			long totalTime = System.currentTimeMillis()-startTime;
			LOG.debug("Time Taken into Solution Article Mapping :: {}",totalTime);
			LOG.info("Object creation time");
			RightAnswersApiInfo apiInfo = new RightAnswersApiInfo();
			apiInfo.setPassword(BEARER + oAuthAccessToken);
			apiInfo.setUrl(endpointLoc);
			apiInfo.setKeycloudUrl(keycloudextapigateway);
			apiInfo.setUserName(username);
			apiInfo.setApikey(apikey);
			apiInfo.setClientid(clientid);
			apiInfo.setHitkeycloudendpoint(keycloudflag);
			apiInfo.setKeycloudPath(keycloudpath);
			apiInfo.setPath(pathLoc);

			Article article = setArticle(wsArticle, ainfo.getArticleSummary());
			List<Attribute> attrmap = article.getAttributemap();
			articleCreated = false;
			String solutionType = "";
			String corppagepath = "";

			for (Attribute attr : attrmap) {
				startTime = System.currentTimeMillis();
				if ("Cox.com Location".equalsIgnoreCase(attr.getName())) {
					List<String> locations = attr.getValues();
					deleteArticlePages(lob, article.getSolutionid());
					if(flag.equalsIgnoreCase("true")) {
						initiateDeletedArticle(resourceResolver,session);
					}
                    LOG.debug("Time Taken in processing deleteArticlePages method :: {} ",(System.currentTimeMillis()-startTime));
                    Node locDefaultPage = null; 
                    boolean locDefaultPageCreated = false;
                    String defaultLocXFPath = "";
					for (String location : locations) {
						LOG.debug("Location : " + location);

						if(null==locDefaultPage && location != null && !location.isEmpty() && !("global".equalsIgnoreCase(location))) {
							locDefaultPage = ReactRightAnswersArticleUtil.createLocationDefaultPage(resourceResolver, article, articleinfo, location,
									replicator, lob, csspath, regex, false, apiInfo, templateType, this.reactRASupportPagesPath, locations);
							defaultLocXFPath = locDefaultPage.getPath();
						}
						
						if (article.getType() != null) {
							solutionType = article.getType();
							if ("Template Solution".equalsIgnoreCase(solutionType)) {
								//Create Simple Article
								startTime = System.currentTimeMillis();
								Node writeArticlePageNode = ReactRightAnswersArticleUtil.generateReactRightAnswersHtmlNode(resourceResolver, article, articleinfo, location,
										replicator, lob, csspath, regex, false, apiInfo, templateType, this.reactRASupportPagesPath, defaultLocXFPath);
								if(writeArticlePageNode!=null){
									LOG.debug("Adding Article {} to Activation Queue",writeArticlePageNode.getPath());
									Resource resource = resourceResolver.getResource(writeArticlePageNode.getPath()+"/jcr:content/root/responsivegrid/article_xf");
									String xfPath = (String) resource.adaptTo(ValueMap.class).get("fragmentVariationPath");
									this.createdArticlePath.add(xfPath);
									this.createdArticlePath.add(writeArticlePageNode.getPath());
									this.createdArticlePath.add(CoxConstants.RA_ARTICLE_LOCATION_XF_PATH+article.getTitle()+MASTER); //add the location XF for replication
									this.createdArticlePath.add(defaultLocXFPath); //add the location page for replication 
									LOG.info(" Adding Page added -{} and its article xf -{} and default location page {} for replication", writeArticlePageNode.getPath(), xfPath, defaultLocXFPath);
								} else {
									LOG.debug("Article Not Added to Activation Queue because of null !!!!");
								}
								totalTime = System.currentTimeMillis()-startTime;
								LOG.debug("Time Taken in processing raUtil.generateReactRightAnswersHtmlNode :: {} ",totalTime);
							} else if ("Diagnostic".equalsIgnoreCase(solutionType) || ("Decision Tree".equalsIgnoreCase(solutionType)) ) {
								//Create Decision Tree Article
								startTime = System.currentTimeMillis();
								Node writeArticlePageNode = ReactRightAnswersArticleUtil.generateReactRightAnswersHtmlNode(resourceResolver, article, articleinfo, location,
										replicator, lob, csspath, regex, true, apiInfo, templateType, this.reactRASupportPagesPath, defaultLocXFPath);
								if(writeArticlePageNode!=null) {
									LOG.debug("Adding DT or Diagnostic Article {} to Activation Queue",writeArticlePageNode.getPath());
									Resource resource = resourceResolver.getResource(writeArticlePageNode.getPath()+"/jcr:content/root/responsivegrid/article_xf");
									String xfPath = (String) resource.adaptTo(ValueMap.class).get("fragmentVariationPath");
									this.createdArticlePath.add(xfPath);
									this.createdArticlePath.add(writeArticlePageNode.getPath());
									this.createdArticlePath.add(CoxConstants.RA_ARTICLE_LOCATION_XF_PATH+article.getTitle()+MASTER); //add the location XF for replication
									this.createdArticlePath.add(defaultLocXFPath);//add the location page for replication
									LOG.info(" Adding  DT or Diagnostic Page added -{} and its article xf -{} and default location page {} for replication", writeArticlePageNode.getPath(), xfPath, defaultLocXFPath);
								}
								totalTime = System.currentTimeMillis()-startTime;
								LOG.debug("Time Taken in processing raUtil.generateReactRightAnswersHtmlNode :: {} ",totalTime);
							} else {
								LOG.info("Right Answers - Article {} is neither Simple nor DecisionTree - Solution Type {} How can that be true???? ", articleID, solutionType);
							}
						} else {
							LOG.info("Right Answers - SolutionType is null for Article {} - How can that be true???? ", articleID);
						}


						articleCreated = true;
					}

					// Create Corporate Page for Article created at Locations Page
					if (articleCreated) {
						corppagepath = ReactRightAnswersArticleUtil.generatedefaultCorporateNode(resourceResolver, article, replicator,
								raResidentialDefaultPath, "", lob, regex);
						if(!corppagepath.isEmpty()) {
							this.createdArticlePath.add(corppagepath);
							LOG.debug(" Adding Page added {} for replication", corppagepath);
						}
					}
				}
			}
			LOG.debug(" RA Article write method. Total Time of Execution :: {}",(System.currentTimeMillis()-startTime));
			if (!articleCreated) {
				LOG.info("Right Answers - Skipped Creating article {} since No Location found ", articleID);
				return false;
			}
		} catch (IOException e) {
			LOG.error("Right Answers - Exception ", e);
		} catch (URISyntaxException e) {
			LOG.error("Right Answers - Exception ", e);
		} catch (RepositoryException e) {
			LOG.error("Right Answers - Exception ", e);
		} catch (LoginException e) {
			LOG.error("Right Answers - Exception ", e);
		} catch (ReplicationException e) {
			LOG.error("Right Answers - Exception ", e);
		} catch (WCMException e) {
			LOG.error("Right Answers - Exception ", e);
		} 
//		catch (CoxException e) {
//			LOG.error("Right Answers - Exception ", e);
//		}
		return true;
	}

	/**
	 * Gets the summary.
	 *
	 * @param articleId the article id
	 * @param lob the lob
	 * @return the summary
	 */
	private String getSummary(String articleId, String lob, String oAuthAccessToken)
	{
		LOG.info("Right Answers - Call Get Summary to Fetch the summary information");
		String summary = "";
		String rightAnswersSearchResponse = "";
		URIBuilder builder = new URIBuilder();
		String pathLoc=keycloudpath;
		String endpointLoc=keycloudextapigateway;
		builder.setScheme(HTTPS).setHost(endpointLoc).setPath(pathLoc+SEARCHTEXT)
		    .setParameter(COMPANY_CODE, "NA")
		    .setParameter(APP_INTERFACE, "sa")
			.setParameter(STATUSES_TEXT, CoxConstants.RA_STATUSES).setParameter("queryText", articleId);
		
		if(lob != null && lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS))
			builder.setParameter(COLLECTIONSTEXT, CoxConstants.BUSINESS_COLLECTIONS);
		else										
			builder.setParameter(COLLECTIONSTEXT, CoxConstants.RESIDENTIAL_COLLECTIONS);
		
		
		
		try (CloseableHttpClient httpClient = HttpClients.createSystem();) {
			URI uri = builder.build();
			LOG.info("Right Answers Get URL in getSummary - "+uri.toString());
			HttpGet httpGet = new HttpGet(uri.toString());
			httpGet.addHeader(API_KEY, apikey);
			httpGet.addHeader(CLIENT_ID, clientid);
			httpGet.addHeader(AUTHORIZATION, BEARER + oAuthAccessToken);
			LOG.info("getSummary APIKey : {} ClientId : {}  oathaccesstoken : {}", apikey, clientid, oAuthAccessToken);

			CloseableHttpResponse httpResponse;
			httpResponse = httpClient.execute(httpGet);
			LOG.info("GET Response Status in getSummary:: " + httpResponse.getStatusLine().getStatusCode());

			rightAnswersSearchResponse = EntityUtils.toString(httpResponse.getEntity(), UTF8TEXT);
			ObjectMapper objectMapper = new ObjectMapper();
//			TODO : Check the requiredness of the below code
//            objectMapper.configure(Feature.INDENT_OUTPUT, true);
	        	/*
	        	 * ignore extra fields,
	        	 */
//            objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			RaResult result = objectMapper.readValue(rightAnswersSearchResponse, RaResult.class);
			if(result!=null && result.getSolutions()!=null)
			{
				List<Solution> solutions = result.getSolutions();
				
				for (Solution solution : solutions) {
					if(!StringUtils.isEmpty(solution.getSummary())) {	
						summary = solution.getSummary();
						break;
					}
				}
			}
		} catch (IOException e) {
			LOG.error("Right Answers - Exception occured in getSearchResults - Error Trace:", e);
		} catch (URISyntaxException e) {
			LOG.error("Right Answers - Exception occured in getSearchResults - Error Trace:", e);
		}
		
		LOG.debug("Right Answers - Articles retrieved {} out of {} for {} ", articleinfo.size(),totalhits,lob);
		return summary;
	
	}
		
		/**
		 * Set Article details in Object
		 * @param wsArticle
		 * @param summary
		 * @return 
		 */
		private Article setArticle(SolutionArticle wsArticle,String summary) {
			
			Article article = new Article();
			article.setSolutionid(wsArticle.getId());
			article.setSummary(summary);				
			article.setApprover(wsArticle.getApprover());
			article.setAttributesetname(wsArticle.getAttributeSetName());				
			article.setAttributemap(wsArticle.getAttributes());
			article.setAuthor(wsArticle.getAuthor());
			article.setCategoryCode(wsArticle.getCategoryCode());
			article.setCollection(wsArticle.getCollections());
			long createDate = wsArticle.getCreateDate();
			final String newYorkText="America/New_York";
			GregorianCalendar createDateCalendar = new GregorianCalendar(TimeZone.getTimeZone(newYorkText));
			createDateCalendar.setTimeInMillis(createDate);
			article.setCreateDate(createDateCalendar);
			article.setDiagnosticResponses(wsArticle.getDiagnosticResponses());
			article.setSolutionfieldsmap(wsArticle.getFields());
			article.setKeywords(wsArticle.getKeywords());
			article.setLanguage(wsArticle.getLanguage());
			
			long modifiedDate = wsArticle.getLastModifiedDate();
			GregorianCalendar modifiedCalendar = new GregorianCalendar(TimeZone.getTimeZone(newYorkText));
			modifiedCalendar.setTimeInMillis(modifiedDate);
    		article.setLastModifiedDate(modifiedCalendar);
			article.setQuestion(wsArticle.getQuestion());
			long renewDate = wsArticle.getRenewDate();
			GregorianCalendar renewDateCalendar = new GregorianCalendar(TimeZone.getTimeZone(newYorkText));
			renewDateCalendar.setTimeInMillis(renewDate);
     		article.setRenewDate(renewDateCalendar);
			article.setSolutionBoost(wsArticle.getSolutionBoost());
			article.setSolvedCount(wsArticle.getSolvedCount());
			article.setStatus(wsArticle.getStatus());
			article.setTaxonomies(wsArticle.getTaxonomy());
			article.setTemplateName(wsArticle.getTemplateName());
			article.setTitle(wsArticle.getTitle());
			article.setType(wsArticle.getType());
			article.setViewCount(wsArticle.getViewCount());
			return article;
			
		}
		

		/**
		 * Call GetSearchResults to get all article id, title and summary from RA portal 
		 * @param lob
		 * @return
		 * @throws MalformedURLException
		 */
		public boolean getSearchResults(String lob, String oAuthAccessToken) throws MalformedURLException {
			
			LOG.info("Right Answers - Call GETSearchResults to fetch all articles information");
			
			totalhits = 0;
			String rightAnswersSearchResponse = "";
            
			URIBuilder builder = new URIBuilder();
			String pathLoc=keycloudpath;
			String endpointLoc=keycloudextapigateway;
			builder.setScheme(HTTPS).setHost(endpointLoc).setPath(pathLoc+SEARCHTEXT)
			    .setParameter(COMPANY_CODE, "NA")
			    .setParameter(APP_INTERFACE, "sa")
				.setParameter(STATUSES_TEXT, CoxConstants.RA_STATUSES);
			
			if(lob != null && lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS))
				builder.setParameter(COLLECTIONSTEXT, CoxConstants.BUSINESS_COLLECTIONS);
			else										
				builder.setParameter(COLLECTIONSTEXT, CoxConstants.RESIDENTIAL_COLLECTIONS);

			long startTime = System.currentTimeMillis();
			try (CloseableHttpClient httpClient = HttpClients.createSystem();) {
				int pageNumber = 0;
				int rafetched = 0;
				int totalrafetched = 0;
				
				while(true) {
					
					builder.setParameter("page", String.valueOf(++pageNumber));
					URI uri = builder.build();
					LOG.info("Right Answers Get URL in getSearchResults - "+uri.toString());
					HttpGet httpGet = new HttpGet(uri.toString());
					httpGet.addHeader(API_KEY, apikey);
					httpGet.addHeader(CLIENT_ID, clientid);
					httpGet.addHeader(AUTHORIZATION, BEARER + oAuthAccessToken);
					LOG.info("APIKey : {} ClientId : {}  oathaccesstoken : {}", apikey, clientid, oAuthAccessToken);

					CloseableHttpResponse httpResponse;
					httpResponse = httpClient.execute(httpGet);
					LOG.info("GET Response Status in getSearchResults:: " + httpResponse.getStatusLine().getStatusCode());


					rightAnswersSearchResponse = EntityUtils.toString(httpResponse.getEntity(), UTF8TEXT);
					ObjectMapper objectMapper = new ObjectMapper();
					//TODO : check the requiredness of the below code
//		            objectMapper.configure(Feature.INDENT_OUTPUT, true);
		        	/*
		        	 * ignore extra fields,
		        	 */
//		            objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
					RaResult result = objectMapper.readValue(rightAnswersSearchResponse, RaResult.class);
					
					
					rafetched = result.getSolutions().size();
					totalhits = result.getTotalHits();
					
					LOG.info("Right Answers - Number of Articles Fetched {} on page {} for {} ", rafetched, pageNumber,lob);
					
					//Break the loop if no articles are retrieved
					if(rafetched == 0) {
						break;
					}
					
					List<Solution> solutions = result.getSolutions();
					
					for (Solution solution : solutions) {
						Articleinfo ainfo = new Articleinfo();
						ainfo.setArticleID(solution.getId());
						ainfo.setArticleSummary(solution.getSummary());
						ainfo.setArticleTitle(solution.getTitle());
						LOG.info("Article {} added to list during full fetch.", solution.getId());
						articleinfo.add(ainfo);
					}
					
					totalrafetched += rafetched;
					
					//Break the loop if less than 10 articles are retrieved - It'll be last page 
					if( (totalhits == totalrafetched) || (rafetched < 10) ){
						break;
					}						
				}
				LOG.debug("Right Answers - Articles retrieved - Total Time in processed RA Articles from API :: {}",(System.currentTimeMillis()-startTime));
				if(articleinfo.size() == totalhits){
					LOG.debug("Right Answers Inside - Articles retrieved {} out of {} for {} ", articleinfo.size(),totalhits,lob);
					return true;
				}     
			} catch (IOException e) {
				LOG.error("Right Answers - Exception occured in getSearchResults - Error Trace:", e);
			} catch (URISyntaxException e) {
				LOG.error("Right Answers - Exception occured in getSearchResults - Error Trace:", e);
			}
			LOG.debug("Right Answers - Articles retrieved {} out of {} for {} ", articleinfo.size(),totalhits,lob);
			return false;
		}
		
		
		
    
  		/** to get the access token of oauth2 */
		public String getAccessToken() {
			String oAuthAccessToken = "";
			try(CloseableHttpClient httpClient = HttpClients.createSystem()) {
				
				URIBuilder builder = new URIBuilder();
				builder.setScheme(HTTPS).setHost(keycloudextapigateway).setPath(keycloudoauth2path).setParameter("grant_type", "client_credentials");
				
				URI uri = builder.build();
				LOG.info("Access Token URL in getAccessToken - "+uri.toString());
				LOG.debug("oauth clientid:{}, clientsecret:{}", extapigatewayclientid, extapigatewayclientsecret);
				HttpPost httpPost = new HttpPost(uri.toString());
				httpPost.setHeader("Accept", "application/json");
				httpPost.setHeader(CLIENT_ID, clientid);
				String clientcredentails = extapigatewayclientid + ":" + extapigatewayclientsecret;
				String encodedAuthorization = Base64Utils.base64Encode(clientcredentails);
				httpPost.setHeader(AUTHORIZATION, BASIC + encodedAuthorization);
				LOG.info("ClientId : {}  encodedAuthorization : {}", clientid, encodedAuthorization);

				CloseableHttpResponse httpResponse;
				long startTime = System.currentTimeMillis();
				httpResponse = httpClient.execute(httpPost);
				LOG.info("Fetch OAUTH Token Time Taken :: {} ",(System.currentTimeMillis()-startTime));
				LOG.info("Response Status in getAccessToken:: " + httpResponse.getStatusLine().getStatusCode());
				String response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
				LOG.info("Returned acces token response is : {}", response);
				JSONObject json = new JSONObject(response);
				if(json.has("access_token")) {
					oAuthAccessToken = json.getString("access_token");
					LOG.info("oAuthAccessToken : "+oAuthAccessToken);
					return oAuthAccessToken;
				}
				
			} catch (IOException e) {
				LOG.error("Exception while making the RA Access Token call {}", e.getMessage());
			} catch (URISyntaxException e) {
				LOG.error("Exception while making the RA Access Token call {}", e.getMessage());
			} catch (JSONException e) {
				LOG.error("Exception while making the RA Access Token call {}", e.getMessage());
			}
			return oAuthAccessToken;
		}
		
		/**
		 * Call GetSearchResultsDelta to get only modified articleinfo from RA portal 
		 * @param lob
		 * @return
		 * @throws MalformedURLException
		 */
		public boolean getSearchResultsDelta(String lob, String oAuthAccessToken) throws MalformedURLException {
			
			LOG.info("Right Answers - Call GETSearchResults DELTA to fetch article information");
			
			totalhits = 0;
			String rightAnswersSearchResponse = "";
			URIBuilder builder = new URIBuilder();
			
			Date date = new Date(System.currentTimeMillis());
		    SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
		    String today = formatter.format(date);
		    String datesuccess = getSuccessdate(lob);
			String pathLoc=keycloudpath;
			String endpointLoc=keycloudextapigateway;
			builder.setScheme(HTTPS).setHost(endpointLoc).setPath(pathLoc+SEARCHTEXT)
			    .setParameter(COMPANY_CODE, "NA")
			    .setParameter(APP_INTERFACE, "sa")
			    .setParameter(STATUSES_TEXT, CoxConstants.RA_STATUSES)
				.setParameter("startDate", datesuccess)
				.setParameter("endDate", today);
			    
			if(lob != null && lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_BUSINESS))
				builder.setParameter(COLLECTIONSTEXT, CoxConstants.BUSINESS_COLLECTIONS);
			else										
				builder.setParameter(COLLECTIONSTEXT, CoxConstants.RESIDENTIAL_COLLECTIONS);
			
			LOG.info("Right Answers - Get RA Articles Modified from {} to {} ", datesuccess,today);

			long startTime = System.currentTimeMillis();
			try (CloseableHttpClient httpClient = HttpClients.createSystem();) {
				int pageNumber = 0;
				int rafetched = 0;
				int totalrafetched = 0;

				while(true) {
					builder.setParameter("page", String.valueOf(++pageNumber));
					URI uri = builder.build();
					LOG.info("Right Answers Get URL in getSearchResultsDelta - "+uri.toString());
					HttpGet httpGet = new HttpGet(uri.toString());
					httpGet.addHeader(API_KEY, apikey);
					httpGet.addHeader(CLIENT_ID, clientid);
					httpGet.addHeader(AUTHORIZATION, BEARER + oAuthAccessToken);
					LOG.info("getSearchResultsDelata APIKey : {} ClientId : {}  oathaccesstoken : {}", apikey, clientid, oAuthAccessToken);
					
					CloseableHttpResponse httpResponse;
					httpResponse = httpClient.execute(httpGet);
					LOG.info("GET Response Status in getSearchResultsDelta:: " + httpResponse.getStatusLine().getStatusCode());

					rightAnswersSearchResponse = EntityUtils.toString(httpResponse.getEntity(), UTF8TEXT);
					ObjectMapper objectMapper = new ObjectMapper();
					//TODO : Check the requiredness of the below code 
//		            objectMapper.configure(Feature.INDENT_OUTPUT, true);
		        	/*
		        	 * ignore extra fields,
		        	 */
//		            objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
					RaResult result = objectMapper.readValue(rightAnswersSearchResponse, RaResult.class);
					if(result.getSolutions() != null)
					{
						rafetched = result.getSolutions().size();
					}
					totalhits = result.getTotalHits();
					
					LOG.info("Right Answers Delta - Number of Articles Fetched {} on page {} ", rafetched, pageNumber);
					
					//Break the loop if no articles are retrieved
					if(rafetched == 0) {
						break;
					}
					
					List<Solution> solutions = result.getSolutions();
					
					for (Solution solution : solutions) {
						Articleinfo ainfo = new Articleinfo();
						ainfo.setArticleID(solution.getId());
						ainfo.setArticleSummary(solution.getSummary());
						ainfo.setArticleTitle(solution.getTitle());
						LOG.info("Article {} added to list during delta fetch.", solution.getId());
						articleinfodelta.add(ainfo);
					}
					
					totalrafetched += rafetched;
					
					//Break the loop if less than 10 articles are retrieved - It'll be last page 
					if( (totalhits == totalrafetched) || (rafetched < 10) ){
						break;
					}						
				}
				LOG.debug("Right Answers Inside - Delta Articles retrievedTotal Time in processed Delta RA Articles from API :: {}",(System.currentTimeMillis()-startTime));
				if(articleinfodelta.size() == totalhits){
					LOG.debug("Right Answers Inside - Articles retrieved {} out of {}", articleinfodelta.size(),totalhits);
					return true;
				}
			} catch (IOException e) {
				LOG.error("Right Answers - Exception occured in getSearchResultsDelta - Error Trace:",e);
			} catch (URISyntaxException e) {
				LOG.error("Right Answers - Exception occured in getSearchResultsDelta - Error Trace:",e);
			}
			return false;
		}

		/**
		 * Flush cache of AEM pages with solution list components.
		 */
		public void flushSolutionListPages(String querySolutionListPages)		
		{	
			LOG.info("Inside Flush");
		
			List<String> referencePathList = new ArrayList<String>();
			//try (ResourceResolver resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT)){
			try (ResourceResolver resourceResolver = resourceResolverService.getResourceResolver("cox-rulebuilder-user"))//CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT))
			{
				LOG.info("Iside Try");
			
				Session session = ((Session) resourceResolver.adaptTo(Session.class));
				LOG.info("After session");
				
				// Obtain the query manager for the session
				QueryManager queryManager = session.getWorkspace().getQueryManager();
				LOG.info("After query manager");
				
				// Create the Queries
				Query querySolutionListPage = queryManager.createQuery(querySolutionListPages, JCR_SQL2);
				LOG.info("Query to flush pages with solution list components : " + querySolutionListPage.getStatement());
				long queryStartTime = System.currentTimeMillis();

				// Execute the queries and get the results
				QueryResult resultSolutionListPages = querySolutionListPage.execute();
				LOG.debug("Query Results for Ra Components: " + resultSolutionListPages);

				long queryStopTime = System.currentTimeMillis();
				long queryElapsedTime = queryStopTime - queryStartTime;
				LOG.debug("Time taken to execute the query:  {}", queryElapsedTime);

				long processingStartTime = System.currentTimeMillis();

				// Iterate over the nodes in the results
				NodeIterator nodeIterSolutionList = resultSolutionListPages.getNodes();
				while (nodeIterSolutionList.hasNext())
				{
					referencePathList=addReferencePathList(nodeIterSolutionList, referencePathList);
				}
				ResourceResolver dispFlushResourceResolver = resourceResolverService.getResourceResolver("replication-service");//CoxUtils.getAdminResourceResolver(this.resolverFactory, "replication-service");
				//List<String> publishedPages = CoxUtils.filterListOnPublishedPages(referencePathList, resourceResolver);
				//Modified as part of EMKT-15266
				//DistributionRequest distributionRequest = new SimpleDistributionRequest(DistributionRequestType.INVALIDATE, false, referencePathList.toArray(new String[0]));
    			List<String> referencePathListFinal = referencePathList.stream()
    				      //.filter(Objects::nonNull)
    				      .filter(str -> null != str && str.trim().length() > 0)
    				      .collect(Collectors.toList());
    			LOG.debug("flushSolutionListPages referencePathList paths: ", referencePathList.toString());
    			LOG.debug("flushSolutionListPages referencePathListFinal paths: ", referencePathListFinal.toString());
                if(referencePathListFinal.size()>0) {
                	DistributionRequest distributionRequest = new SimpleDistributionRequest(DistributionRequestType.INVALIDATE, false, referencePathListFinal.toArray(new String[0]));
                	DistributionResponse distribute = distributor.distribute(agentName, dispFlushResourceResolver, distributionRequest);
                	LOG.info("RA Utility - Pages Flushed and Response message :: {}",distribute.getMessage());
					//dispatcherFlusher.flush(resourceResolver, ReplicationActionType.ACTIVATE, false, DispatcherFlushFilter.RESOURCE_ONLY, referencePathList.toArray(new String[0]));
					
					long processingStopTime = System.currentTimeMillis();
					long processingElapsedTime = processingStopTime - processingStartTime;
					LOG.debug("Time taken to process the query to deactivating the Solution List Pages:  {}", processingElapsedTime);
                }
		} catch (LoginException e) {
			LOG.error("Exception happened while deactivating the Solution List Pages.",e);
		} catch (InvalidQueryException e) {
			LOG.error("Exception happened while deactivating the Solution List Pages.",e);
		} catch (RepositoryException e) {
			LOG.error("Exception happened while deactivating the Solution List Pages.",e);
		} catch (Exception e) {
			LOG.error("Exception happened while deactivating the Solution List Pages.",e);
		}

			LOG.info("flushSolutionListPages End");
		}
		
		
		/**
		 * Delete article pages from AEM
		 *
		 * @param lob the lob
		 */
		private void deleteArticlePages(String lob, String articleid)
		{
			String deletePath = "";
			ResourceResolver resourceResolver = null;
			
			ArticleLocations articleLocation = articleLocationMap.get(articleid);
			if(articleLocation==null)
			{
				LOG.info("No Article Found for delete in repository for articled id - {}", articleid);
				return;
			}
			String corppageref = articleLocation.getCorpRefPath();
			
			try{
			/* Execution of cleaning up of stale articles -- Start */
			//resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
			resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));
			Session session = ((Session) resourceResolver.adaptTo(Session.class));
        	
			Node root = null;
			Node deletePageNode = null;
			
			long processingStartTime = System.currentTimeMillis();

			PageManager pageManager = resourceResolver.adaptTo(PageManager.class);
			Page createdpage = null;

			for(String deleteNodeLocation: articleLocation.getLocations())
			{
				
					LOG.debug("Found Page - "+deleteNodeLocation);
					deletePath = StringUtils.removeEnd(deleteNodeLocation, JCR_CONTENT);
					root = session.getRootNode();
					if(root.hasNode(deletePath.substring(1)))
					{
						LOG.debug("Deleting Page in deletenode - {} ",deletePath.substring(1));
						createdpage = pageManager.getPage("/" + deletePath);
						if(createdpage.isLocked()){
							LOG.debug("Unlocking Page locked - {} ",createdpage.getPath());
							createdpage.unlock();
						}
						else{
							LOG.debug("Page is not locked. lock staus - {} ",createdpage.isLocked());
						}

						deletePageNode = root.getNode(deletePath.substring(1));
						/*replicator.replicate(session, ReplicationActionType.DEACTIVATE, deletePath);
						session.save();
						deletePageNode.remove();
						session.save();*/
						deleteArticlePaths.add(deletePath);

					}
					LOG.debug("Corporate Page Reference Inside Loop - {} ",corppageref);

				
				

			}
			LOG.debug("Corporate Page Ref - {} ",corppageref);
			if(!corppageref.isEmpty()){
				root = session.getRootNode();
				deletePath = corppageref.substring(1, corppageref.length());
				if(root.hasNode(deletePath))
				{	
					LOG.debug("Deleting Page in corporate page - {} ",deletePath);
					createdpage = pageManager.getPage("/" + deletePath);
					if(createdpage.isLocked()){
						LOG.debug("Unlocking Page to delete page- {} ",createdpage.getPath());
						createdpage.unlock();										
					}
					deletePageNode = root.getNode(deletePath);
					/*replicator.replicate(session, ReplicationActionType.DEACTIVATE, corppageref);
					session.save();
					deletePageNode.remove();
					session.save();*/
					deleteArticlePaths.add(corppageref);
				}
			}
			
			long processingStopTime = System.currentTimeMillis();
			long processingElapsedTime = processingStopTime - processingStartTime;
			//LOG.debug("Time taken to process the query deleting the right answers article pages with path :  {}", processingElapsedTime);
			
			} catch (LoginException e) {
				LOG.error("Exception happened in deleteArticlePages while deleting the right answers article pages with path - {} ", deletePath, e);
			} catch (RepositoryException e) {
				LOG.error("Exception happened in deleteArticlePages while deleting the right answers article pages with path - {} ", deletePath, e);
			} catch (WCMException e) {
				LOG.error("Exception happened in deleteArticlePages while deleting the right answers article pages with path - {} ", deletePath, e);
			}
			
			finally
			{
				if(resourceResolver != null){
					resourceResolver.close();
				}
			}
				}
		
	/**
	 * Get Article object matching with linked ArticleID
	 * @param articleinfolist
	 * @param articleID
	 * @return articletitle
	 */
	private boolean articlefound(String articleID,ArrayList<Articleinfo> articleinfolist) {		
		for(Articleinfo ainfo : articleinfolist) {
			if(ainfo.getArticleID().equalsIgnoreCase(articleID)){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Update Date after importing all articles from RA successfully  
	 * @param lob
	 */
	private void updateSuccessdate(String lob){
	    LOG.info("Update RA import Success date for {}",lob);
	    Node root = null;
	    ResourceResolver resourceResolver = null;
	    
	    try {
	       resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"));

	       Date date = new Date(System.currentTimeMillis());
	        SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
	        String today = formatter.format(date);
	        Resource parentPage = null; 
	        
	        if(lob.equalsIgnoreCase(CoxConstants.LINE_OF_BUSINESS_RA_BUSINESS)){
	          parentPage = resourceResolver.getResource("/" + CoxConstants.RA_BUSINESS_LOC + "/support/jcr:content");
	        }
	        else{
	           //parentPage = root.getNode(CoxConstants.RA_RESIDENTIAL_LOC);
	           parentPage = resourceResolver.getResource("/" + this.reactRASupportPagesPath + "/support/jcr:content");
	        }

	       ModifiableValueMap mvp = parentPage.adaptTo(ModifiableValueMap.class);
	       mvp.put("ra.successfulimport",today);
	       resourceResolver.commit();
	       LOG.info("Success date is updated to {} for {}", today,lob);
	    } catch (LoginException e) {
	       LOG.error("Exception while updating Successdate: {} ", e);
	    } catch(Exception e) {
	       LOG.error("Exception while updating Successdate: {} ", e);
	    }
	    finally {
	       if(resourceResolver != null){
	          resourceResolver.close();
	       }        
	    }
	}
	
	/**
	 * Get Date of last successful RA imports
	 * @param lob
	 */
	private String getSuccessdate(String lob){
		LOG.debug("Get Date of last successful RA imports");
		String successfulimport = ""; 
		Node root = null;
		//try (ResourceResolver resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT)){
		try(ResourceResolver resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"))){
			Session session = ((Session) resourceResolver.adaptTo(Session.class));
			root = session.getRootNode();

			Node parentPage = null; 
		    
		    if(CoxConstants.LINE_OF_BUSINESS_RA_BUSINESS.equalsIgnoreCase(lob)){
		    	parentPage = root.getNode(CoxConstants.RA_BUSINESS_LOC);
		    }
		    else{
//		    	parentPage = root.getNode(CoxConstants.RA_RESIDENTIAL_LOC);
		    	parentPage = root.getNode(this.reactRASupportPagesPath);
		    }
		 	parentPage = parentPage.getNode("support");
			parentPage = parentPage.getNode(JcrConstants.JCR_CONTENT);
			final String successfulimportText="ra.successfulimport";
			successfulimport = parentPage.hasProperty(successfulimportText) ? parentPage.getProperty(successfulimportText).getString() : "";
			LOG.debug("RA import utility was last run successful on : {} ", successfulimport);
		} catch (RepositoryException e) {
			LOG.error("Exception while Getting Success Date : {} ", e);
		} catch (LoginException e) {
			LOG.error("Exception while Getting Success Date : {} ", e);
		}
		return successfulimport;
	}
	
	/**
	 * Get AEM article pages.
	 *
	 * @param lob the lob
	 */
	private void getaemArticles(String lob)
	{
		String sqlStatementRa = "select * from [cq:PageContent] as node where ISDESCENDANTNODE(node, '/content/cox/" + lob + "/corporate/') and [cq:template] = '/conf/cox-cms-react/settings/wcm/templates/ra-article' and [section] = 'Support'" ;
		
		aemarticles.clear();

		//try (ResourceResolver resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT)){
		try(ResourceResolver resourceResolver = resolverFactory.getServiceResourceResolver(Collections. < String, Object > singletonMap(ResourceResolverFactory.SUBSERVICE, "cox-rulebuilder-user"))){
			Session session = ((Session) resourceResolver.adaptTo(Session.class));

			// Obtain the query manager for the session
			QueryManager queryManager = session.getWorkspace().getQueryManager();

			// Create the Queries
			Query queryRa = queryManager.createQuery(sqlStatementRa, JCR_SQL2);

			LOG.info("Right Answers - Query to fetch AEM articles : " + queryRa.getStatement());
			long queryStartTime = System.currentTimeMillis();

			// Execute the queries and get the results
			QueryResult resultRa = queryRa.execute();
			LOG.debug("Right Answers - Query Results : " + resultRa);

			long queryStopTime = System.currentTimeMillis();
			long queryElapsedTime = queryStopTime - queryStartTime;
			LOG.debug("Right Answers - Time taken to execute the query:  {}", queryElapsedTime);

			long processingStartTime = System.currentTimeMillis();

			updateAemArticles(resultRa);

			long processingStopTime = System.currentTimeMillis();
			long processingElapsedTime = processingStopTime - processingStartTime;
			LOG.debug("Time taken to process the query:  {}", processingElapsedTime);
	} catch (RepositoryException e) {
		LOG.error("Exception in getaemArticles {} ",e);
	} catch (LoginException e) {
		LOG.error("Exception in getaemArticles {} ",e);
	}
}
	
	void sendEmail(StringBuilder notifymessage) {
		if(distributionlist.length != 0) {
			LOG.info("---------- Invoking Email Functionality for RA Import Utility ----------");
            
            ResourceResolver resourceResolver = null;
			try {
				//resourceResolver = CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);
				resourceResolver = resourceResolverService.getResourceResolver("cox-rulebuilder-user");//CoxUtils.getAdminResourceResolver(this.resolverFactory, CoxConstants.SERVICE_USER_COX_COPY_CONTENT);

			Map<String, String> emailParams = new HashMap<String, String>();
			Set<String> receipients = new HashSet<String>();

           for (int i = 0; i < distributionlist.length; i++) {
	            	LOG.debug("Inside Distribution List Loop" + distributionlist[i]);
	            	receipients.add(distributionlist[i]);
            	}
            
			emailParams.put("articles", notifymessage.toString());

			LOG.debug("Email Params Values are ==> " + Arrays.asList(emailParams));

			if(!receipients.isEmpty() && resourceResolver != null){
				List<String> failureList = new ArrayList<String>();//emailService.sendEmail(resourceResolver, CoxConstants.RA_IMPORT_EMAIL_TEMPLATE_PATH, emailParams , receipients.toArray(new String[receipients.size()]));

				if (failureList.isEmpty()) {
					LOG.info("RightAnswers Import - Email sent successfully to the following recipients: ");
					for(String recipient : receipients){
						LOG.info(recipient);
					}
				} else {
					LOG.info("RightAnswers Import = Email failed for the following recipients: ");
					for(String failureEmail : failureList){
						LOG.info(failureEmail);
					}
				}
			}else{
				LOG.info("No recipients available for this RightAnswers Import");
			}

			LOG.info("---------- End Email Functionality for RightAnswers Import ----------");
			} catch (LoginException ex) {
				LOG.error("Exception while creating Resource Resolver {} ", ex);
			}finally {
				if(resourceResolver!=null && resourceResolver.isLive()){
					resourceResolver.close();
				}
			}
        }
	}
	private List<String> addReferencePathList(NodeIterator nodeIterSolutionList,List<String> referencePathList){
		try {

			Node solutionListNode = (Node)nodeIterSolutionList.next();

			String pageNode = solutionListNode.getPath();
			int index = pageNode.indexOf(JcrConstants.JCR_CONTENT);
			String solutionListPagePath = pageNode.substring(0, index);

			LOG.debug("Found Solution Page to clear cache {}",solutionListPagePath);
			referencePathList.add(solutionListPagePath);

		} catch (RepositoryException e) {
			LOG.error("Exception happened while deactivating the Solution List Pages {} ", e);

		}
		return referencePathList;
	}
	private void updateAemArticles(QueryResult resultRa) throws RepositoryException{
		// Iterate over the nodes in the results
		NodeIterator nodeIterRa = resultRa.getNodes();
		String aid = "";
		String aname = "";
		String filename = "";
		while (nodeIterRa.hasNext())
		{
			Node node = (Node)nodeIterRa.next();
			String nodePath = node.getPath();
			LOG.debug("Right Answers - Article Page {}",node.getPath());
			//B-23678 - Reduce Nightly Import Duration changes -- start
			final String corppageRefText="corppageRef";
			String corppageref = node.hasProperty(corppageRefText) ? node.getProperty(corppageRefText).getString() : "";
			//B-23678 - Reduce Nightly Import Duration changes -- end

			aid = node.hasProperty("articleid") ? node.getProperty("articleid").getString() : "";
			aname = StringUtils.removeEnd(node.getPath(), JCR_CONTENT);
			filename = aname.substring(StringUtils.lastIndexOf(aname, "/")+1,aname.length());

			if(!aid.isEmpty()){
				//B-23678 - Reduce Nightly Import Duration changes -- start
				ArticleLocations articleLocation = articleLocationMap.get(aid);
				if(articleLocation==null)
				{
					articleLocation = new ArticleLocations();
				}
				articleLocation.setCorpRefPath(corppageref);
				articleLocation.setCorpRefPathPresent(node.hasProperty(corppageRefText));
				articleLocation.getLocations().add(nodePath);
				articleLocationMap.put(aid,articleLocation);
				//B-23678 - Reduce Nightly Import Duration changes -- end
				aemarticles.put(aid, filename);
			}

			
		}

	}
	private void updatePages(String lob, String defaultTitle, Session session, Page sourcepage, PageManager pageManager){

		NodeIterator nodeIterRa = null;
		try {
			String sqlStatementRa = "select * from [nt:unstructured] as node where ISDESCENDANTNODE(node, \"/content/cox/" + lob + "/corporate/" + "\") and [jcr:title]=\"" + defaultTitle + "\" and [raflag] = \"true\"";

			// Obtain the query manager for the session
			QueryManager queryManager = session.getWorkspace().getQueryManager();

			// Create the Queries
			Query queryRa = queryManager.createQuery(sqlStatementRa, JCR_SQL2);

			LOG.debug("Right Answers - Query to fetch default RA Pages : " + queryRa.getStatement());
			long queryStartTime = System.currentTimeMillis();

			// Execute the queries and get the results
			QueryResult resultRa = queryRa.execute();
			LOG.debug("Right Answers - Query Results : " + resultRa);

			long queryStopTime = System.currentTimeMillis();
			long queryElapsedTime = queryStopTime - queryStartTime;
			LOG.debug("Right Answers - Time taken to execute the query:  {}", queryElapsedTime);

			nodeIterRa = resultRa.getNodes();
		} catch (RepositoryException e) {
			LOG.error("Exception in updatePages {}", e);
		}
		

		// Iterate over the nodes in the results
		Node root = null;
		Node deletePageNode = null;
		String deletePath = "";
		Page createdpage = null;
if(nodeIterRa != null) {
	while (nodeIterRa.hasNext()) {
		try {
			Node node = (Node) nodeIterRa.next();

			deletePath = StringUtils.removeEnd(node.getPath(), JCR_CONTENT);

			root = session.getRootNode();
			if (root.hasNode(deletePath.substring(1))) {
				LOG.debug("Deleting Page for deleting page- {} ", deletePath.substring(1));
				createdpage = pageManager.getPage("/" + deletePath);
				if (createdpage.isLocked()) {
					LOG.debug("Unlocking Page for deleting page - {} ", createdpage.getPath());
					createdpage.unlock();
				}

				deletePageNode = root.getNode(deletePath.substring(1));
				deletePageNode.remove();
				session.save();

				LOG.info("Right Answers - Copy default Corporate Page at {}", deletePath);

				pageManager.copy(sourcepage, deletePath, null, true, true);

				session.save();
				//createdpage = pageManager.getPage("/" + deletePath);
				node = session.getNode(deletePath + JCR_CONTENT);
				node.setProperty("raflag", true);

				session.save();
				//Added as part of EMKT-15266
				if(deletePath!=null && !deletePath.trim().isEmpty()) {
					replicator.replicate(session, ReplicationActionType.ACTIVATE, deletePath);
				}
				session.save();
				//createdpage.lock();

			}

		} catch (RepositoryException e) {
			LOG.error("Right Answers - Exception happened while fetching article pages {} from AEM {} ", deletePath, e);
		} catch (WCMException e) {
			LOG.error("Right Answers - Exception happened while fetching article pages {} from AEM {} ", deletePath, e);
		} catch (ReplicationException e) {
			LOG.error("Right Answers - Exception happened while fetching article pages {} from AEM {} ", deletePath, e);
		}
	}
}
	}

	@Override
	public String getReactRASupportPagesPath() {
		return reactRASupportPagesPath;
	}
	
    @SuppressWarnings("CQRules:CWE-676")
    private void threadSleep() {
        try {
        	LOG.info("sleeptime : {}", raArticleSleepTime);
        	if(StringUtils.isEmpty(raArticleSleepTime)) {
        		raArticleSleepTime = "20000";
        	}
        	LOG.info("final sleeptime : {}", raArticleSleepTime);
            Thread.sleep(Long.parseLong(raArticleSleepTime));
            LOG.info("after waiting for 10 secs");
        } catch (InterruptedException e) {
            LOG.error("Exception while keeping the delete on pause", e);
            Thread.currentThread().interrupt();
        }
    }
}
