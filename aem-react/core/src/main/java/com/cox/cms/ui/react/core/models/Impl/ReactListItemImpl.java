package com.cox.cms.ui.react.core.models.Impl;

import com.adobe.cq.export.json.ComponentExporter;
import com.adobe.cq.export.json.ExporterConstants;
import com.adobe.cq.wcm.core.components.models.List;
import com.adobe.cq.wcm.core.components.models.ListItem;
import com.cox.cms.ui.react.core.models.ReactListItem;
import com.cox.cms.ui.react.core.service.ReactExternalizerService;
import com.cox.cms.ui.react.core.models.ReactList;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.resource.LoginException;
import org.apache.sling.models.annotations.*;
import org.apache.sling.models.annotations.injectorspecific.ValueMapValue;

import javax.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.commons.lang3.StringUtils;



@Model(	adaptables = SlingHttpServletRequest.class, 
		adapters = { ReactList.class, ComponentExporter.class }, 
		resourceType = ReactListItemImpl.RESOURCE_TYPE, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
@Exporter(
        name = ExporterConstants.SLING_MODEL_EXPORTER_NAME,
        extensions = ExporterConstants.SLING_MODEL_EXTENSION
)

public class ReactListItemImpl implements ReactListItem {
	
    static final String RESOURCE_TYPE = "cox-cms-react/components/list";
    final Logger LOG = LoggerFactory.getLogger(ReactListItemImpl.class);
    
    static final String CONTEXT_URL = "/content/cox";
    static final String CORPORATE_PAGES_URL = "/corporate/pages";
    
    @Inject
    private ReactExternalizerService externalizerService;

    private String id;
    private String icon;
    private String iconAlt;
    private String title;
    private String description;
    private String anchor;
	private String link;
    private String openinnewtab;
    private String listDescription;
	//React Calculator
	private String planName;
    private String planDescription;
    private String rate;
	private String planID;
	//Mix and Match Line Options
	private String optionsIcon;
	private String lines;
	//Mix and Match Plan Options
	private String mndmplanName;
	private String planIcon;
	private String planUnit;
	private Double scratchPrice = null;
	private Double line1 = null;
	private Double line2 = null;
	private Double line3 = null;
	private Double line4 = null;
	private Double line5 = null;
	
	
	private String fragmentId;
	private String accordionIcon;
	private String accordionIconAlttxt; 
	private String title1;
	private String title2;
	private String titleColor;
	private String[] bulletPoints;
	private String bulletIcon;
	private Boolean useRTE;


    public ReactListItemImpl(String id, String icon, String iconAlt, String title, String description, String anchor, String link, String openinnewtab) {
        this.id = id;
        this.icon=icon;
        this.iconAlt=iconAlt;
        this.title=title;
        this.description=description;
        this.anchor = anchor;
        this.link=link;
        this.openinnewtab = openinnewtab;
    }

	public ReactListItemImpl(String id, String icon, String iconAlt, String title, String description, String anchor, String link, String openinnewtab, Boolean useRTE) {
		this.id = id;
		this.icon=icon;
		this.iconAlt=iconAlt;
		this.title=title;
		this.description=description;
		this.anchor = anchor;
		this.link=link;
		this.openinnewtab = openinnewtab;
		this.useRTE = useRTE;
	}
    
    //React Calculator List for PlanOptions
    public ReactListItemImpl(String planName, String planDescription, String rate,String planID) {
        this.planName = planName;
        this.planDescription=planDescription;
        this.rate=rate;
		this.planID=planID;
    }
// This list of for Carousel Mix and Match LineOptions
	public ReactListItemImpl(String optionsIcon, String lines) {
		this.optionsIcon=optionsIcon;
        this.lines = lines;
        
    }
	
// This list of for Carousel Mix and Match LineOptions
	public ReactListItemImpl(String title, String description, boolean forAccodion) {
		this.title = title;
        this.description = description;
    }
	

// This list of for Carousel Mix and Match Plan Options
	public ReactListItemImpl(String mndmplanName, String planIcon, String planUnit, Double scratchPrice,Double line1,Double line2,Double line3,Double line4,Double line5) {
        this.mndmplanName = mndmplanName;
        this.planIcon=planIcon;
		this.planUnit=planUnit;
		this.scratchPrice=scratchPrice;
        this.line1=line1;
		this.line2=line2;
		this.line3=line3;
		this.line4=line4;
		this.line5=line5;
		
	}
	
	
	// This list of for PSU card Options
		public ReactListItemImpl(String fragmentId, String accordionIcon, String accordionIconAlttxt, String title1,String title2,String titleColor,String[] bulletPoints,String bulletIcon, String test) {
	        this.fragmentId = fragmentId;
	        this.accordionIcon = accordionIcon;
			this.accordionIconAlttxt = accordionIconAlttxt;
			this.title1 = title1;
	        this.title2 = title2;
			this.titleColor = titleColor;
			this.bulletPoints = bulletPoints;
			this.bulletIcon = bulletIcon;
			
		}

// This list of for Carousel Mix and Match LineOptions
	public String getLines() {
		return lines;
	}

	public String getoptionsIcon() {
		return optionsIcon;
	}


	// This list of for Carousel Mix and Match LineOptions
	public String getPlanIcon() {
		return planIcon;
	}
    
	public String getPlanUnit() {
		return planUnit;
	}
	public String getMndMPlanName() {
		return mndmplanName;
	}


	public Double getScratchPrice(){
		return scratchPrice;
	}

	public Double getLine1(){
		return line1;
	}

	public Double getLine2(){
		return line2;
	}
	public Double getLine3(){
		return line3;
	}
	public Double getLine4(){
		return line4;
	}
	public Double getLine5(){
		return line5;
	}

	public String getPlanName() {
		return planName;
	}
    
	public String getPlanID() {
		return planID;
	}

	public String getPlanDescription() {
		return planDescription;
	}

	public String getRate() {
		return rate;
	}

	public ReactListItemImpl() {
	}

	@Override
	public String getExportedType() {
		return null;
	}

	@Override
	public String getId() {
		return id;
	}

	@Override
	public String getIcon() {
		return icon;
	}

	@Override
	public String getIconAlt() {
		return iconAlt;
	}

	@Override
	public String getTitle() {
		return title;
	}

	@Override
	public String getDescription() {
		try {
			if (StringUtils.isNotEmpty(description) && description.indexOf("href=") != -1) {
				description = description.indexOf(CONTEXT_URL) != -1 ? description.replace(CONTEXT_URL, "")
						: description;
				description = description.indexOf(CORPORATE_PAGES_URL) != -1
						? description.replace(CORPORATE_PAGES_URL, "")
						: description;
			}
		} catch (Exception e) {
			LOG.error("Exception while shortening url for the description : {}", e.getMessage());
		}
		return description;
	}

	@Override
	public String getAnchor() {
		return anchor;
	}

	@Override
	public String getLink() throws LoginException{
		if(StringUtils.isNotEmpty(link)) {
			/*
			 * link = link.indexOf(CONTEXT_URL)!=1 ? link.replace(CONTEXT_URL, "") : link;
			 * link = link.indexOf(CORPORATE_PAGES_URL)!=1 ?
			 * link.replace(CORPORATE_PAGES_URL, "") : link;
			 */
			if(null!=externalizerService) {
	    		link = externalizerService.getExternalizerUrl(link);
	    	}
    	}
		return link;
	}

	@Override
	public String getOpeninnewtab() {
		return openinnewtab;
	}

	@Override
	public void setIcon(String icon) {
		this.icon = icon;
		
	}

	@Override
	public void setListDescription(String listDescription) {
		this.listDescription = listDescription;
		
	}

	@Override
	public String getListDescription() {
		return listDescription;
	}

	@ValueMapValue
	private String lob;
	
	@Override
	public String getLob() {
		return lob;
	}

	public String getFragmentId() {
		return fragmentId;
	}

	public String getAccordionIcon() {
		return accordionIcon;
	}

	public String getAccordionIconAlttxt() {
		return accordionIconAlttxt;
	}

	public String getTitle1() {
		return title1;
	}

	public String getTitle2() {
		return title2;
	}

	public String getTitleColor() {
		return titleColor;
	}

	public String[] getBulletPoints() {
		return bulletPoints;
	}

	public String getBulletIcon() {
		return bulletIcon;
	}

	@Override
	public Boolean getUseRTE() {
		return useRTE;
	}
	
}