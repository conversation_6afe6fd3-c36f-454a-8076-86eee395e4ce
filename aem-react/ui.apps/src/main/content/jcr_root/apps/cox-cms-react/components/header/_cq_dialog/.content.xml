<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="React Header"
    sling:resourceType="cq/gui/components/authoring/dialog"
    extraClientlibs="[cq.standard.header.authoring.dialog, cq.react.globalnav.authoring.dialog, cq.header.authoring.dialog]"
    helpPath="en/cq/current/wcm/default_components.html#Text">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs">
                <items jcr:primaryType="nt:unstructured">
                    <globalNav
                        cq:showOnCreate="{Boolean}false"
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Global Nav"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="/apps/cox-cms-react/components/globalNav/cq:dialog/content/items/tabs/items/tab1"/>
                    <tab1
                        jcr:primaryType="nt:unstructured"
                        jcr:title="React Header"
                        sling:resourceType="granite/ui/components/coral/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <lob
                                                granite:class="teaserType cq-dialog-dropdown-showhide"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                fieldLabel="LOB"
                                                name="./lob">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".teasertype-showhide-target"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <select
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Select"
                                                        value="select"/>
                                                    <cox-resi
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Residential"
                                                        value="cox-resi"/>
                                                    <cox-busi
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Business"
                                                        value="cox-busi"/>
                                                </items>
                                            </lob>
                                            <minimalHeader
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./minimalHeader"
                                                text="is Minimal Header"
                                                value="{Boolean}true"/>
                                            <hide_contact_us
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./displayContactUs"
                                                text="Display Contact us"
                                                value="{Boolean}true"/>
                                            <hide_contact_us-delete
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/hidden"
                                                name="./displayContactUs@Delete"
                                                value="{Boolean}true"/>
                                            <hide_shopping_cart
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./displayShoppingCart"
                                                text="Display Shopping Cart"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
                                                value="{Boolean}true"/>
                                            <hide_shopping_cart-delete
                                                jcr:primaryType="nt:unstructured"
                                                name="./displayShoppingCart@Delete"
                                                value="{Boolean}true"/>
                                            <hide_sign_in_out
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./displaySignInOut"
                                                text="Display Sign in-out"
                                                value="{Boolean}true"/>
                                            <hide_sign_in_out-delete
                                                jcr:primaryType="nt:unstructured"
                                                name="./displaySignInOut@Delete"
                                                value="{Boolean}true"/>
                                            <displayLocation
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./displayLocation"
                                                text="Display Location"
                                                uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
                                                value="{Boolean}true"/>
                                            <displayLocation-delete
                                                jcr:primaryType="nt:unstructured"
                                                name="./displayLocation@Delete"
                                                value="{Boolean}true"/>
                                            <new_navigation
                                                granite:class="is-new-nav"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./newNavigation"
                                                text="New Navigation"
                                                value="{Boolean}true"/>
                                            <okta_authentication
                                                granite:class="is-okta-authentication"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./oktaAuthentication"
                                                text="OKTA Authentication?"
                                                value="{Boolean}true"/>
                                            <hide_search
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./hideSearch"
                                                text="Hide Search"
                                                value="{Boolean}true"/>
                                            <hide_search-delete
                                                jcr:primaryType="nt:unstructured"
                                                name="./hideSearch@Delete"
                                                value="{Boolean}true"/>
                                           <bottomHeader
					                            jcr:primaryType="nt:unstructured"
					                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
					                            name="./bottomHeader"
					                            text="Bottom Spacing"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
					                            value="{Boolean}true"/>
                                            <signin_url
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Url for redirect after Sign in. Enter $currentUrl to stay on current page or leave blank to go to My Account Page."
                                                fieldLabel="Sign In Url"
                                                name="./signInUrl"/>
                                            <target_geo_location
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                fieldDescription="Check to enable Target Geo Location"
                                                name="./targetGeoLocation"
                                                text="Target Geo Location"
                                                value="{Boolean}true"/>
                                            <target_geo_location-delete
                                                jcr:primaryType="nt:unstructured"
                                                fieldDescription="Check to enable Target Geo Location"
                                                name="./targetGeoLocation@Delete"
                                                value="{Boolean}true">
                                                <hide_search
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                    name="./hideSearch"
                                                    text="Hide Search"
                                                    value="{Boolean}true"/>
                                            </target_geo_location-delete>
                                            <notification_url
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="URL to redirect the page to bell notification landing page"
                                                fieldLabel="Bell Notification Landing Page"
                                                name="./notificationUrl"/>
                                            <notification_view_all_url
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="URL for View All link"
                                                fieldLabel="Bell Notification View All Landing Page"
                                                name="./notificationViewAllUrl"/>
                                            <shown-rte-for-un-authenticated
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./showRTEforUnauthenticated"
                                                text="Show RTE for UnAuthenticated Users"
                                                value="{Boolean}true"/>
                                            <shown-rte-for-un-authenticated-delete
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/hidden"
                                                name="./showRTEforUnauthenticated@Delete"
                                                value="{Boolean}true"/>
                                            <show-diff-myaccount-ment-for-authenticated
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./showDiffMenuForAuthenticated"
                                                text="Show Different My Account Menu for Authenticated Users"
                                                value="{Boolean}true"/>
                                            <show-diff-myaccount-ment-for-authenticated-delete
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/hidden"
                                                name="./showDiffMenuForAuthenticated@Delete"
                                                value="{Boolean}true"/>
                                            <keep-myaccount-menu-open-after-authentication
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                name="./keepMenuOpenAfterAuthentication"
                                                text="Keep My Account Menu Open After Authentication"
                                                value="{Boolean}true"/>
                                            <keep-myaccount-menu-open-after-authentication-delete
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/hidden"
                                                name="./keepMenuOpenAfterAuthentication@Delete"
                                                value="{Boolean}true"/>
                                            <myaccount-mobile-icon
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldDescription="Icon for My account on Mobile"
                                                fieldLabel="My Account Mobile Icon"
                                                name="./myaccountmobileicon"
                                                rootPath="/content/dam/cox"/>
                                            <contactus-mobile-icon
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldDescription="Icon for Contact Us on Mobile"
                                                fieldLabel="Contact Us Mobile Icon"
                                                name="./contactusmobileicon"
                                                rootPath="/content/dam/cox"/>
								            <shoppingCart
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Shopping Cart"
								                name="./shoppingCart"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/cart-sky-blue.svg"/>
                							<shoppingcart_url
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Shopping cart page url."
                                                fieldLabel="Shopping Cart Page Url"
                                                name="./shoppingCartUrl"/>
								            <logo
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Logo"
								                name="./logo"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/cox_logo.png"/>
								            <signIn
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Sign In"
								                name="./signIn"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/profile.svg"/>
								            <search
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Search"
								                name="./search"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/magnifying-glass.svg"/>
								            <bellnotification
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Bell Notification"
								                name="./bellnotification"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/bell.svg"/>
								            <locationPin
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Location Pin"
								                name="./locationPin"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/location-pin-black.svg"/>
								            <geoMenu
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="GeoLocation Menu"
								                name="./geoMenu"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/close-btn.svg"/>
								            <geoError
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Geolocation Error"
								                name="./geoError"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/error-triangle.svg"/>
								            <geoFormFields
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Geolocation Form Fields"
								                name="./geoFormFields"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/chevron-down-blue.svg"/>
								            <timesCircle
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Times Circle"
								                name="./timesCircle"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/times-circle.svg"/>
								            <timesCircleWhite
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Times Circle White"
								                name="./timesCircleWhite"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components/times-circle-white.svg"/>
								            <globalNavMenu
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="Global Nav Menu"
								                name="./globalNavMenu"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components"/>
								            <signInOverlayMenu
								                jcr:primaryType="nt:unstructured"
								                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
								                fieldLabel="SignIn Overlay Menu"
								                name="./SignInOverlayMenu"
								                rootPath="/content/dam/cox/"
                								value="/content/dam/cox/common/icons/ui_components"/>
                                            <myAccountMobileIconAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="My Account Mobile Icon AltTxt"
                                                fieldLabel="My Account Mobile Icon AltTxt"
                                                name="./myAccountMobileIconAltTxt"/>
                                            <contactUsMobileIconAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Contact Us Mobile Icon AltTxt"
                                                fieldLabel="Contact Us Mobile Icon AltTxt"
                                                name="./contactUsMobileIconAltTxt"/>
                                            <shoppingCartAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Shopping Cart AltTxt"
                                                fieldLabel="Shopping Cart AltTxt"
                                                name="./shoppingCartAltTxt"/>
                                            <logoAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Logo AltTxt"
                                                fieldLabel="Logo AltTxt"
                                                name="./logoAltTxt"/>
                                            <signInAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="SignIn AltTxt"
                                                fieldLabel="SignIn AltTxt"
                                                name="./signInAltTxt"/>
                                            <searchAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Search AltTxt"
                                                fieldLabel="Search AltTxt"
                                                name="./searchAltTxt"/>
                                            <bellNotificationAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldDescription="Bell Notification AltTxt"
                                                fieldLabel="Bell Notification AltTxt"
                                                name="./bellNotificationAltTxt"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tab1>
                    <geoLocationTab1
                        cq:showOnCreate="{Boolean}false"
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Geo Location"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="/apps/cox-cms-react/components/geolocation/cq:dialog/content/items/tabs/items/tab1"/>
                    <reactsearch
                        cq:showOnCreate="{Boolean}false"
                        jcr:primaryType="nt:unstructured"
                        jcr:title="React Search"
                        sling:resourceType="granite/ui/components/coral/foundation/include"
                        path="/apps/cox-cms-react/components/search/cq:dialog/content/items/tabs/items/properties"/>
                    <extendedudo
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Extended UDO"
                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                        fieldDescription="For Example App POC"
                        fieldLabel="Contexts"
                        name="./extendedudo"/>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
