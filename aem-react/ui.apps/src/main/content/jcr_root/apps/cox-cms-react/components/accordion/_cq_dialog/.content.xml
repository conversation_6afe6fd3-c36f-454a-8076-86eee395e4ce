<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Accordion"
    sling:resourceType="cq/gui/components/authoring/dialog"
    extraClientlibs="[core.wcm.components.commons.editor.dialog.childreneditor.v1, core.wcm.components.carousel.v1.editor, core.wcm.components.accordion.v1.editor, cq.accordion.authoring.dialog]">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
        <items jcr:primaryType="nt:unstructured">
            <tabs jcr:primaryType="nt:unstructured">
                <items jcr:primaryType="nt:unstructured">
                    <containerItems jcr:primaryType="nt:unstructured">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <lob
                                                granite:class="teaserType cq-dialog-dropdown-showhide"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                fieldLabel="LOB"
                                                name="./lob">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".teasertype-showhide-target"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <select
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Select"
                                                        value="select"/>
                                                    <cox-resi
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Residential"
                                                        value="cox-resi"/>
                                                    <cox-busi
                                                        jcr:primaryType="nt:unstructured"
                                                        text="Business"
                                                        value="cox-busi"/>
                                                </items>
                                            </lob>
                                             <enableFAQs
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}false"
                                                name="./enableFAQs"
                                                text="FAQ"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                            <fragmentPath
                                                jcr:primaryType="nt:unstructured"
                                                jcr:title="Content Fragment"
                                                sling:resourceType="granite/ui/components/coral/foundation/include"
                                                path="/libs/core/wcm/components/contentfragment/v1/contentfragment/cq:dialog/content/items/tabs/items/properties/items/column/items/fragmentPath"/>
                                            <cfaccordions
                                                granite:class="showcontentfragments"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                fieldLabel="Fragment Accordions">
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    fieldLabel="Fragments"
                                                    name="./cfaccordions">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <cffragmentPath
                                                            jcr:primaryType="nt:unstructured"
                                                            jcr:title="Content Fragment"
                                                            sling:resourceType="granite/ui/components/coral/foundation/include"
                                                            path="/libs/core/wcm/components/contentfragment/v1/contentfragment/cq:dialog/content/items/tabs/items/properties/items/column/items/fragmentPath"/>
                                                    </items>
                                                </field>
                                            </cfaccordions>
                                            <manualitems
                                                granite:class="&#x9;&#xa;showmanualitems"
                                                jcr:primaryType="nt:unstructured"
                                                sling:orderBefore="_coral-Multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                fieldDescription="Select this to provide manual items instead of Content Fragment."
                                                name="./manualitems"
                                                text="Manual Items"
                                                uncheckedValue="false"
                                                value="{Boolean}true"/>
                                            <fragmentaccordions
                                                granite:class="&#x9;&#xa;showfragments"
                                                jcr:primaryType="nt:unstructured"
                                                sling:orderBefore="_coral-Multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                fieldDescription="Select this to provide Fragment items instead of manual items."
                                                name="./fragmentaccordions"
                                                text="Fragment Accordions"
                                                uncheckedValue="false"
                                                value="{Boolean}true"/>
                                            <offercardaccordions
                                                granite:class="&#x9;&#xa;offercards"
                                                jcr:primaryType="nt:unstructured"
                                                sling:orderBefore="_coral-Multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                fieldDescription="Select this to provide Fragment items instead of manual items."
                                                name="./offercardaccordions"
                                                text="Offer Card Accordions"
                                                uncheckedValue="false"
                                                value="{Boolean}true"/>
                                             <isHideMCP
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}false"
                                                name="./isHideMCP"
                                                text="Hide In MCP"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                            <tag
                                                granite:class="tag-cq-dialog-dropdown-showhide"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                fieldLabel="Tag"
                                                name="./tag">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    cq-dialog-dropdown-showhide-target=".tag-showhide-target"/>
                                                <items jcr:primaryType="nt:unstructured">
                                                    <h1
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H1"
                                                        value="h1"/>
                                                    <h2
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H2"
                                                        value="h2"/>
                                                    <h3
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H3"
                                                        value="h3"/>
                                                    <h4
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H4"
                                                        value="h4"/>
                                                    <h5
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H5"
                                                        value="h5"/>
                                                    <h6
                                                        jcr:primaryType="nt:unstructured"
                                                        text="H6"
                                                        value="h6"/>
                                                    <p
                                                        jcr:primaryType="nt:unstructured"
                                                        text="P"
                                                        value="p"/>
                                                </items>
                                            </tag>
                                            <accordionIcon
                                                granite:class="accordionIcon"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Accordion Icon"
                                                name="./accordionIcon"
                                                rootPath="/content/dam/cox/"
                                                value="/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg"/>
                                            <top
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}true"
                                                name="./top"
                                                text="Top"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                            <bottom
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}true"
                                                name="./bottom"
                                                text="Bottom"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                            <right
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}true"
                                                name="./right"
                                                text="Right"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                            <left
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                checked="{Boolean}true"
                                                name="./left"
                                                text="Left"
                                                uncheckedValue="{Boolean}false"
                                                value="{Boolean}true"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </containerItems>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
