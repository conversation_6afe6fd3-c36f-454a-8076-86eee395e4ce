
console.log("Video Listener Loaded");
 (function(document, $) {
     "use strict";
     var compNameFlag=false;
     $(document).on("dialog-ready", function() {
    	 var compName=$(".coral-Form--vertical.cq-dialog").attr("action");
         if(compName!=null){
        	var compNameArray=compName.split("/");
        	compNameFlag=(compNameArray[compNameArray.length-1]).includes("embed");
        	hideMute();
          }   
     })
     
    $(document).on("change", "[name='./embeddableResourceType']", function(e) {
    	hideMute();
    })
    function hideMute(){
		if(compNameFlag){ 
    		setTimeout(function(){
            	$("[name='./youtubeMute']").parents(".coral-Form-fieldwrapper").remove();
    		}, 500);        	
         }
	}
    
    $(document).on("click", ".cq-dialog-submit", function(e) {
  		if(compNameFlag){
            window.location.reload();
  	     }
  	});
    console.log("Video Listener Ended");
 })(document, Granite.$);