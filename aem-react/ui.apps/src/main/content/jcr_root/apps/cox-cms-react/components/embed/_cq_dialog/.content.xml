<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="React Video"
    sling:resourceType="cq/gui/components/authoring/dialog"
    helpPath="en/cq/current/wcm/default_components.html#Text"
    extraClientlibs="[cq.embed.authoring.dialog]">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs">
                <items jcr:primaryType="nt:unstructured">
                    <properties
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Video"
                        sling:resourceType="granite/ui/components/coral/foundation/container">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <videoAltTxt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Video Alt Text"
                                                name="./videoAltTxt"/>
                                            <videoIcon
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathbrowser"
                                                fieldDescription="Select the path of the image for video icon"
                                                fieldLabel="Video Icon"
                                                name="./videoIcon"
                                                rootPath="/content/dam/cox/common/icons/ui_components/"
                								value="/content/dam/cox/common/icons/ui_components/video-icon-lg.svg"/>
                                            <top
					                            jcr:primaryType="nt:unstructured"
					                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
					                            name="./top"
					                            text="Top"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
					                            value="{Boolean}true"/>
                                           <bottom
					                            jcr:primaryType="nt:unstructured"
					                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
					                            name="./bottom"
					                            text="Bottom"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
					                            value="{Boolean}true"/>
											<right
					                            jcr:primaryType="nt:unstructured"
					                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
					                            name="./right"
					                            text="Right"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
					                            value="{Boolean}true"/>
                                           <left
					                            jcr:primaryType="nt:unstructured"
					                            sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
					                            name="./left"
					                            text="Left"
					                            uncheckedValue="{Boolean}false"
												checked="{Boolean}true"
					                            value="{Boolean}true"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </properties>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
