{"name": "cox-cms-react", "version": "0.1.0", "private": true, "scripts": {"start": "craco start", "build": "craco build && clientlib", "test": "craco test", "test:coverage": "craco test --watchAll=false --coverage", "test:snapshots": "craco test --watchAll=false -u", "eject": "craco eject", "sync": "aemsync -d -w ../ui.apps/src/main/content", "refresh": "rimraf node_modules && rimraf pnpm-lock.yaml && pnpm install", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "format": "prettier --write .", "storybook": "storybook dev -p 6007", "build-storybook": "storybook build", "analyze": "source-map-explorer 'build/static/js/*.js'"}, "dependencies": {"@adobe/aem-core-components-react-base": "^1.1.8", "@adobe/aem-core-components-react-spa": "^1.2.0", "@adobe/aem-react-editable-components": "^1.1.6", "@adobe/aem-spa-component-mapping": "^1.1.1", "@adobe/aem-spa-page-model-manager": "^1.4.0", "@babel/core": "^7.0.0-0", "@cox/core-ui8": "6.0.732", "@popperjs/core": "^2.11.6", "axios": "^1.3.2", "bootstrap": "^5.2.2", "custom-event-polyfill": "^1.0.7", "history": "^4.10.1", "html-react-parser": "^3.0.6", "immer": "^9.0.21", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "react": "^18.3.1", "react-app-polyfill": "^1.0.5", "react-bootstrap": "2.7.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-multi-carousel": "^2.8.5", "react-router": "6.6.1", "react-router-dom": "^6.25.1", "react-scripts": "5.0.1", "sass": "^1.57.1", "styled-components": "^5.3.10", "use-immer": "^0.9.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@axe-core/react": "^4.7.3", "@babel/plugin-syntax-flow": "7.14.5", "@babel/plugin-transform-react-jsx": "7.14.9", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "@craco/craco": "^7.1.0", "@storybook/addon-essentials": "7.0.2", "@storybook/addon-interactions": "7.0.2", "@storybook/addon-links": "7.0.2", "@storybook/addon-onboarding": "1.0.8", "@storybook/blocks": "7.0.2", "@storybook/preset-create-react-app": "7.0.2", "@storybook/react": "7.0.2", "@storybook/react-webpack5": "7.0.2", "@storybook/theming": "^7.0.2", "@testing-library/dom": "^7.21.4", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^7.1.2", "@types/google.maps": "^3.54.10", "@types/jest": "^27.5.2", "@types/js-cookie": "^3.0.2", "@types/lodash": "4.14.168", "@types/node": "^16.18.11", "@types/react": "^18.3.3", "@types/react-bootstrap": "^0.32.32", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@types/react-test-renderer": "^18.0.0", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "@welldone-software/why-did-you-render": "^7.0.1", "aem-clientlib-generator": "^1.8.0", "aemsync": "^4.0.0", "babel-plugin-named-exports-order": "0.0.2", "clean-webpack-plugin": "^0.1.19", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-testing-library": "^5.10.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-property-matchers": "^0.1.2", "jest-styled-components": "^7.1.1", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^13.1.0", "prettier": "^2.8.3", "prop-types": "15.8.1", "react-test-renderer": "^18.3.1", "source-map-explorer": "^2.5.3", "storybook": "7.0.2", "typescript": "^4.8.4", "webpack": "5.89.0"}, "proxy": "http://localhost:4502", "browserslist": ["defaults"], "eslintConfig": {"extends": ["react-app", "plugin:storybook/recommended"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["pnpm format", "pnpm lint", "pnpm test --silent -- --watchAll=false --json --passWithNoTests -u"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!axios)/"], "collectCoverageFrom": ["!/node_modules", "src/**/*.{js,jsx,ts,tsx}"]}, "overrides": {"axios": "1.2.6"}}