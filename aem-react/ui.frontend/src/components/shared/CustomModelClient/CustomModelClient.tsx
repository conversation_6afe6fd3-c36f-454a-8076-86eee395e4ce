import { ModelClient } from '@adobe/aem-spa-page-model-manager'

import {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  APP_HOST,
  COX_DOMAINS,
  HOST,
  HREF,
  isLocalHost,
  ORIGIN,
  PATHNAME,
  REACT_APP_AEM_AUTHORIZATION_HEADER,
  VDF_MODEL_PATH
} from '../../../constants'
import { getLocalToEnv } from '../../../utils/local-util'

export default class CustomModelClient extends ModelClient {
  fetch(modelPath: any) {
    if (!modelPath) {
      const err = `Fetching model rejected for path: ${modelPath}`
      return Promise.reject(new Error(err))
    }

    const replaceHtmlWithJson = (url: any) => {
      if (!url) return ''
      let str = url
      const index = str.lastIndexOf('.html')
      if (index !== -1) {
        str = str.slice(0, index) + str.slice(index).replace('.html', '.model.json')
      }
      str.replace('/editor.html', '')
      return str
    }
    const jsonUrl = replaceHtmlWithJson(PATHNAME)
    const options: any = {}
    options.credentials = 'include'
    let newUrl: any

    const queryParameters = new URLSearchParams(window.location.search)
    const zip = queryParameters.get('zip')
    const zipParam = zip ? `&zip=${zip}` : ''

    if (isLocalHost() && HOST !== 'localhost:4502') {
      options.headers = {
        Authorization: REACT_APP_AEM_AUTHORIZATION_HEADER
      }
      // use below line for accessing localhost:4502
      // newUrl = `${APP_HOST}${jsonUrl}`

      // use below line for accessing long urls
      // newUrl = `${getLocalToEnv()}${jsonUrl}`

      // use below line for accessing short urls
      newUrl = `${getLocalToEnv()}${VDF_MODEL_PATH}?requestUrl=${getLocalToEnv()}${PATHNAME}${zipParam}`
    } else if (HOST.includes('s3.amazonaws.com')) {
      newUrl = `${COX_DOMAINS.DEV}${jsonUrl}`
    } else if (
      HOST === 'localhost:4502' ||
      HOST.includes('author') ||
      HOST.includes('publish') ||
      //PATHNAME.includes('/en') ||
      PATHNAME.includes('/content/cox')
    ) {
      // use below line for accessing long url's for MASTER
      newUrl = `${ORIGIN}${jsonUrl}`
    } else {
      // use below line for accessing short url's for MASTER
      newUrl = `${ORIGIN}${VDF_MODEL_PATH}?requestUrl=${HREF}${zipParam}`
    }
    return fetch(newUrl, options)
      .then((response) => {
        if (response?.status >= 200 && response?.status < 512) {
          return response.json()
        }
        return Promise.reject('Network Error')
      })
      .catch((error) => {
        console.error('🚀  ~ error:', error)
        return Promise.reject(error)
      })
  }
}
