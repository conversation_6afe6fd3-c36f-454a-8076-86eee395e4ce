import IsInEditorText from './IsInEditorText'

describe('isInEditorText', () => {
  test('renders null when isInEditor is false', () => {
    expect(IsInEditorText(false, 'text')).toBeNull()
  })
  test('renders editor text with error message when isInEditor is true and errorMessage is provided', () => {
    expect(IsInEditorText(true, 'text', 'errorMessage')).toMatchInlineSnapshot(`
      <div
        className="editor-error-text"
      >
        text
         
        errorMessage
      </div>
    `)
  })
  test('renders editor text with custom message when isInEditor is true and isCustomMessage is true', () => {
    expect(IsInEditorText(true, 'text', undefined, true)).toMatchInlineSnapshot(`
      <div
        className="editor-text"
      >
        text
      </div>
    `)
  })
  test('renders editor text with default message when isInEditor is true and errorMessage and isCustomMessage are not provided', () => {
    expect(IsInEditorText(true, 'text')).toMatchSnapshot()
  })
})
