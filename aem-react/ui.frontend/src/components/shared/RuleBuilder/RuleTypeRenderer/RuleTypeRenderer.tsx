import { useEffect, useState } from 'react'

import { RuleType } from '@cox/core-ui8/dist/types'
import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'

import { triggerNewRelicAction } from '../../../../utils/newrelic-util'
import RuleResultsRenderer from '../RuleResultsRenderer'

const RuleTypeRenderer = (props: {
  ruleId: string
  resultsTrue: any
  resultsFalse: any
  ruleType: string
  condition?: string
  ruleExpression?: string
  isInEditor?: boolean
  componentTypeRenderer?: any
  compName?: string
  defaultVariationContent?: any
  showDefaultContent?: boolean
}) => {
  const {
    ruleId,
    resultsTrue,
    resultsFalse,
    ruleType,
    condition = '',
    ruleExpression = '',
    isInEditor = false,
    componentTypeRenderer,
    compName = '',
    defaultVariationContent,
    showDefaultContent
  } = props
  const flexOffers = useFlexOffers(ruleExpression)
  const [results, setResults] = useState([])
  const [error, setError] = useState<any>()

  useEffect(() => {
    const utagData: any = window?.utag_data
    if (utagData && Object.keys(utagData).length > 0 && ruleId) {
      utagData.rulesRendered?.length > 0 ? utagData.rulesRendered?.push(ruleId) : (utagData.rulesRendered = [ruleId])
    }
    if (isInEditor) {
      setResults(resultsTrue)
      return
    }
    if (ruleType === RuleType.pricing) {
      if ((flexOffers && flexOffers?.success && flexOffers?.offers?.length > 0) || flexOffers?.loading) {
        setResults(resultsTrue)
      } else if (showDefaultContent && flexOffers?.error) {
        setResults(defaultVariationContent)
      } else if (flexOffers?.error) {
        setResults(resultsFalse)
      }
    } else if (ruleType === RuleType.udoconditional) {
      setResults(resultsTrue || resultsFalse)
    } else if (ruleType === RuleType.conditional) {
      try {
        if (eval(ruleExpression)) {
          setResults(resultsTrue)
        } else {
          setResults(resultsFalse)
        }
      } catch (err) {
        setError(err)
        console.error(err)
        triggerNewRelicAction({
          errorMessage: err,
          errorName: `Invalid ${ruleType} Rule Expression in ${compName}`,
          actionName: 'Rule Expression Error'
        })
      }
    }
  }, [flexOffers, window?.utag_data?.responsiveDisplayType, window?.utag_data?.visitorType])

  if (error) {
    // This error is handled by the ErrorBoundary in the parent component.
    return <>{error}</>
  }

  if (results?.length > 0 || (results && Object.keys(results).length > 0)) {
    return (
      <div id='rule-results' className='rule-results' data-testid={ruleId}>
        <RuleResultsRenderer
          ruleId={ruleId}
          results={results}
          condition={condition as any}
          ruleType={ruleType as RuleType}
          ruleExpression={ruleExpression}
          isInEditor={isInEditor}
          componentTypeRenderer={componentTypeRenderer}
        />
      </div>
    )
  }
  return <></>
}
export default RuleTypeRenderer
