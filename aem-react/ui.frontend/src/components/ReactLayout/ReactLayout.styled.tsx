import styled from 'styled-components'

import { LayoutBackgroundTypes, ReactLayoutProps, VerticalAlignment } from './types'
import { getImageSrc } from '../../utils/local-util'

export const ReactLayoutStyled = styled.div<ReactLayoutProps>`
  background: ${(props) =>
    props.layoutBackground === LayoutBackgroundTypes.image
      ? `url('${getImageSrc(props.backgroundImage)}') center/ cover no-repeat`
      : props.layoutBackground === LayoutBackgroundTypes.transparent
      ? `transparent`
      : undefined};

  aspect-ratio: ${(props) => (props.layoutBackground === LayoutBackgroundTypes.image ? '16/9' : undefined)};
  align-items: ${(props) =>
    props.verticalAlignment ? `${VerticalAlignment[props.verticalAlignment as keyof typeof VerticalAlignment]}` : ``};
`
