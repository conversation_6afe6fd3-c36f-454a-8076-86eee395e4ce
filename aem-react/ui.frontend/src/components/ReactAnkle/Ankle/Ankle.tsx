import ReactImage from '../../ReactImage'
import './Ankle.scss'
import ReactText from '../../ReactText'
import { ReactAnkleProps, Toasterdetail } from '../types'

const Ankle = (props: ReactAnkleProps): JSX.Element => {
  const {
    toasterdetail = [
      {
        icon: '',
        headline: '',
        description: '',
        alttext: '',
        targeturl: '',
        hideinmobile: false
      }
    ]
  } = props

  if (toasterdetail && toasterdetail.length > 0) {
    return (
      <div className='ankle__wrapper' data-testid='ankle__wrapper'>
        <div className='container'>
          <div data-testid='ankle__container' className='ankle__container'>
            {toasterdetail?.map((item: Toasterdetail, index: number) => {
              const { icon, description, headline, alttext, targeturl = '' } = item
              return (
                <div key={index.toString()} className='ankle__item' data-testid='ankle__item' id={index.toString()}>
                  <a href={targeturl} className='ankle__item-link' aria-label={alttext}>
                    <div className='ankle__item-inner'>
                      <ReactImage src={icon} imgCss='ankle__icon' asIcon />
                      <div className='ankle__content'>
                        <div className='ankle__label'>{headline}</div>
                        <ReactText className='ankle__action' text={description} />
                      </div>
                    </div>
                  </a>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  } else return <></>
}
export default Ankle
