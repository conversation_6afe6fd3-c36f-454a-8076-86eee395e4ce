import { ButtonTypes, LinkTypes } from '@cox/core-ui8/dist/Button'
import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'
import { useWindowSize } from '@cox/core-ui8/dist/useWindowSize'

import ReactProductTeaserSkeleton from './ReactProductTeaserSkeleton'
import { DynamicMediaComponents, getDynamicMediaFromSrc } from '../../../utils/dynamic-media-util'
import { getAttributeValue, hasCFConditionals } from '../../../utils/rule-builder-util'
import { getAnchorInfo } from '../../../utils/teaser-util'
import { ScreenSizes } from '../../../utils/types'
import ReactBadge from '../../ReactBadge'
import ReactButton from '../../ReactButton'
import ReactList, { ListType } from '../../ReactList'
import ReactPrice, { PriceVariant } from '../../ReactPrice'
import ReactText from '../../ReactText'
import { ReactProductTeaserProps, TeaserType } from '../types'
import './ReactProductTeaser.scss'

const ReactProductTeaser = (props: ReactProductTeaserProps): JSX.Element => {
  const { id, elements, ruleExpression = '', cardType, tokenProperties, priceTagIcon, index, priceRef, priceHeight } = props
  const flexOffers = useFlexOffers(ruleExpression)
  const {
    image = { value: '' },
    imageParams = { value: '' },
    altImage = { value: '' },
    contentBadgeColor = { value: '' },
    contentBadgeText = { value: '' },
    eyebrowIcon = { value: '' },
    eyebrowIconAltText = { value: '' },
    eyebrow = { value: '', title: '' },
    heading = { value: '' },
    additionalNote = { value: '' },
    description = { value: '' },
    bulletPoints = { value: [] },
    planName = { value: '' },
    price = { value: '' },
    scratchPrice = { value: '' },
    term = { value: '' },
    discount = { value: '' },
    /* PRIMARY BUTTON */
    buttonTextPrimary = { value: '' },
    buttonLinkPrimary = { value: '' },
    buttonLinkTypePrimary = { value: '' },
    buttonLinkNewTabPrimary = { value: '' },
    linkTypeButtonPrimary = { value: '' },
    linkIdButtonPrimary = { value: '' },
    /* SECONDARY BUTTON */
    buttonTextSecondary = { value: '' },
    buttonLinkSecondary = { value: '' },
    buttonLinkNewTabSecondary = { value: '' },
    buttonLinkTypeSecondary = { value: '' },
    linkTypeButtonSecondary = { value: '' },
    linkIdButtonSecondary = { value: '' },
    /* TERTIARY BUTTON */
    buttonTextTertiary = { value: '' },
    buttonLinkTertiary = { value: '' },
    buttonLinkNewTabTertiary = { value: '' },
    buttonLinkTypeTertiary = { value: '' },
    linkTypeButtonTertiary = { value: '' },
    linkIdButtonTertiary = { value: '' }
  } = elements
  const {
    hasAnchorLink: primaryButtonHasAnchorLink,
    anchorUrl: primaryButtonAnchorLink,
    anchorId: primaryButtonAnchorId
  } = getAnchorInfo(buttonLinkPrimary?.value)
  const {
    hasAnchorLink: secondaryButtonHasAnchorLink,
    anchorUrl: secondaryButtonAnchorLink,
    anchorId: secondaryButtonAnchorId
  } = getAnchorInfo(buttonLinkSecondary?.value)
  const {
    hasAnchorLink: tertiaryButtonHasAnchorLink,
    anchorUrl: tertiaryButtonAnchorLink,
    anchorId: tertiaryButtonAnchorId
  } = getAnchorInfo(buttonLinkTertiary?.value)

  const { width: windowWidth } = useWindowSize()

  const isLargeScreen = windowWidth >= ScreenSizes.LG

  const getDynamicPrice = () => {
    if (priceValue) {
      if (isLargeScreen) {
        return (
          <div ref={(ref: any) => ((priceRef.current[index as any] as any) = ref)} style={{ minHeight: `${priceHeight}px` }}>
            <ReactPrice
              variation={PriceVariant.DEFAULT}
              price={priceValue}
              scratchPrice={scratchPriceValue}
              term={termValue}
              additionalNote={discountValue}
              priceTagIcon={priceTagIcon}
              cardType={cardType}
            />
          </div>
        )
      }
      return (
        <ReactPrice
          variant={PriceVariant.DEFAULT}
          price={priceValue}
          scratchPrice={scratchPriceValue}
          term={termValue}
          additionalNote={discountValue}
          priceTagIcon={priceTagIcon}
          cardType={cardType}
        />
      )
    }
    return <></>
  }
  const getValue = (attribute: any, propertyName?: string) => {
    if (hasCFConditionals(attribute)) {
      const jsonAttribute = (elements as any)?.[`${propertyName}_json`]
      return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
    }
    return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
  }
  const getDynamicMediaComponentName = () => {
    if (cardType === TeaserType.imagethreeup || cardType === TeaserType.exposedthreeup) {
      return DynamicMediaComponents.TEASER_IMAGE_THREE_UP
    } else if (cardType === TeaserType.imagetwoup || cardType === TeaserType.exposedtwoup) {
      return DynamicMediaComponents.TEASER_IMAGE_TWO_UP
    } else if (cardType === TeaserType.featured) {
      return DynamicMediaComponents.PRODUCT_TEASER_FEATURED
    } else {
      return DynamicMediaComponents.TEASER
    }
  }

  if (flexOffers?.loading) {
    return <ReactProductTeaserSkeleton />
  }
  const contentBadgeTextValue = getValue(contentBadgeText, 'contentBadgeText')
  const contentBadgeColorValue = getValue(contentBadgeColor, 'contentBadgeColor')
  const altImageValue = getValue(altImage, 'altImage')
  const imageParamsValue = getValue(imageParams, 'imageParams')
  const imageValue = getDynamicMediaFromSrc(
    getValue(image) as string,
    (imageParamsValue as DynamicMediaComponents) || getDynamicMediaComponentName()
  )
  const eyebrowIconValue = getDynamicMediaFromSrc(getValue(eyebrowIcon) as string, getDynamicMediaComponentName())
  const eyebrowIconAltTextValue = getValue(eyebrowIconAltText, 'eyebrowIconAltText')
  const eyebrowValue = getValue(eyebrow, 'eyebrow')
  const headingValue = getValue(heading, 'heading')
  const additionalNoteValue = getValue(additionalNote, 'additionalNote')
  const descriptionValue = getValue(description, 'description')
  const planNameValue = getValue(planName, 'planName')
  const priceValue = getValue(price, 'price')
  const scratchPriceValue = getValue(scratchPrice, 'scratchPrice')
  const termValue = getValue(term, 'term')
  const discountValue = getValue(discount, 'discount')
  const hasEyebrowIcon = eyebrowIcon?.value || eyebrowIconAltText?.value

  /* PRIMARY BUTTON */
  const primaryButtonType = buttonLinkTypePrimary?.value || ButtonTypes.PRIMARY
  const buttonTextPrimaryValue = getValue(buttonTextPrimary, 'buttonTextPrimary')
  const buttonLinkPrimaryValue = getValue(buttonLinkPrimary, 'buttonLinkPrimary')
  const buttonLinkNewTabPrimaryValue = getValue(buttonLinkNewTabPrimary, 'buttonLinkNewTabPrimary')
  const linkIdButtonPrimaryValue = getValue(linkIdButtonPrimary, 'linkIdButtonPrimary')
  const linkTypeButtonPrimaryValue = linkTypeButtonPrimary?.value
  const primaryButtonLink = primaryButtonHasAnchorLink ? primaryButtonAnchorLink : buttonLinkPrimaryValue

  /* SECONDARY BUTTON */
  const secondaryButtonType = buttonLinkTypeSecondary?.value || ButtonTypes.SECONDARY
  const buttonTextSecondaryValue = getValue(buttonTextSecondary, 'buttonTextSecondary')
  const buttonLinkSecondaryValue = getValue(buttonLinkSecondary, 'buttonLinkSecondary')
  const buttonLinkNewTabSecondaryValue = getValue(buttonLinkNewTabSecondary, 'buttonLinkNewTabSecondary')
  const linkIdButtonSecondaryValue = getValue(linkIdButtonSecondary, 'linkIdButtonSecondary')
  const linkTypeButtonSecondaryValue = linkTypeButtonSecondary?.value
  const secondaryButtonLink = secondaryButtonHasAnchorLink ? secondaryButtonAnchorLink : buttonLinkSecondaryValue

  /* TERTIARY BUTTON */
  const tertiaryButtonType = buttonLinkTypeTertiary?.value || ButtonTypes.TERTIARY
  const buttonTextTertiaryValue = getValue(buttonTextTertiary, 'buttonTextTertiary')
  const buttonLinkTertiaryValue = getValue(buttonLinkTertiary, 'buttonLinkTertiary')
  const buttonLinkNewTabTertiaryValue = getValue(buttonLinkNewTabTertiary, 'buttonLinkNewTabTertiary')
  const linkIdButtonTertiaryValue = getValue(linkIdButtonTertiary, 'linkIdButtonTertiary')
  const linkTypeButtonTertiaryValue = linkTypeButtonTertiary?.value
  const tertiaryButtonLink = tertiaryButtonHasAnchorLink ? tertiaryButtonAnchorLink : buttonLinkTertiaryValue

  return (
    <div data-testid='react-product-teaser-card' className='react-product-teaser-card' id={id}>
      <div className='react-product-teaser-card-top'>
        {contentBadgeTextValue && (
          <ReactBadge
            contentBadgeText={contentBadgeTextValue}
            contentBadgeColor={contentBadgeColorValue}
            isInContainer={true}
          />
        )}
        {imageValue && <img src={imageValue} alt={altImageValue} className='large-product-icon' />}
        <div className='react-product-teaser-card-title'>
          {(hasEyebrowIcon || eyebrowValue) && (
            <div className='icon-details'>
              {hasEyebrowIcon && <img src={eyebrowIconValue} alt={eyebrowIconAltTextValue} className='small-product-icon' />}
              {eyebrowValue && <ReactText className='icon-description' text={eyebrowValue} isParsed />}
            </div>
          )}
          {(headingValue || additionalNoteValue) && (
            <div className='plan-heading'>
              {headingValue && <ReactText className='heading' text={headingValue} isParsed />}
              {additionalNoteValue && <ReactText className='additional-note' text={additionalNoteValue} isParsed />}
            </div>
          )}
        </div>
        <div className='react-product-teaser-card-body'>
          {descriptionValue && <ReactText className='heading-description' text={descriptionValue} isParsed />}
          {bulletPoints?.value?.length > 0 && (
            <ReactList
              listFrom={ListType.customList}
              elements={elements}
              ruleExpression={ruleExpression}
              index={index}
              tokenProperties={tokenProperties}
            />
          )}
        </div>
      </div>
      <div className='react-product-teaser-card-bottom'>
        <div className='price-details'>
          {planNameValue && <ReactText className='plan-name' text={planNameValue} isParsed />}
          {priceValue && getDynamicPrice()}
        </div>
        <div className='product-teaser-buttons'>
          {buttonTextPrimaryValue && buttonTextPrimaryValue.length > 0 && (
            <div className='primary-teaser-button'>
              <ReactButton
                buttonTypes={primaryButtonType as ButtonTypes}
                text={buttonTextPrimaryValue}
                anchor={primaryButtonAnchorId}
                linkType={linkTypeButtonPrimaryValue as LinkTypes}
                linkUrl={primaryButtonLink}
                openInNewTab={Boolean(buttonLinkNewTabPrimaryValue)}
                linkId={linkIdButtonPrimaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            </div>
          )}
          {buttonTextSecondaryValue && buttonTextSecondaryValue.length > 0 && (
            <div className='secondary-teaser-button'>
              <ReactButton
                buttonTypes={secondaryButtonType as ButtonTypes}
                text={buttonTextSecondaryValue}
                anchor={secondaryButtonAnchorId}
                linkType={linkTypeButtonSecondaryValue as LinkTypes}
                linkUrl={secondaryButtonLink}
                openInNewTab={Boolean(buttonLinkNewTabSecondaryValue)}
                linkId={linkIdButtonSecondaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            </div>
          )}
          {buttonTextTertiaryValue && buttonTextTertiaryValue.length > 0 && (
            <div className='tertiary-teaser-button'>
              <ReactButton
                buttonTypes={tertiaryButtonType as ButtonTypes}
                text={buttonTextTertiaryValue}
                anchor={tertiaryButtonAnchorId}
                linkType={linkTypeButtonTertiaryValue as LinkTypes}
                linkUrl={tertiaryButtonLink}
                openInNewTab={Boolean(buttonLinkNewTabTertiaryValue)}
                linkId={linkIdButtonTertiaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
export default ReactProductTeaser
