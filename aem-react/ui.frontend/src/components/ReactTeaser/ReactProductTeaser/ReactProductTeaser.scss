@import '../../../assets/styles/v3/globalStyles.scss';

.react-product-teaser-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 28px;
  background: var(--color-neutral-100);
  border: 2px solid var(--color-neutral-250);
  border-radius: 20px;
  width: 100%;

  @media (min-width: $md) {
    padding: 28px;
  }

  @media (min-width: $lg) {
    padding: 44px 32px;
  }

  @media (min-width: $xl) {
    padding: 44px 36px;
  }

  @media (min-width: $xxl) {
    padding: 44px 36px;
  }

  .react-product-teaser-card-top {
    display: flex;
    flex-direction: column;

    .large-product-icon {
      width: 54px;
      height: 54px;
      border-radius: 999px;
      margin: 20px 0px;

      @media (min-width: $md) {
        width: 86px;
        height: 86px;
      }
    }
    .react-product-teaser-card-title {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .icon-details {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 12px;
        gap: 8px;

        .small-product-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }

        .icon-description {
          @include cox-text-eyebrow1;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-eyebrow1;
            margin-bottom: 0px;
          }
        }
      }

      .plan-heading {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .heading {
          @include cox-text-title1-bold;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-title1-bold;
          }
        }

        .additional-note {
          @include cox-text-paragraph5-medium;
          color: var(--color-neutral-500) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph5-medium;
            color: var(--color-neutral-500) !important;
          }
        }
      }
    }
    .react-product-teaser-card-body {
      margin-top: 24px;

      .heading-description {
        @include cox-text-paragraph2-regular;
        color: var(--color-neutral-400) !important;
        margin-bottom: 12px;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          @include cox-text-paragraph2-regular;
          color: var(--color-neutral-400) !important;
        }
      }

      .bullet-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .bullet-list-item {
          display: flex;
          flex-direction: row;
          justify-content: left;
          align-items: center;
          gap: 8px;

          .bullet-icon {
            align-self: flex-start;
            margin-top: 3px;
            border-radius: 50%;
            width: 16px;
            height: 16px;
          }

          .bullet-text {
            @include cox-text-paragraph3-medium;
            font-weight: 500 !important;
            color: var(--color-neutral-500) !important;

            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-paragraph3-medium;
              color: var(--color-neutral-500) !important;
            }
          }
        }

        @media (min-width: $lg) {
          min-height: 196px !important;
        }
      }
    }
  }

  .react-product-teaser-card-bottom {
    margin-top: 32px;

    .price-details {
      display: flex;
      flex-direction: column;
      flex: 1;

      * {
        cursor: default !important;
      }

      .plan-name {
        @include cox-text-title2-bold;
        color: var(--color-neutral-400) !important;
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          @include cox-text-title2-bold;
          color: var(--color-neutral-400) !important;
          margin-bottom: 8px;
        }
      }

      .react-default-price-container .react-price .react-price-col1 {
        .price-content {
          gap: 4px !important;

          .price {
            @media (min-width: $xl) {
              align-items: center !important;
              flex-direction: row !important;
            }
          }
        }
        .additional-details {
          .additional-note {
            @include cox-text-paragraph5-medium;

            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-paragraph5-medium;
            }
          }
        }
      }

      .word-wrap {
        cursor: text !important;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          cursor: text !important;
        }
      }
    }

    .product-teaser-buttons {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-self: stretch;

      @media (min-width: $lg) {
        align-self: auto;
      }

      .button-base {
        width: 100%;
      }

      .secondary-teaser-button {
        .button .button-container .text-button {
          .tertiary,
          .tertiaryalt {
            padding: 16px 20px !important;
          }
        }
      }
    }
  }
}
