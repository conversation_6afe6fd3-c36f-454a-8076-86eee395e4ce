@import '../../../assets/styles/v3/globalStyles.scss';

.react-psu-teaser-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  background: var(--color-neutral-100);
  border: 2px solid var(--color-neutral-300);
  border-radius: 20px;
  width: 100%;
  gap: 24px;
  padding: 28px;

  @media (min-width: $lg) {
    padding: 44px 32px;
  }

  @media (min-width: $xl) {
    padding: 44px 36px;
  }

  .top-section {
    width: 100%;

    .icon {
      margin: 20px 0;

      img {
        border-radius: 50%;
        width: 54px;
        height: 54px;

        @media (min-width: $md) {
          width: 86px;
          height: 86px;
        }
      }

      .img-placeholder {
        border-radius: 50%;
        width: 54px;
        height: 54px;

        @media (min-width: $md) {
          width: 86px;
          height: 86px;
        }
      }
    }

    .item-eyebrow {
      display: flex;
      align-items: center;
      @include cox-text-eyebrow1;
      p {
        @include cox-text-eyebrow1;
      }
      margin-bottom: 12px;

      .eyebrow-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        border-radius: 999px;
      }
    }

    // header for the item
    .header {
      @include cox-text-title1-bold;
      margin-bottom: 4px;
      p {
        @include cox-text-title1-bold;
      }
      word-break: break-word;
    }

    // additional note in the item
    .additional-note {
      @include cox-text-paragraph2-regular;
      color: var(--color-neutral-500) !important;
      p {
        @include cox-text-paragraph2-regular;
        color: var(--color-neutral-500) !important;
      }
    }

    // heading description
    .heading-description {
      @include cox-text-paragraph2-regular;
      color: var(--color-neutral-400) !important;
      margin-top: 24px;
      margin-bottom: 12px;
      p {
        @include cox-text-paragraph2-regular;
        color: var(--color-neutral-400) !important;
      }
    }

    // bullet list
    .bullet-list {
      margin: 20px 0;

      .bullet-list-item {
        display: flex;
        margin-bottom: 12px;

        p {
          margin: 0;
        }
      }

      .bullet-icon {
        width: 16px;
        height: 20px;
        margin-right: 8px;
        padding-top: 4px;
      }

      .bullet-text {
        @include cox-text-paragraph3-medium;
        color: var(--color-neutral-500) !important;
        white-space: pre-wrap;
        word-break: break-word;
        p {
          @include cox-text-paragraph3-medium;
          color: var(--color-neutral-500) !important;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
    }

    .accordion-container .accordion-item {
      .accordion-header {
        .accordion-title-icon {
          object-fit: fill;
        }
        .accordion-chevron {
          background-repeat: no-repeat;
        }
      }
      .accordion-body {
        .bullet-point-text {
          b {
            font-weight: 700;
          }
        }
      }
    }
  }

  .bottom-section {
    width: 100%;
    margin-top: 24px;

    @media (min-width: $lg) {
      margin-top: 32px;
    }

    .button-section {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-top: 24px;

      @media (min-width: $lg) {
        margin-top: 32px;
      }

      .button-base {
        width: 100%;
      }
    }
  }
}
