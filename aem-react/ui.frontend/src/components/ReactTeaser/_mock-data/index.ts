import { ContentType } from '../../../utils/types'
import { ComparisonVariation } from '../types'

export const imageTwoUp = {
  contentType: ContentType.static,
  teaserType: 'teaser',
  cardType: 'imagetwoup',
  variation: 'neutral',
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-0361ef8d01',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-15dda51d62',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-11eee9f0bb',
      ':itemsOrder': [],
      elements: {},
      elementsOrder: [],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath:
    '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop_1270115419',
  isInEditor: false,
  componentProperties: {
    contentType: ContentType.static,
    teaserType: 'teaser',
    cardType: 'imagetwoup',
    variation: 'neutral',
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-0361ef8d01',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-15dda51d62',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-11eee9f0bb',
        ':itemsOrder': [],
        elements: {},
        elementsOrder: [],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop_1270115419',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const imageThreeUp = {
  contentType: ContentType.static,
  teaserType: 'teaser',
  cardType: 'imagethreeup',
  variation: 'oceangreen',
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-3befe7c694',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-ced785863d',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-224df21d91',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'Standard',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Standard content',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Most Popular',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Super Fast',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
            '<p>Up to 35 Mbps upload speeds</p>\n',
            '<p>1.25 TB (1280 GB) monthly data included</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '1 GIG',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '95.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '109.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath:
    '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_1657842880',
  isInEditor: false,
  componentProperties: {
    contentType: ContentType.static,
    teaserType: 'teaser',
    cardType: 'imagethreeup',
    variation: 'oceangreen',
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-3befe7c694',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-ced785863d',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-224df21d91',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'Standard',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Standard content',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Most Popular',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Super Fast',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
              '<p>Up to 35 Mbps upload speeds</p>\n',
              '<p>1.25 TB (1280 GB) monthly data included</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '1 GIG',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '95.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '109.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_1657842880',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const iconTwoUp = {
  contentType: ContentType.static,
  teaserType: 'teaser',
  cardType: 'icontwoup',
  variation: 'white',
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-58df310957',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-abb234dcea',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-b29495aaa0',
      ':itemsOrder': [],
      elements: {},
      elementsOrder: [],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath:
    '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop_1372863264',
  isInEditor: false,
  componentProperties: {
    contentType: ContentType.static,
    teaserType: 'teaser',
    cardType: 'icontwoup',
    variation: 'neutral',
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-58df310957',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-abb234dcea',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-b29495aaa0',
        ':itemsOrder': [],
        elements: {},
        elementsOrder: [],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop_1372863264',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const iconThreeUp = {
  contentType: ContentType.static,
  teaserType: 'teaser',
  cardType: 'iconthreeup',
  variation: 'riverblue',
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-8bcd2087c5',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-55d71a6c57',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-71fd1669f2',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'Standard',
          ':type': 'string',
          multiValue: false
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Standard content',
          ':type': 'string',
          multiValue: false
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
          ':type': 'string',
          multiValue: false
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          ':type': 'string',
          multiValue: false
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Most Popular',
          ':type': 'string',
          multiValue: false
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Super Fast',
          ':type': 'string',
          multiValue: false
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
          ':type': 'text/html',
          multiValue: false
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          ':type': 'string',
          multiValue: false
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
            '<p>Up to 35 Mbps upload speeds</p>\n',
            '<p>1.25 TB (1280 GB) monthly data included</p>\n'
          ],
          ':type': 'text/html',
          multiValue: true
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '1 GIG',
          ':type': 'string',
          multiValue: false
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '95.00',
          ':type': 'string',
          multiValue: false
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '109.00',
          ':type': 'string',
          multiValue: false
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          ':type': 'string',
          multiValue: false
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          ':type': 'string',
          multiValue: false
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          ':type': 'string',
          multiValue: false
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          ':type': 'boolean',
          multiValue: false
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath:
    '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_165',
  isInEditor: false,
  componentProperties: {
    contentType: ContentType.static,
    teaserType: 'teaser',
    cardType: 'iconthreeup',
    variation: 'riverblue',
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-8bcd2087c5',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-55d71a6c57',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-71fd1669f2',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'Standard',
            ':type': 'string',
            multiValue: false
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Standard content',
            ':type': 'string',
            multiValue: false
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
            ':type': 'string',
            multiValue: false
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            ':type': 'string',
            multiValue: false
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Most Popular',
            ':type': 'string',
            multiValue: false
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Super Fast',
            ':type': 'string',
            multiValue: false
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
            ':type': 'text/html',
            multiValue: false
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            ':type': 'string',
            multiValue: false
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
              '<p>Up to 35 Mbps upload speeds</p>\n',
              '<p>1.25 TB (1280 GB) monthly data included</p>\n'
            ],
            ':type': 'text/html',
            multiValue: true
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '1 GIG',
            ':type': 'string',
            multiValue: false
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '95.00',
            ':type': 'string',
            multiValue: false
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '109.00',
            ':type': 'string',
            multiValue: false
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            ':type': 'string',
            multiValue: false
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            ':type': 'string',
            multiValue: false
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            ':type': 'string',
            multiValue: false
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            ':type': 'boolean',
            multiValue: false
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_165',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const exposedOneUp = {
  contentType: ContentType.static,
  teaserType: 'product',
  cardType: 'exposedoneup',
  variation: ComparisonVariation.PRICE_DROPDOWN,
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-db90a24187',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-8e30311a7e',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-b043ac6e17',
      ':itemsOrder': [],
      elements: {},
      elementsOrder: [],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath:
    '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop',
  isInEditor: false,
  componentProperties: {
    contentType: 'static',
    teaserType: 'product',
    cardType: 'exposedoneup',
    variation: ComparisonVariation.PRICE_DROPDOWN,
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-db90a24187',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-8e30311a7e',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-b043ac6e17',
        ':itemsOrder': [],
        elements: {},
        elementsOrder: [],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy_cop',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const exposedTwoUp = {
  contentType: ContentType.static,
  teaserType: 'product',
  cardType: 'exposedtwoup',
  variation: ComparisonVariation.PRICE_DROPDOWN,
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-2d0724807c',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-074359769d',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-a945a00135',
      ':itemsOrder': [],
      elements: {},
      elementsOrder: [],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath: '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy',
  isInEditor: false,
  componentProperties: {
    contentType: 'static',
    teaserType: 'product',
    cardType: 'exposedtwoup',
    variation: ComparisonVariation.PRICE_DROPDOWN,
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo. Get Internet starting at only $35/mo. Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-2d0724807c',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-074359769d',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-a945a00135',
        ':itemsOrder': [],
        elements: {},
        elementsOrder: [],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath:
      '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser_copy',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const exposedThreeUp = {
  contentType: ContentType.static,
  teaserType: 'product',
  cardType: 'exposedthreeup',
  variation: ComparisonVariation.PRICE_DROPDOWN,
  eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
  title: 'Get Internet starting at only $35/mo.',
  description:
    '<p>Get Internet starting at only $35/mo.&nbsp;Get Internet starting at only $35/mo.&nbsp;Get Internet starting at only $35/mo.</p>\r\n',
  linkText: '<p>Link text</p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
  tokenProperties: '{}',
  cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
  cqType: 'cox-cms-react/components/reactteaser',
  cqItems: {
    contentfragment_1: {
      id: 'reactcontentfragment-1db11e3e3a',
      description: '',
      title: 'Product Teaser',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'mobile',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer ',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox-cms-react/images/headset.svg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'gettyimages',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Description',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          value: 'Additional note',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n',
            '<p>Character count 50. Lorem ipsum</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: 'Plan Name',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '30.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '50.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'Term agreement: 50 characters max',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: '$20.00 Discount applied',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: '20 char. max',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'secondary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 1'
    },
    contentfragment_2: {
      id: 'reactcontentfragment-31053fdc04',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'special',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Special Offer',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Special Offer for you',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Even Faster',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Save with Cox Mobile</p>\n',
            '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
            '<p>No cancellation fees</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '500 Mbps',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '75.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '99.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 2'
    },
    contentfragment_3: {
      id: 'reactcontentfragment-b7cdbd92d8',
      description: '',
      title: 'product-teaser-static',
      ':itemsOrder': [],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          value: 'Standard',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          value: 'Standard content',
          multiValue: false,
          ':type': 'string'
        },
        image: {
          title: 'Large Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
          multiValue: false,
          ':type': 'string'
        },
        altImage: {
          title: 'Icon Alt Text',
          dataType: 'string',
          value: 'Couple with game controllers',
          multiValue: false,
          ':type': 'string'
        },
        eyebrowIcon: {
          title: 'eyebrowIcon',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'eyebrow',
          dataType: 'string',
          value: 'Most Popular',
          multiValue: false,
          ':type': 'string'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: 'Go Super Fast',
          multiValue: false,
          ':type': 'string'
        },
        additionalNote: {
          title: 'additionalNote',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        bulletIcon: {
          title: 'Bullet Icon',
          dataType: 'string',
          value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
          multiValue: false,
          ':type': 'string'
        },
        bulletPoints: {
          title: 'Bullet Points',
          dataType: 'string',
          value: [
            '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
            '<p>Up to 35 Mbps upload speeds</p>\n',
            '<p>1.25 TB (1280 GB) monthly data included</p>\n'
          ],
          multiValue: true,
          ':type': 'text/html'
        },
        planName: {
          title: 'Offer Name',
          dataType: 'string',
          value: '1 GIG',
          multiValue: false,
          ':type': 'string'
        },
        price: {
          title: 'Promotional Monthly Price',
          dataType: 'string',
          value: '95.00',
          multiValue: false,
          ':type': 'string'
        },
        scratchPrice: {
          title: 'Regular Price Strike through',
          dataType: 'string',
          value: '109.00',
          multiValue: false,
          ':type': 'string'
        },
        term: {
          title: 'Pricing Specifics - Term',
          dataType: 'string',
          value: 'No term agreement',
          multiValue: false,
          ':type': 'string'
        },
        discount: {
          title: 'Discount - Percent Savings',
          dataType: 'string',
          value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Order now',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/content/cox/residential/corporate/pages/internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'buttonLinkTypePrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        buttonTextSecondary: {
          title: 'Text Link',
          dataType: 'string',
          value: 'Tertiary',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'Text Link Url',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'buttonLinkTypeSecondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'buttonLinkNewTabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'image',
        'altImage',
        'eyebrowIcon',
        'eyebrow',
        'heading',
        'additionalNote',
        'description',
        'bulletIcon',
        'bulletPoints',
        'planName',
        'price',
        'scratchPrice',
        'term',
        'discount',
        'buttonTextPrimary',
        'buttonLinkPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkNewTabPrimary',
        'buttonTextSecondary',
        'buttonLinkSecondary',
        'buttonLinkTypeSecondary',
        'buttonLinkNewTabSecondary'
      ],
      ':type': 'cox-cms-react/components/reactcontentfragment',
      ':items': {},
      model: 'cox/ContentFragments/models/product-teaser-model1',
      'cq:panelTitle': 'Content Fragment 3'
    }
  },
  cqPath: '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser',
  isInEditor: false,
  componentProperties: {
    contentType: ContentType.static,
    teaserType: 'product',
    cardType: 'exposedthreeup',
    variation: ComparisonVariation.PRICE_DROPDOWN,
    eyebrow: 'COX HOMELIFE VIDEO DOORBELL',
    title: 'Get Internet starting at only $35/mo.',
    description:
      '<p>Get Internet starting at only $35/mo.&nbsp;Get Internet starting at only $35/mo.&nbsp;Get Internet starting at only $35/mo.</p>\r\n',
    linkText: '<p>Link text</p>\r\n',
    priceTagIcon: '/content/dam/cox/common/icons/ui_components/bell.svg',
    tokenProperties: '{}',
    cqItemsOrder: ['contentfragment_1', 'contentfragment_2', 'contentfragment_3'],
    cqType: 'cox-cms-react/components/reactteaser',
    cqItems: {
      contentfragment_1: {
        id: 'reactcontentfragment-1db11e3e3a',
        description: '',
        title: 'Product Teaser',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'mobile',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer ',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox-cms-react/images/headset.svg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'gettyimages',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/cox_completecare_icon.png',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Description',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Headline or plan name: Character count 50. Lorem ipsum dolor',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            value: 'Additional note',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Character count 100. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/images/icons/wayfinder-checkmark-green.png',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n',
              '<p>Character count 50. Lorem ipsum</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: 'Plan Name',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '30.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '50.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'Term agreement: 50 characters max',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: '$20.00 Discount applied',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: '20 char. max',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'secondary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            value: '/content/cox-cms-react/us/en/demo/Pricing-Teaser',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 1'
      },
      contentfragment_2: {
        id: 'reactcontentfragment-31053fdc04',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'special',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Special Offer',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/common/images/tv/tv_features_ondemand.jpg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Special Offer for you',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Even Faster',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 4k video, multi-player gaming, working from home, and more</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Save with Cox Mobile</p>\n',
              '<p>INCLUDES Panoramic Wifi equipment for 24 mos.</p>\n',
              '<p>No cancellation fees</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '500 Mbps',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '75.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '99.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 2'
      },
      contentfragment_3: {
        id: 'reactcontentfragment-b7cdbd92d8',
        description: '',
        title: 'product-teaser-static',
        ':itemsOrder': [],
        elements: {
          contentBadgeColor: {
            title: 'Content Badge Color',
            dataType: 'string',
            value: 'Standard',
            multiValue: false,
            ':type': 'string'
          },
          contentBadgeText: {
            title: 'Content Badge Text',
            dataType: 'string',
            value: 'Standard content',
            multiValue: false,
            ':type': 'string'
          },
          image: {
            title: 'Large Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-couple-with-game-controllers.jpg',
            multiValue: false,
            ':type': 'string'
          },
          altImage: {
            title: 'Icon Alt Text',
            dataType: 'string',
            value: 'Couple with game controllers',
            multiValue: false,
            ':type': 'string'
          },
          eyebrowIcon: {
            title: 'eyebrowIcon',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          eyebrow: {
            title: 'eyebrow',
            dataType: 'string',
            value: 'Most Popular',
            multiValue: false,
            ':type': 'string'
          },
          heading: {
            title: 'Headline',
            dataType: 'string',
            value: 'Go Super Fast',
            multiValue: false,
            ':type': 'string'
          },
          additionalNote: {
            title: 'additionalNote',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          description: {
            title: 'Body Copy',
            dataType: 'string',
            value: '<p>Great for 8k video, multi-player gaming, working from home, and more</p>\n',
            multiValue: false,
            ':type': 'text/html'
          },
          bulletIcon: {
            title: 'Bullet Icon',
            dataType: 'string',
            value: '/content/dam/cox/residential/hrd-assets/hrd-icon-arrow-inactive-right.svg',
            multiValue: false,
            ':type': 'string'
          },
          bulletPoints: {
            title: 'Bullet Points',
            dataType: 'string',
            value: [
              '<p>Up to 1 Gbps (1,000 Mbps) download speeds</p>\n',
              '<p>Up to 35 Mbps upload speeds</p>\n',
              '<p>1.25 TB (1280 GB) monthly data included</p>\n'
            ],
            multiValue: true,
            ':type': 'text/html'
          },
          planName: {
            title: 'Offer Name',
            dataType: 'string',
            value: '1 GIG',
            multiValue: false,
            ':type': 'string'
          },
          price: {
            title: 'Promotional Monthly Price',
            dataType: 'string',
            value: '95.00',
            multiValue: false,
            ':type': 'string'
          },
          scratchPrice: {
            title: 'Regular Price Strike through',
            dataType: 'string',
            value: '109.00',
            multiValue: false,
            ':type': 'string'
          },
          term: {
            title: 'Pricing Specifics - Term',
            dataType: 'string',
            value: 'No term agreement',
            multiValue: false,
            ':type': 'string'
          },
          discount: {
            title: 'Discount - Percent Savings',
            dataType: 'string',
            value: 'with Cox Mobile, for 24 mos. No annual contract or cancellation fees.',
            multiValue: false,
            ':type': 'string'
          },
          buttonTextPrimary: {
            title: 'CTA Text Primary',
            dataType: 'string',
            value: 'Order now',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkPrimary: {
            title: 'CTA Link/Url - Primary',
            dataType: 'string',
            value: '/content/cox/residential/corporate/pages/internet',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypePrimary: {
            title: 'buttonLinkTypePrimary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabPrimary: {
            title: 'buttonLinkNewTabPrimary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          },
          buttonTextSecondary: {
            title: 'Text Link',
            dataType: 'string',
            value: 'Tertiary',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkSecondary: {
            title: 'Text Link Url',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkTypeSecondary: {
            title: 'buttonLinkTypeSecondary',
            dataType: 'string',
            multiValue: false,
            ':type': 'string'
          },
          buttonLinkNewTabSecondary: {
            title: 'buttonLinkNewTabSecondary',
            dataType: 'boolean',
            value: false,
            multiValue: false,
            ':type': 'boolean'
          }
        },
        elementsOrder: [
          'contentBadgeColor',
          'contentBadgeText',
          'image',
          'altImage',
          'eyebrowIcon',
          'eyebrow',
          'heading',
          'additionalNote',
          'description',
          'bulletIcon',
          'bulletPoints',
          'planName',
          'price',
          'scratchPrice',
          'term',
          'discount',
          'buttonTextPrimary',
          'buttonLinkPrimary',
          'buttonLinkTypePrimary',
          'buttonLinkNewTabPrimary',
          'buttonTextSecondary',
          'buttonLinkSecondary',
          'buttonLinkTypeSecondary',
          'buttonLinkNewTabSecondary'
        ],
        ':type': 'cox-cms-react/components/reactcontentfragment',
        ':items': {},
        model: 'cox/ContentFragments/models/product-teaser-model1',
        'cq:panelTitle': 'Content Fragment 3'
      }
    },
    cqPath: '/content/cox/residential/corporate/pages/reactqa/hk/prod-teaser/jcr:content/root/responsivegrid/reactteaser',
    isInEditor: false,
    containerProps: {
      className: ' aem-GridColumn aem-GridColumn--default--12'
    }
  },
  baseCssClass: 'cmp-teaser'
}

export const standard = {
  contentType: ContentType.static,
  lob: '',
  teaserType: 'teaser',
  cardType: 'standard',
  variation: 'withoutimage',
  eyebrow: 'Cox internet + Mobile',
  title: 'Make the switch',
  description:
    '<p>Experience ultimate connectivity when you sign up for both Cox Internet and Cox Mobile. Get mobile as low as $15/mo*—plus a discount on your monthly internet bill. For Cox Mobile eligibility, you need to be a Cox Internet customer.</p>\r\n',
  linkText:
    '<p><a href="/content/cox/residential/corporate/pages/internet.html" target="_self" rel="noopener noreferrer">Shop internet</a></p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components',
  top: true,
  bottom: true,
  right: true,
  left: true,
  tokenProperties: '{}',
  ':items': {
    contentfragment_1: {
      id: 'reactcontentfragment-1ea0f2ba21',
      description: '',
      title: 'Commerce Teaser No Image Master',
      ':items': {},
      ':type': 'cox-cms-react/components/reactcontentfragment',
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'eyebrow',
        'heading',
        'description',
        'buttonTextPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkPrimary',
        'buttonLinkNewTabPrimary',
        'linkTypeButtonPrimary',
        'linkIdButtonPrimary',
        'buttonLinkTypeSecondary',
        'buttonTextSecondary',
        'linkTypeButtonSecondary',
        'buttonLinkSecondary',
        'linkIdButtonSecondary',
        'buttonLinkNewTabSecondary'
      ],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'Eyebrow Text',
          dataType: 'string',
          value: '<p>Cox Internet + Mobile</p>',
          multiValue: false,
          ':type': 'text/html'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: '<p>Make the switch</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value:
            '<p>Cox Internet and Cox Mobile keep you connected—at home and on the go. Get mobile as low as $15/GB—plus a discount on your monthly internet bill.&nbsp;<i>Cox Internet required for Cox Mobile.</i></p>\n<p><span class="cox-text-paragraph5-regular"><i><span class="text-white">Prices exclude taxes, device, activation, roaming data, international usage and other fees and charges, which are subject to change.</span></i></span></p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Shop internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'Button LinkType Primary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/residential/internet.html',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        linkTypeButtonPrimary: {
          title: 'LinkTypeButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonPrimary: {
          title: 'LinkIdButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'Button LinkType Secondary',
          dataType: 'string',
          value: 'tertiaryalt',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextSecondary: {
          title: 'Button Text Secondary',
          dataType: 'string',
          value: 'Sign in',
          multiValue: false,
          ':type': 'string'
        },
        linkTypeButtonSecondary: {
          title: 'LinkType Button Secondary',
          dataType: 'string',
          value: 'page',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'CTA Link/Url - Secondaryl',
          dataType: 'string',
          value:
            'https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresidential%2Fmobile.html',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonSecondary: {
          title: 'LinkId Button Secondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'Button LinkNew TabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      ':itemsOrder': [],
      model: 'cox/residential/models/teaser-no-image',
      'cq:panelTitle': 'Content Fragment 1'
    }
  },
  ':type': 'cox-cms-react/components/reactteaser',
  ':itemsOrder': ['contentfragment_1']
}

export const commerce = {
  contentType: ContentType.static,
  lob: '',
  teaserType: 'teaser',
  cardType: 'commerce',
  variation: 'withoutimage',
  eyebrow: 'Cox internet + Mobile',
  title: 'Make the switch',
  description:
    '<p>Experience ultimate connectivity when you sign up for both Cox Internet and Cox Mobile. Get mobile as low as $15/mo*—plus a discount on your monthly internet bill. For Cox Mobile eligibility, you need to be a Cox Internet customer.</p>\r\n',
  linkText:
    '<p><a href="/content/cox/residential/corporate/pages/internet.html" target="_self" rel="noopener noreferrer">Shop internet</a></p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components',
  top: true,
  bottom: true,
  right: true,
  left: true,
  tokenProperties: '{}',
  ':items': {
    contentfragment_1: {
      id: 'reactcontentfragment-1ea0f2ba21',
      description: '',
      title: 'Commerce Teaser No Image Master',
      ':items': {},
      ':type': 'cox-cms-react/components/reactcontentfragment',
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'eyebrow',
        'heading',
        'description',
        'buttonTextPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkPrimary',
        'buttonLinkNewTabPrimary',
        'linkTypeButtonPrimary',
        'linkIdButtonPrimary',
        'buttonLinkTypeSecondary',
        'buttonTextSecondary',
        'linkTypeButtonSecondary',
        'buttonLinkSecondary',
        'linkIdButtonSecondary',
        'buttonLinkNewTabSecondary'
      ],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'Eyebrow Text',
          dataType: 'string',
          value: '<p>Cox Internet + Mobile</p>',
          multiValue: false,
          ':type': 'text/html'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: '<p>Make the switch</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value:
            '<p>Cox Internet and Cox Mobile keep you connected—at home and on the go. Get mobile as low as $15/GB—plus a discount on your monthly internet bill.&nbsp;<i>Cox Internet required for Cox Mobile.</i></p>\n<p><span class="cox-text-paragraph5-regular"><i><span class="text-white">Prices exclude taxes, device, activation, roaming data, international usage and other fees and charges, which are subject to change.</span></i></span></p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Shop internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'Button LinkType Primary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/residential/internet.html',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        linkTypeButtonPrimary: {
          title: 'LinkTypeButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonPrimary: {
          title: 'LinkIdButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'Button LinkType Secondary',
          dataType: 'string',
          value: 'tertiaryalt',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextSecondary: {
          title: 'Button Text Secondary',
          dataType: 'string',
          value: 'Sign in',
          multiValue: false,
          ':type': 'string'
        },
        linkTypeButtonSecondary: {
          title: 'LinkType Button Secondary',
          dataType: 'string',
          value: 'page',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'CTA Link/Url - Secondaryl',
          dataType: 'string',
          value:
            'https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresidential%2Fmobile.html',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonSecondary: {
          title: 'LinkId Button Secondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'Button LinkNew TabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      ':itemsOrder': [],
      model: 'cox/residential/models/teaser-no-image',
      'cq:panelTitle': 'Content Fragment 1'
    }
  },
  ':type': 'cox-cms-react/components/reactteaser',
  ':itemsOrder': ['contentfragment_1']
}

export const featured = {
  contentType: ContentType.static,
  lob: '',
  teaserType: 'teaser',
  cardType: 'featured',
  variation: 'withoutimage',
  eyebrow: 'Cox internet + Mobile',
  title: 'Make the switch',
  description:
    '<p>Experience ultimate connectivity when you sign up for both Cox Internet and Cox Mobile. Get mobile as low as $15/mo*—plus a discount on your monthly internet bill. For Cox Mobile eligibility, you need to be a Cox Internet customer.</p>\r\n',
  linkText:
    '<p><a href="/content/cox/residential/corporate/pages/internet.html" target="_self" rel="noopener noreferrer">Shop internet</a></p>\r\n',
  priceTagIcon: '/content/dam/cox/common/icons/ui_components',
  top: true,
  bottom: true,
  right: true,
  left: true,
  tokenProperties: '{}',
  ':items': {
    contentfragment_1: {
      id: 'reactcontentfragment-1ea0f2ba21',
      description: '',
      title: 'Commerce Teaser No Image Master',
      ':items': {},
      ':type': 'cox-cms-react/components/reactcontentfragment',
      elementsOrder: [
        'contentBadgeColor',
        'contentBadgeText',
        'eyebrow',
        'heading',
        'description',
        'buttonTextPrimary',
        'buttonLinkTypePrimary',
        'buttonLinkPrimary',
        'buttonLinkNewTabPrimary',
        'linkTypeButtonPrimary',
        'linkIdButtonPrimary',
        'buttonLinkTypeSecondary',
        'buttonTextSecondary',
        'linkTypeButtonSecondary',
        'buttonLinkSecondary',
        'linkIdButtonSecondary',
        'buttonLinkNewTabSecondary'
      ],
      elements: {
        contentBadgeColor: {
          title: 'Content Badge Color',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        contentBadgeText: {
          title: 'Content Badge Text',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        eyebrow: {
          title: 'Eyebrow Text',
          dataType: 'string',
          value: '<p>Cox Internet + Mobile</p>',
          multiValue: false,
          ':type': 'text/html'
        },
        heading: {
          title: 'Headline',
          dataType: 'string',
          value: '<p>Make the switch</p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        description: {
          title: 'Body Copy',
          dataType: 'string',
          value:
            '<p>Cox Internet and Cox Mobile keep you connected—at home and on the go. Get mobile as low as $15/GB—plus a discount on your monthly internet bill.&nbsp;<i>Cox Internet required for Cox Mobile.</i></p>\n<p><span class="cox-text-paragraph5-regular"><i><span class="text-white">Prices exclude taxes, device, activation, roaming data, international usage and other fees and charges, which are subject to change.</span></i></span></p>\n',
          multiValue: false,
          ':type': 'text/html'
        },
        buttonTextPrimary: {
          title: 'CTA Text Primary',
          dataType: 'string',
          value: 'Shop internet',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypePrimary: {
          title: 'Button LinkType Primary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkPrimary: {
          title: 'CTA Link/Url - Primary',
          dataType: 'string',
          value: '/residential/internet.html',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabPrimary: {
          title: 'buttonLinkNewTabPrimary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        },
        linkTypeButtonPrimary: {
          title: 'LinkTypeButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonPrimary: {
          title: 'LinkIdButtonPrimary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkTypeSecondary: {
          title: 'Button LinkType Secondary',
          dataType: 'string',
          value: 'tertiaryalt',
          multiValue: false,
          ':type': 'string'
        },
        buttonTextSecondary: {
          title: 'Button Text Secondary',
          dataType: 'string',
          value: 'Sign in',
          multiValue: false,
          ':type': 'string'
        },
        linkTypeButtonSecondary: {
          title: 'LinkType Button Secondary',
          dataType: 'string',
          value: 'page',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkSecondary: {
          title: 'CTA Link/Url - Secondaryl',
          dataType: 'string',
          value:
            'https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresidential%2Fmobile.html',
          multiValue: false,
          ':type': 'string'
        },
        linkIdButtonSecondary: {
          title: 'LinkId Button Secondary',
          dataType: 'string',
          multiValue: false,
          ':type': 'string'
        },
        buttonLinkNewTabSecondary: {
          title: 'Button LinkNew TabSecondary',
          dataType: 'boolean',
          value: false,
          multiValue: false,
          ':type': 'boolean'
        }
      },
      ':itemsOrder': [],
      model: 'cox/residential/models/teaser-no-image',
      'cq:panelTitle': 'Content Fragment 1'
    }
  },
  ':type': 'cox-cms-react/components/reactteaser',
  ':itemsOrder': ['contentfragment_1']
}
