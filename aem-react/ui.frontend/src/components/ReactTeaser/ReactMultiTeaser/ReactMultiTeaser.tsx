import { useEffect, useRef, useState } from 'react'

import { ButtonTypes, LinkTypes } from '@cox/core-ui8/dist/Button'
import StoreButton from '@cox/core-ui8/dist/StoreButton'
import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'
import { useWindowSize } from '@cox/core-ui8/dist/useWindowSize'

import ReactMultiTeaserSkeleton from './ReactMultiTeaserSkeleton'
import { DynamicMediaComponents, getDynamicMediaFromSrc } from '../../../utils/dynamic-media-util'
import {
  getAttributeValue,
  getConditionResult,
  getFlexOffersLength,
  hasCFConditionals
} from '../../../utils/rule-builder-util'
import { getAnchorInfo } from '../../../utils/teaser-util'
import { RuleType, ScreenSizes } from '../../../utils/types'
import ReactBadge from '../../ReactBadge'
import ReactButton from '../../ReactButton'
import ReactImage from '../../ReactImage'
import ReactPrice, { PriceVariant } from '../../ReactPrice'
import ReactSectionHeader from '../../ReactSectionHeader'
import ReactText from '../../ReactText'
import { ContentFragment, Elements, ImageType, ReactMultiTeaserProps, TeaserType, variantClass, Variation } from '../types'
import './ReactMultiTeaser.scss'

const ReactMultiTeaser = (props: ReactMultiTeaserProps): JSX.Element => {
  const {
    cardType,
    variation,
    description,
    eyebrow,
    title,
    ruleExpression = '',
    ruleType,
    condition,
    isDynamic = false,
    contentFragments,
    contentType,
    tokenProperties,
    isInEditor = false,
    hasBorder = false,
    columnWidth,
    hideImageOrIcon = false
  } = props

  const flexOffers = useFlexOffers(ruleExpression)
  const { width: windowWidth } = useWindowSize()

  const hasFourCards = cardType === TeaserType.iconfourup || cardType === TeaserType.imagefourup
  const hasThreeCards = cardType === TeaserType.iconthreeup || cardType === TeaserType.imagethreeup
  const hasTwoCards = cardType === TeaserType.icontwoup || cardType === TeaserType.imagetwoup
  const hasIconImage = cardType.includes('icon')
  const imageType = hasIconImage ? ImageType.circular : ImageType.square
  let cardsLength = 0
  let numberOfCardsClass = ''
  if (hasTwoCards) {
    cardsLength = 2
    numberOfCardsClass = 'two-up'
  } else if (hasThreeCards) {
    cardsLength = 3
    numberOfCardsClass = 'three-up'
  } else if (hasFourCards) {
    cardsLength = 4
    numberOfCardsClass = 'four-up'
  }

  const getColumnClass = () => {
    if (columnWidth == '25') {
      return 'col-lg-3'
    } else if (columnWidth == '33') {
      return 'col-lg-4'
    } else if (columnWidth == '50') {
      return 'col-lg-6'
    } else if (hasFourCards) {
      // If columnWidth is not passed and there are four cards, default:
      return 'col-lg-3'
    } else if (hasThreeCards) {
      // If columnWidth is not passed and there are three cards, default:
      return 'col-lg-4'
    } else {
      // If columnWidth is not passed and there are two cards, default:
      return 'col-lg-6'
    }
  }
  const newContentFragments = contentFragments.length > 0 ? contentFragments.slice(0, cardsLength) : []
  const isDynamicHeightRequired =
    newContentFragments.length > 0
      ? newContentFragments.some(
          (cf: ContentFragment) => cf?.elements?.['description']?.value || cf?.elements?.['disclaimer']?.value
        )
      : true
  const [headingHeight, setHeadingHeight] = useState(0)
  const [eyeBrowHeight, setEyeBrowHeight] = useState(0)
  const [descriptionHeight, setDescriptionHeight] = useState(0)
  const [disclaimerHeight, setDisclaimerHeight] = useState(0)
  const headingRef = useRef([])
  const eyeBrowRef = useRef([])
  const descriptionRef = useRef([])
  const disclaimerRef = useRef([])

  useEffect(() => {
    setTimeout(() => {
      const headingHeights = headingRef?.current?.map((ref: any) => ref?.offsetHeight)
      const eyeBrowHeights = eyeBrowRef?.current?.map((ref: any) => ref?.offsetHeight)
      const descriptionHeights = descriptionRef?.current?.map((ref: any) => ref?.offsetHeight)
      const disclaimerHeights = disclaimerRef?.current?.map((ref: any) => ref?.offsetHeight)
      const maxHeadingHeight = Math.max(...headingHeights)
      const maxEyeBrowHeight = Math.max(...eyeBrowHeights)
      const maxDescriptionHeight = Math.max(...descriptionHeights)
      const maxDisclaimerHeight = Math.max(...disclaimerHeights)
      setEyeBrowHeight(maxEyeBrowHeight)
      setHeadingHeight(maxHeadingHeight)
      setDescriptionHeight(maxDescriptionHeight)
      setDisclaimerHeight(maxDisclaimerHeight)
    }, 1000)
  }, [])

  const getButtonTypes = (item: Elements, button: string) => {
    if (button == 'primary') {
      const { buttonLinkTypePrimary = { value: '' } } = item
      if (buttonLinkTypePrimary?.value) {
        return buttonLinkTypePrimary?.value as ButtonTypes
      } else
        return variation !== Variation.neutral && variation !== Variation.white
          ? ButtonTypes.PRIMARYALT
          : ButtonTypes.PRIMARY
    } else if (button == 'secondary') {
      const { buttonLinkTypeSecondary = { value: '' } } = item
      if (buttonLinkTypeSecondary?.value) {
        return buttonLinkTypeSecondary?.value as ButtonTypes
      } else
        return variation !== Variation.neutral && variation !== Variation.white
          ? ButtonTypes.TERTIARYALT
          : ButtonTypes.TERTIARY
    }
  }

  const getDynamicMediaComponentName = () => {
    if (cardType === TeaserType.imagefourup || cardType === TeaserType.exposedfourup) {
      return DynamicMediaComponents.TEASER_IMAGE_FOUR_UP
    } else if (cardType === TeaserType.imagethreeup || cardType === TeaserType.exposedthreeup) {
      return DynamicMediaComponents.TEASER_IMAGE_THREE_UP
    } else if (cardType === TeaserType.imagetwoup || cardType === TeaserType.exposedtwoup) {
      return DynamicMediaComponents.TEASER_IMAGE_TWO_UP
    } else if (cardType === TeaserType.featured) {
      return DynamicMediaComponents.PRODUCT_TEASER_FEATURED
    } else {
      return DynamicMediaComponents.TEASER
    }
  }

  const renderCard = (cardType: string, item: Elements, index: number, id: string) => {
    const {
      contentBadgeColor = { value: '' },
      contentBadgeText = { value: '' },
      image = { value: '' },
      imageParams = { value: '' },
      altImage = { value: '' },
      eyebrow = { value: '' },
      heading = { value: '' },
      description = { value: '' },
      disclaimer = { value: '' },

      /* Props for Price */
      price = { value: '' },
      term = { value: '' },
      scratchPrice = { value: '' },
      discount = { value: '' },

      /* Props for Primary Button */
      buttonTextPrimary = { value: '' },
      buttonLinkNewTabPrimary = { value: '' },
      buttonLinkPrimary = { value: '' },
      linkIdButtonPrimary = { value: '' },
      linkTypeButtonPrimary = { value: '' },

      /* Props for Secondary Button */
      buttonTextSecondary = { value: '' },
      buttonLinkNewTabSecondary = { value: '' },
      buttonLinkSecondary = { value: '' },
      linkIdButtonSecondary = { value: '' },
      linkTypeButtonSecondary = { value: '' },

      /* Props for App Store Buttons */
      appStoreImage = { value: '' },
      appStoreLink = { value: '' },
      appStoreAltText = { value: '' },
      playStoreImage = { value: '' },
      playStoreLink = { value: '' },
      playStoreAltText = { value: '' }
    } = item

    const flexOffersLength = getFlexOffersLength(flexOffers)

    if (isDynamic && flexOffersLength && flexOffersLength < index + 1) return
    const getValue = (attribute: any, propertyName?: string, index = 0) => {
      if (hasCFConditionals(attribute)) {
        const jsonAttribute = (item as any)?.[`${propertyName}_json`]
        return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
      }
      return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
    }
    const {
      hasAnchorLink: primaryButtonHasAnchorLink,
      anchorUrl: primaryButtonAnchorLink,
      anchorId: primaryButtonAnchorId
    } = getAnchorInfo(buttonLinkPrimary?.value)
    const {
      hasAnchorLink: secondaryButtonHasAnchorLink,
      anchorUrl: secondaryButtonAnchorLink,
      anchorId: secondaryButtonAnchorId
    } = getAnchorInfo(buttonLinkSecondary?.value)

    const contentBadgeTextValue = getValue(contentBadgeText, 'contentBadgeText')
    const contentBadgeColorValue = getValue(contentBadgeColor, 'contentBadgeColor')
    const altImageValue = getValue(altImage, 'altImage', index)
    const imageParamsValue = getValue(imageParams, 'imageParams')
    const imageValue = getDynamicMediaFromSrc(
      getValue(image, 'image', index) as string,
      (imageParamsValue as DynamicMediaComponents) || getDynamicMediaComponentName()
    )
    const eyebrowValue = getValue(eyebrow, 'eyebrow', index)
    const headingValue = getValue(heading, 'heading', index)
    const descriptionValue = getValue(description, 'description', index)
    const disclaimerValue = getValue(disclaimer, 'disclaimer', index)

    /* Values for Price */
    const priceValue = getValue(price, 'price', index)
    const termValue = getValue(term, 'term', index)
    const scratchPriceValue = getValue(scratchPrice, 'scratchPrice', index)
    const discountValue = getValue(discount, 'discount', index)

    /* Values for Primary Button */
    const buttonTextPrimaryValue = getValue(buttonTextPrimary, 'buttonTextPrimary', index)
    const buttonLinkPrimaryValue = getValue(buttonLinkPrimary, 'buttonLinkPrimary', index)
    const buttonLinkNewTabPrimaryValue = getValue(buttonLinkNewTabPrimary, 'buttonLinkNewTabPrimary', index)
    const linkIdButtonPrimaryValue = getValue(linkIdButtonPrimary, 'linkIdButtonPrimary')
    const linkTypeButtonPrimaryValue = linkTypeButtonPrimary?.value
    const primaryButtonLink = primaryButtonHasAnchorLink ? primaryButtonAnchorLink : buttonLinkPrimaryValue
    /* Values for Secondary Button */
    const buttonTextSecondaryValue = getValue(buttonTextSecondary, 'buttonTextSecondary', index)
    const buttonLinkSecondaryValue = getValue(buttonLinkSecondary, 'buttonLinkSecondary', index)
    const buttonLinkNewTabSecondaryValue = getValue(buttonLinkNewTabSecondary, 'buttonLinkNewTabSecondary', index)
    const linkIdButtonSecondaryValue = getValue(linkIdButtonSecondary, 'linkIdButtonSecondary')
    const linkTypeButtonSecondaryValue = linkTypeButtonSecondary?.value
    const secondaryButtonLink = secondaryButtonHasAnchorLink ? secondaryButtonAnchorLink : buttonLinkSecondaryValue

    /* Values for App Store Buttons */
    const appStoreImageValue = getValue(appStoreImage, 'appStoreImage')
    const appStoreLinkValue = getValue(appStoreLink, 'appStoreLink')
    const appStoreAltTextValue = getValue(appStoreAltText, 'appStoreAltText')
    const playStoreImageValue = getValue(playStoreImage, 'playStoreImage')
    const playStoreLinkValue = getValue(playStoreLink, 'playStoreLink')
    const playStoreAltTextValue = getValue(playStoreAltText, 'playStoreAltText')

    const displayAppStore = appStoreImageValue && appStoreLinkValue
    const displayPlayStore = playStoreImageValue && playStoreLinkValue
    const displayStoreButtonsSection = displayAppStore && displayPlayStore
    const displayRegularButtonsSection = buttonTextPrimaryValue || buttonTextSecondaryValue
    const displayReactTeaserPrice = priceValue || termValue

    const styles = {
      image: {
        backgroundImage: imageValue ? `url("${imageValue}")` : 'none',
        backgroundColor: imageValue ? 'none' : '#b9c9d2'
      }
    }

    const isLargeScreen = windowWidth >= ScreenSizes.LG

    const getDynamicEyeBrow = () => {
      if (eyebrowValue) {
        if (isLargeScreen) {
          return (
            <div ref={(ref: any) => ((eyeBrowRef.current[index] as any) = ref)} style={{ minHeight: `${eyeBrowHeight}px` }}>
              <ReactText text={eyebrowValue} isParsed className='eyebrow' />
            </div>
          )
        }
        return <ReactText text={eyebrowValue} isParsed className='eyebrow' />
      }
      return <></>
    }
    const getDynamicHeading = () => {
      if (headingValue) {
        if (isLargeScreen) {
          return (
            <div ref={(ref: any) => ((headingRef.current[index] as any) = ref)} style={{ minHeight: `${headingHeight}px` }}>
              <ReactText text={headingValue} isParsed className='heading' />
            </div>
          )
        }
        return <ReactText text={headingValue} isParsed className='heading' />
      }
      return <></>
    }
    const getDynamicDescription = () => {
      if (descriptionValue) {
        if (isLargeScreen) {
          return (
            <div
              ref={(ref: any) => ((descriptionRef.current[index] as any) = ref)}
              style={{ minHeight: `${descriptionHeight}px` }}
            >
              <ReactText text={descriptionValue} isParsed className={isDynamicHeightRequired ? 'description' : ''} />
            </div>
          )
        }
        return <ReactText text={descriptionValue} isParsed className={isDynamicHeightRequired ? 'description' : ''} />
      }
      return <></>
    }
    const getDynamicDisclaimer = () => {
      if (disclaimerValue) {
        if (isLargeScreen) {
          return (
            <div
              ref={(ref: any) => ((disclaimerRef.current[index] as any) = ref)}
              style={{ minHeight: `${disclaimerHeight}px` }}
            >
              <ReactText text={disclaimerValue} isParsed className={isDynamicHeightRequired ? 'disclaimer' : ''} />
            </div>
          )
        }
        return <ReactText text={disclaimerValue} isParsed className={isDynamicHeightRequired ? 'disclaimer' : ''} />
      }
      return <></>
    }

    const showImage = imageType === ImageType.square && !hideImageOrIcon
    const showIcon = imageType === ImageType.circular && !hideImageOrIcon

    return (
      <div
        className={`col-md-12 ${getColumnClass()} cards-container`}
        key={`react-multi-teaser${index}}`}
        id={id}
        data-testid='react-multi-teaser-card'
      >
        {showImage && <ReactImage src={imageValue} alt={altImageValue} imgCss='squareImage' asBG></ReactImage>}
        <div
          className={`react-multi-teaser-card  react-multi-teaser-card-body
              ${variation !== Variation.neutral && variation !== Variation.white && 'react-teaser-color'}
             ${variantClass[variation]} 
             ${hasBorder ? 'has-border' : ''} 
             ${cardType}`}
          style={{
            borderTopLeftRadius: imageType === ImageType.square ? 0 : '20',
            borderTopRightRadius: imageType === ImageType.square ? 0 : '20'
          }}
        >
          {showIcon && (
            <div
              style={styles.image}
              aria-label={altImageValue}
              className={`profileImage squareBgImage ${variantClass[variation]}`}
              data-testid='profileImage'
              role='img'
              tabIndex={0}
            ></div>
          )}
          <div className='react-multi-teaser-card-content'>
            {contentBadgeTextValue && (
              <ReactBadge
                contentBadgeText={contentBadgeTextValue}
                contentBadgeColor={contentBadgeColorValue}
                isInContainer={true}
              />
            )}
            {getDynamicEyeBrow()}
            {getDynamicHeading()}
            {getDynamicDisclaimer()}
            {getDynamicDescription()}
          </div>
          {displayReactTeaserPrice && (
            <ReactPrice
              variant={PriceVariant.DEFAULT}
              price={priceValue}
              term={termValue}
              scratchPrice={scratchPriceValue}
              additionalNote={discountValue}
              cardType={cardType}
              className='react-multi-teaser-price-section'
            />
          )}
          {displayRegularButtonsSection && (
            <div className='regular-buttons-section'>
              {buttonTextPrimaryValue && (
                <ReactButton
                  buttonTypes={getButtonTypes(item, 'primary')}
                  text={buttonTextPrimaryValue}
                  anchor={primaryButtonAnchorId}
                  aria-label={buttonLinkPrimaryValue}
                  linkUrl={primaryButtonLink}
                  openInNewTab={!!buttonLinkNewTabPrimaryValue}
                  linkType={linkTypeButtonPrimaryValue as LinkTypes}
                  linkId={linkIdButtonPrimaryValue}
                  index={index}
                  ruleExpression={ruleExpression}
                />
              )}
              {buttonTextSecondaryValue && (
                <ReactButton
                  buttonTypes={getButtonTypes(item, 'secondary')}
                  text={buttonTextSecondaryValue}
                  anchor={secondaryButtonAnchorId}
                  aria-label={buttonLinkSecondaryValue}
                  linkUrl={secondaryButtonLink}
                  openInNewTab={!!buttonLinkNewTabSecondaryValue}
                  linkType={linkTypeButtonSecondaryValue as LinkTypes}
                  linkId={linkIdButtonSecondaryValue}
                  index={index}
                  ruleExpression={ruleExpression}
                />
              )}
            </div>
          )}
          {displayStoreButtonsSection && (
            <div className='store-buttons-section'>
              <StoreButton
                linkValue={appStoreLinkValue}
                imageValue={appStoreImageValue}
                altTextValue={appStoreAltTextValue}
                size={'md'}
              />
              <StoreButton
                linkValue={playStoreLinkValue}
                imageValue={playStoreImageValue}
                altTextValue={playStoreAltTextValue}
                size={'md'}
              />
            </div>
          )}
        </div>
      </div>
    )
  }

  if (flexOffers?.loading) {
    return (
      <ReactMultiTeaserSkeleton
        columnClass={getColumnClass()}
        hasTwoCards={hasTwoCards}
        imageType={imageType}
        newContentFragments={newContentFragments}
        contentType={contentType}
        variation={variation}
        flexOffers={flexOffers}
      />
    )
  }

  return (
    <div data-testid={`react-multi-teaser-container`} className={`react-multi-teaser-container ${numberOfCardsClass}`}>
      <ReactSectionHeader
        eyebrow={{ value: eyebrow, isRTE: true }}
        title={{ value: title, isRTE: true }}
        description={{ value: description, isRTE: true, className: 'main-description' }}
        isInEditor={isInEditor}
      />

      <div data-testid='react-multi-teaser-cards-container' className='react-multi-teaser-cards-container overflow-hidden'>
        <div className='react-multi-teaser-cards-wrapper row'>
          {newContentFragments.map((item: ContentFragment, index: number) => {
            const elements = item?.elements
            const id = item?.id
            const flexOffersLength: number = getFlexOffersLength(flexOffers)
            let conditionResult = true
            if (flexOffers?.success && ruleType === RuleType.conditional && condition) {
              conditionResult = getConditionResult(condition, flexOffers, index)
            }
            if (conditionResult) {
              if (contentType === 'static' || (contentType === 'dynamic' && index < flexOffersLength)) {
                return renderCard(cardType, elements, index, id)
              }
              return null
            }
          })}
        </div>
      </div>
    </div>
  )
}
export default ReactMultiTeaser
