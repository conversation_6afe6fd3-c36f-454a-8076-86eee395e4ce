import { CoreContainerProperties } from '@adobe/aem-core-components-react-spa'

import { ContentType, RuleType } from '../../utils/types'

export interface ElementObject {
  dataType: string
  title: string
  value: string
  multiValue: boolean
  ':type': string
}

export interface ElementBooleanObject {
  dataType: string
  title: string
  value: boolean
  multiValue: boolean
  ':type': string
}

export interface ElementArrayObject {
  dataType: string
  title: string
  value: string[]
  multiValue: boolean
  ':type': string[]
}

export enum TeaserCategory {
  product = 'product',
  psu = 'psu',
  teaser = 'teaser'
}

export enum TeaserType {
  iconfourup = 'iconfourup',
  imagefourup = 'imagefourup',
  iconthreeup = 'iconthreeup',
  imagethreeup = 'imagethreeup',
  icontwoup = 'icontwoup',
  imagetwoup = 'imagetwoup',
  standard = 'standard',
  featured = 'featured',
  commerce = 'commerce',
  comparison = 'comparison',
  exposedoneup = 'exposedoneup',
  exposedtwoup = 'exposedtwoup',
  exposedthreeup = 'exposedthreeup',
  exposedfourup = 'exposedfourup'
}

export enum TeaserComponents {
  iconthreeup = 'ReactMultiTeaser',
  imagethreeup = 'ReactMultiTeaser',
  icontwoup = 'ReactMultiTeaser',
  imagetwoup = 'ReactMultiTeaser',
  standard = 'ReactStandardTeaser',
  featured = 'featured',
  commerce = 'ReactStandardTeaser',
  comparison = 'comparison',
  exposedoneup = 'exposedoneup',
  exposedtwoup = 'exposedtwoup',
  exposedthreeup = 'exposedthreeup'
}

export enum Variation {
  riverblue = 'riverblue',
  oceangreen = 'oceangreen',
  neutral = 'neutral',
  white = 'white',
  withimage = 'withimage',
  withoutimage = 'withoutimage'
}
/*
Ocean Green (value: "oceangreen")
River Blue (value: "riverblue")
White Lilac (value: "whitelilac")
Royal Blue (value: "royalblue")
Night Gray (value: "nightgray")
Light Field Green (value: "lightfieldgreen")
Mint Green (value: "mintgreen")
Business Navy (value: "businessnavy")
Alice Blue (value: "aliceblue")
*/
export enum BackgroundVariation {
  SELECT = 'select',
  OCEAN_GREEN = 'oceangreen',
  RIVER_BLUE = 'riverblue',
  WHITE_LILAC = 'whitelilac',
  ROYAL_BLUE = 'royalblue',
  NIGHT_GRAY = 'nightgray',
  LIGHT_FIELD_GREEN = 'lightfieldgreen',
  MINT_GREEN = 'mintgreen',
  BUSINESS_NAVY = 'businessnavy',
  ALICE_BLUE = 'aliceblue'
}

export enum ImageType {
  square = 'square',
  circular = 'circular'
}

export enum ImageAlignment {
  LEFT = 'left',
  RIGHT = 'right'
}

export const variantClass = {
  [Variation.neutral]: 'neutral',
  [Variation.oceangreen]: 'ocean-green',
  [Variation.riverblue]: 'river-blue',
  [Variation.white]: 'white',
  [Variation.withimage]: 'with-image',
  [Variation.withoutimage]: 'witout-image'
}

export interface ReactTeaserProps extends CoreContainerProperties {
  lob?: string
  teaserType: string
  cardType: string
  description: string
  eyebrow: string
  title: string
  variation: string
  ruleExpression?: string
  isInEditor: boolean
  ruleType: RuleType
  condition: string
  ':itemsOrder': string[]
  ':items': object
  contentType: ContentType
  tokenProperties?: string
  priceTagIcon?: string
  priceDropdownIcon?: string
  priceDropdownIconAlt?: string
  id?: string
  top?: boolean
  right?: boolean
  bottom?: boolean
  left?: boolean
  backgroundVariation?: string
  imageAlignment?: ImageAlignment
  maxSlides?: number
  hideMCP?: boolean
  hasBorder?: boolean
  columnWidth?: string
  hideImageOrIcon?: boolean
}

export interface ReactStandardTeaserProps {
  cardType: TeaserType
  condition: string
  contentFragments: ContentFragments
  isInEditor?: boolean
  ruleExpression?: string
  ruleType: RuleType
  variation: Variation
  contentType: ContentType
  tokenProperties?: string
  priceTagIcon?: string
  backgroundVariation: BackgroundVariation
  imageAlignment?: ImageAlignment
}

export interface ReactMultiTeaserProps {
  ruleExpression?: string
  cardType: TeaserType
  variation: Variation
  eyebrow: string
  title: string
  description: string
  linkText?: string
  isDynamic?: boolean
  ruleType?: RuleType
  condition?: string
  isInEditor?: boolean
  contentFragments: ContentFragments
  contentType: ContentType
  tokenProperties?: string
  hasBorder?: boolean
  columnWidth?: string
  hideImageOrIcon?: boolean
}

export interface ReactProductTeaserProps {
  elements: Elements
  elementsOrder?: string[]
  ruleExpression?: string
  index?: number
  isInEditor?: boolean
  cardType?: TeaserType
  tokenProperties?: string
  priceTagIcon?: string
  id?: string
  priceRef?: any
  priceHeight?: number
}

export interface ReactPSUTeaserProps {
  elements: Elements
  elementsOrder?: string[]
  ruleExpression?: string
  index?: number
  isInEditor?: boolean
  cardType?: TeaserType
  tokenProperties?: string
  priceTagIcon?: string
  id?: string
  badgeRef?: any
  badgeHeight?: number
}

export interface ReactProductComparisonTeaserProps {
  contentFragments: ContentFragments
  ruleExpression?: string
  index?: number
  isInEditor?: boolean
  cardType?: TeaserType
  tokenProperties?: string
  priceTagIcon?: string
  priceDropdownIcon?: string
  priceDropdownIconAlt?: string
  id?: string
  ruleType?: RuleType
  condition?: string
  contentType: ContentType
  variation?: string
  priceDropdown_json?: any
  backgroundVariation?: BackgroundVariation
  maxSlides?: number
}

export interface ReactTeaserCardsProps {
  contentFragments: ContentFragments
  ruleExpression?: string
  ruleType?: RuleType
  condition?: string
  contentType?: ContentType
  maxSlides?: number
  renderCard: (elements: Elements, index: number, id: string) => NonNullable<unknown>
}

export interface ReactMultiProductTeaserProps {
  cardType: TeaserType
  eyebrow: string
  title: string
  description: string
  ruleExpression?: string
  isInEditor?: boolean
  contentFragments: ContentFragments
  contentType: ContentType
  ruleType?: RuleType
  condition?: string
  tokenProperties?: string
  priceTagIcon?: string
}

export interface ReactMultiPSUTeaserProps {
  cardType: TeaserType
  eyebrow: string
  title: string
  description: string
  ruleExpression?: string
  isInEditor?: boolean
  contentFragments: ContentFragments
  contentType: ContentType
  ruleType?: RuleType
  condition?: string
  tokenProperties?: string
  priceTagIcon?: string
}

export type ContentFragments = Array<ContentFragment>

export interface ContentFragment {
  description: string
  title: string
  elements: Elements
  id: string
}

export interface Elements {
  accordionItems?: ElementObject
  additionalNote?: ElementObject
  altImage?: ElementObject
  appStoreAltText?: string
  appStoreImage?: string
  appStoreLink?: string
  bulletIcon?: ElementObject
  bulletIconAltText?: ElementObject
  bulletIconAltTexts?: ElementArrayObject
  bulletIcons?: ElementArrayObject
  bulletPoints?: ElementArrayObject
  buttonLinkNewTabPrimary?: ElementBooleanObject
  buttonLinkNewTabSecondary?: ElementBooleanObject
  buttonLinkNewTabTertiary?: ElementBooleanObject
  buttonLinkPrimary?: ElementObject
  buttonLinkSecondary?: ElementObject
  buttonLinkTertiary?: ElementObject
  buttonLinkTypePrimary?: ElementObject
  buttonLinkTypeSecondary?: ElementObject
  buttonLinkTypeTertiary?: ElementObject
  buttonTextPrimary?: ElementObject
  buttonTextSecondary?: ElementObject
  buttonTextTertiary?: ElementObject
  changeSign?: ElementObject
  contentBadgeColor?: ElementObject
  contentBadgeText?: ElementObject
  contentFragmentId?: ElementObject
  description?: ElementObject
  disclaimer?: ElementObject
  discount?: ElementObject
  eyebrow?: ElementObject
  eyebrowIcon?: ElementObject
  eyebrowIconAltText?: ElementObject
  heading?: ElementObject
  headingIcon?: ElementObject
  headingIconAltText?: ElementObject
  headingUnit?: ElementObject
  image?: ElementObject
  imageParams?: ElementObject
  linkIdButtonPrimary?: ElementObject
  linkIdButtonSecondary?: ElementObject
  linkIdButtonTertiary?: ElementObject
  linkTypeButtonPrimary?: ElementObject
  linkTypeButtonSecondary?: ElementObject
  linkTypeButtonTertiary?: ElementObject
  newBillText?: ElementObject
  planName?: ElementObject
  playStoreAltText?: string
  playStoreImage?: string
  playStoreLink?: string
  price?: ElementObject
  priceChangeText?: ElementObject
  pricedropdown?: ElementObject
  psuItems?: ElementObject
  scratchPrice?: ElementObject
  term?: ElementObject
  title1?: ElementObject
}

export enum ComparisonVariation {
  PRICE_DROPDOWN = 'pricedropdown',
  NO_PRICEDROPDOWN = 'nopricedropdown'
}
