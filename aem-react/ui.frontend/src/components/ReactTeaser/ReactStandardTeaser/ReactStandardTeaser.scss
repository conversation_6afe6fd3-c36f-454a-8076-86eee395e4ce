@import '../../../assets/styles/v3/globalStyles.scss';

.react-standard-teaser-wrapper {
  @media (max-width: $lg) {
    display: flex;
    justify-content: center;
  }

  .react-standard-teaser-container {
    border-radius: 20px;
    overflow: hidden;
    --bs-gutter-x: -0.5rem !important;
    min-width: 272px;

    .react-badge {
      text-align: center;
    }

    .squareImage {
      min-height: 272px;
      padding: 0;
    }

    .squareBgImage {
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .teaser-top {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .button-secondary {
      .tertiary,
      .tertiaryalt {
        padding: 16px 20px !important;
      }
    }

    .standard-teaser-card {
      background: var(--background-strong);
      color: var(--foreground-on-strong-1);
      gap: 24px;
      @media (min-width: $md) {
        gap: 32px;
      }
      @media (min-width: $xl) {
        gap: 36px;
      }

      .teaser-top {
        gap: 16px;
        @media (min-width: $md) {
          gap: 20px;
        }
        @media (min-width: $lg) {
          align-items: flex-start;
        }
        @media (min-width: $xl) {
          gap: 28px;
        }

        a {
          color: var(--color-neutral-100);
          &:hover {
            color: var(--color-blue-100);
          }
        }

        .eyebrow {
          color: var(--foreground-on-strong-1);
          display: inline;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);
            display: inline;
          }
        }

        .heading {
          color: var(--foreground-on-strong-1);
          display: inline;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);
            display: inline;
          }
        }

        .description {
          color: var(--foreground-on-strong-1);

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);
          }
        }
      }

      .teaser-buttons {
        .button-primary {
          display: flex;
          justify-content: center;

          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          display: flex;
          justify-content: center;
        }
      }
    }

    .commerce-teaser-card {
      background: var(--background-bold);
      color: var(--foreground-on-strong-1);
      gap: 24px;
      @media (min-width: $md) {
        gap: 32px;
        padding: 56px;
      }
      @media (min-width: $xl) {
        gap: 48px;
        padding: 88px;
      }

      .teaser-top {
        gap: 16px;

        @media (min-width: $sm) {
          gap: 24px;
        }
        @media (min-width: $md) {
          gap: 32px;
        }
        @media (min-width: $lg) {
          gap: 20px;
          align-items: flex-start;
        }
        @media (min-width: $xl) {
          gap: 24px;
        }

        .eyebrow {
          color: var(--foreground-on-strong-1);
          display: inline;
          a {
            color: var(--foreground-on-strong-1);
            &:hover {
              color: var(--interactive-accent-button-primary-on-strong-hover);
            }
          }
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);
            display: inline;

            a {
              color: var(--foreground-on-strong-1);
              &:hover {
                color: var(--interactive-accent-button-primary-on-strong-hover);
              }
            }
          }
        }

        .heading {
          color: var(--foreground-on-strong-1);
          display: inline;
          a {
            color: var(--foreground-on-strong-1);
            &:hover {
              color: var(--interactive-accent-button-primary-on-strong-hover);
            }
          }
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);
            display: inline;
            a {
              color: var(--foreground-on-strong-1);
              &:hover {
                color: var(--interactive-accent-button-primary-on-strong-hover);
              }
            }
          }
        }

        .description {
          color: var(--foreground-on-strong-1);
          a {
            color: var(--foreground-on-strong-1);
            &:hover {
              color: var(--color-blue-100);
            }
          }
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--foreground-on-strong-1);

            a {
              color: var(--foreground-on-strong-1);
              &:hover {
                color: var(--color-blue-100);
              }
            }
          }
        }
      }

      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
      }
    }

    .featured-teaser-card {
      background: var(--background-muted-1);
      color: var(--foreground-on-default-1);
      gap: 24px;

      @media (min-width: $md) {
        gap: 32px;
      }
      @media (min-width: $lg) {
        padding: 56px;
      }
      @media (min-width: $xl) {
        gap: 40px;
      }

      .teaser-top {
        gap: 16px;
        @media (min-width: $md) {
          gap: 20px;
        }
        @media (min-width: $lg) {
          align-items: flex-start;
        }
        @media (min-width: $xl) {
          gap: 24px;
        }

        .eyebrow {
          display: inline;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            display: inline;
          }
        }

        .heading {
          display: inline;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            display: inline;
          }
        }

        .description {
          text-align: center;
          @media (min-width: $lg) {
            text-align: left;
          }

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            text-align: center;
            @media (min-width: $lg) {
              text-align: left;
            }
          }
        }
      }

      .react-price-container {
        .dropdown-toggle::after {
          display: none !important;
        }
        .react-price-col1 {
          gap: 16px;
          text-align: center;

          @media (min-width: $lg) {
            text-align: left;
          }

          .price-content {
            .price {
              align-self: center;
              align-items: center;
              flex-direction: column;

              @media (min-width: $sm) {
                flex-direction: row;
                gap: 8px;
              }
              @media (min-width: $lg) {
                align-self: start;
              }
            }
            .product-term {
              @include cox-text-paragraph3-medium;
              h1,
              h2,
              h3,
              h4,
              h5,
              h6,
              p {
                @include cox-text-paragraph3-medium;
              }
            }
          }

          .additional-details {
            align-self: center !important;

            @media (min-width: $lg) {
              align-self: start !important;
            }
            .additional-note {
              @include cox-text-paragraph5-regular;
              @media (min-width: $xl) {
                @include cox-text-paragraph3-regular;
              }
              h1,
              h2,
              h3,
              h4,
              h5,
              h6,
              p {
                @include cox-text-paragraph5-regular;

                @media (min-width: $xl) {
                  @include cox-text-paragraph3-regular;
                }
              }
            }
          }
          @media (min-width: $lg) {
            gap: 24px;
          }
        }
      }

      .teaser-buttons {
        .button-secondary {
          .button-base {
            .button-text {
              color: var(--color-blue-600) !important;
            }
          }
          .button-base:hover {
            .button-text {
              color: var(--color-blue-900) !important;
            }
          }
        }
      }
    }

    .react-standard-teaser-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;

      .teaser-top,
      .react-price-container {
        span.o-slash-disclaimer > a {
          color: inherit !important;
        }
      }

      .react-price-container {
        .dropdown-toggle {
          background-color: transparent !important;
        }
      }

      .featured-teaser-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;

        @media (min-width: $md) {
          gap: 6px;
        }

        @media (min-width: $lg) {
          flex-direction: row;
          gap: 0px;
        }

        .featured-button-primary {
          .button {
            .button-container {
              .text-button {
                display: flex;
                justify-content: center;
              }
            }
          }
        }

        .featured-button-secondary {
          .button {
            .button-container {
              .text-button {
                display: flex;
                justify-content: center;
              }
            }
          }
          @media (min-width: $lg) {
            .button-base.responsive-size {
              padding-left: 20px;
            }
          }
        }
      }
    }

    // ********************************** STYLES BASED ON BACKGROUND VARIATION ********************************** //

    .oceangreen-background-variation {
      background-color: var(--color-teal-500) !important;
      .teaser-top * {
        color: var(--color-neutral-100) !important;

        a {
          color: var(--foreground-on-strong-1) !important;
          &:hover {
            color: var(--interactive-accent-button-primary-on-strong-hover) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .react-price-container {
        .details-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .scratch-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .product-term {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .additional-note {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100) !important;
            border-color: var(--color-blue-100) !important;
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .tertiaryalt {
            color: var(--color-neutral-100) !important;
            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .tertiaryalt:hover {
            .button-text {
              color: var(--color-blue-100) !important;
            }
          }
        }
      }
    }

    .riverblue-background-variation {
      background-color: var(--color-blue-600) !important;
      .teaser-top * {
        color: var(--color-neutral-100) !important;

        a {
          color: var(--color-neutral-100) !important;
          &:hover {
            color: var(--color-blue-100) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .react-price-container {
        .details-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .scratch-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .product-term {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .additional-note {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .tertiaryalt {
            color: var(--color-neutral-100) !important;
            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .tertiaryalt:hover {
            .button-text {
              color: var(--color-blue-100) !important;
            }
          }
        }
      }
    }

    .whitelilac-background-variation {
      background-color: var(--color-neutral-200) !important;

      .teaser-top * {
        color: var(--color-neutral-600) !important;

        a {
          color: var(--color-blue-600) !important;
          &:hover {
            color: var(--color-blue-900) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }

      .teaser-buttons {
        .button-secondary {
          .button-base {
            .button-text {
              color: var(--color-blue-600) !important;
            }
          }
          .button-base:hover {
            .button-text {
              color: var(--color-blue-900) !important;
            }
          }
        }
      }
    }

    .royalblue-background-variation {
      background-color: var(--color-blue-900) !important;
      .teaser-top * {
        color: var(--color-neutral-100) !important;
        a {
          &:hover {
            color: var(--color-blue-100) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .react-price-container {
        .details-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .scratch-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .product-term {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .additional-note {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .tertiaryalt {
            color: var(--color-neutral-100) !important;
            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .tertiaryalt:hover {
            .button-text {
              color: var(--color-blue-100) !important;
            }
          }
        }
      }
    }

    .nightgray-background-variation {
      background-color: var(--color-neutral-600) !important;
      .teaser-top * {
        color: var(--color-neutral-100) !important;
        a {
          &:hover {
            color: var(--color-blue-100) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .react-price-container {
        .details-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .scratch-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .product-term {
          color: var(--color-neutral-100) !important;
          a {
            color: var(--color-neutral-100) !important;
            &:hover {
              color: var(--color-blue-100) !important;
            }
          }
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;

            a {
              color: var(--color-neutral-100) !important;
              &:hover {
                color: var(--color-blue-100) !important;
              }
            }
          }
        }
        .additional-note {
          color: var(--color-neutral-100) !important;
          a {
            color: var(--color-neutral-100) !important;
            &:hover {
              color: var(--color-blue-100) !important;
            }
          }
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            a {
              color: var(--color-neutral-100) !important;
              &:hover {
                color: var(--color-blue-100) !important;
              }
            }
            color: var(--color-neutral-100) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .tertiaryalt {
            color: var(--color-neutral-100) !important;
            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .tertiaryalt:hover {
            .button-text {
              color: var(--color-blue-100) !important;
            }
          }
        }
      }
    }

    .lightfieldgreen-background-variation {
      background-color: var(--color-green-400) !important;

      .teaser-top * {
        color: var(--color-neutral-600) !important;

        a {
          color: var(--color-blue-600) !important;
          &:hover {
            color: var(--color-blue-900) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .button-base {
            .button-text {
              color: var(--color-blue-600) !important;
            }
          }
          .button-base:hover {
            .button-text {
              color: var(--color-blue-900) !important;
            }
          }
        }
      }
    }

    .mintgreen-background-variation {
      background-color: var(--color-green-250) !important;
      .teaser-top * {
        color: var(--color-neutral-600) !important;

        a {
          color: var(--color-blue-600) !important;
          &:hover {
            color: var(--color-blue-900) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .button-base {
            background-color: var(--color-green-600) !important;
            border: 2px var(--color-blue-600) !important;

            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .button-base:hover {
            background-color: var(--color-green-800) !important;
          }
        }

        .button-secondary {
          .button-base {
            .button-text {
              color: var(--color-blue-600) !important;
            }
          }
          .button-base:hover {
            .button-text {
              color: var(--color-blue-900) !important;
            }
          }
        }
      }
    }

    .businessnavy-background-variation {
      background-color: var(--color-blue-750) !important;
      .teaser-top * {
        color: var(--color-neutral-100) !important;

        a {
          color: var(--foreground-on-strong-1) !important;
          &:hover {
            color: var(--color-blue-100) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }
      .react-price-container {
        .details-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .scratch-price {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .product-term {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
        .additional-note {
          color: var(--color-neutral-100) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-100) !important;
          }
        }
      }
      .teaser-buttons {
        .button-primary {
          .primaryalt:hover {
            background-color: var(--color-blue-100);
            border-color: var(--color-blue-100);
            color: var(--color-blue-900) !important;
          }
        }
        .button-secondary {
          .tertiaryalt {
            color: var(--color-neutral-100) !important;
            .button-text {
              color: var(--color-neutral-100) !important;
            }
          }
          .tertiaryalt:hover {
            color: var(--color-blue-100) !important;
            .button-text {
              color: var(--color-blue-100) !important;
            }
          }
        }
      }
    }

    .aliceblue-background-variation {
      background-color: var(--color-blue-100) !important;
      .teaser-top * {
        color: var(--color-neutral-600) !important;

        a {
          color: var(--color-blue-600) !important;
          &:hover {
            color: var(--color-blue-900) !important;
          }
        }

        .badge {
          color: var(--color-neutral-600) !important;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            color: var(--color-neutral-600) !important;
          }
        }
      }

      .teaser-buttons {
        .button-secondary {
          .button-base {
            .button-text {
              color: var(--color-blue-600) !important;
            }
          }
          .button-base:hover {
            .button-text {
              color: var(--color-blue-900) !important;
            }
          }
        }
      }
    }

    .eyebrow {
      @include cox-text-eyebrow2;
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-eyebrow2;
      }
    }

    .heading {
      @include cox-text-heading2;
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-heading2;
      }
    }

    .eyebrow,
    .heading {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
    }

    .description {
      @include cox-text-paragraph1-medium;
      text-align: center;
      .linktext {
        margin-left: 8px;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-paragraph1-medium;
        margin: 0;
        text-align: center;
      }
    }

    .standard-description,
    .commerce-description {
      @include cox-text-paragraph1-medium;
      color: var(--foreground-on-strong-1);
      a {
        color: var(--foreground-on-strong-1);
        &:hover {
          color: var(--color-neutral-300);
        }
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-paragraph1-medium;
        color: var(--foreground-on-strong-1);
        a {
          color: var(--foreground-on-strong-1);
          &:hover {
            color: var(--color-neutral-300);
          }
        }
      }
    }

    .featured-description {
      color: var(--foreground-on-default-1);
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        color: var(--foreground-on-default-1);
      }
    }

    .store-buttons-section {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      @media (min-width: $md) {
        flex-direction: row;
      }
    }

    @media (min-width: $sm) {
      width: 528px;
      .squareImage {
        min-height: 528px;
      }
    }
    @media (min-width: $md) {
      width: 672px;
      .squareImage {
        min-height: 672px;
      }
      .react-standard-teaser-card {
        justify-content: center;
        padding: 56px;
      }
    }
    @media (min-width: $lg) {
      display: flex;
      flex-direction: row;
      width: auto;
      text-align: start;
      .eyebrow {
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          text-align: start;
        }
      }

      .heading {
        text-align: left;
        align-items: flex-start;
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          align-items: flex-start;
          text-align: left;
        }
      }

      .description {
        text-align: left;
      }

      .react-standard-teaser-card {
        align-items: flex-start;
      }

      .teaser-buttons {
        display: flex;
        align-items: flex-start;
      }

      .squareImage {
        min-height: 438px;
      }

      .description {
        text-align: left;
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          text-align: left;
        }
      }

      .featured-teaser-buttons {
        .featured-button-secondary {
          .button-base.responsive-size {
            padding-left: 20px;
          }
        }
      }
    }
    @media (min-width: $xl) {
      .standard-teaser-card,
      .featured-teaser-card {
        padding: 84px;
      }
      .commerce-teaser-card {
        padding: 88px;
      }

      .squareImage {
        min-height: 524px;
      }
    }
  }
}
