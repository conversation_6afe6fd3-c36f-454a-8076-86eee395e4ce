import React from 'react'

/* eslint-disable testing-library/no-container */
/* eslint-disable testing-library/no-node-access */
/* eslint-disable @typescript-eslint/no-var-requires */
import { AppDataProvider } from '@cox/core-ui8/dist/AppDataContext'
import { render, screen } from '@testing-library/react'

import ReactModal from './ReactModal'
import { ReactModalProps } from '../../components/ReactModal/types'

jest.mock('@adobe/aem-core-components-react-spa', () => ({
  withStandardBaseCssClass: (Component: any) => Component
}))

jest.mock('@adobe/aem-react-editable-components', () => {
  const mockReact = require('react')
  return {
    Container: class extends mockReact.Component {
      get containerProps() {
        return { className: 'mock-container' }
      }
      get childComponents() {
        return mockReact.createElement('div', { 'data-testid': 'child-components' }, 'Child Components')
      }
      get placeholderComponent() {
        return mockReact.createElement('div', { 'data-testid': 'placeholder' }, 'Placeholder')
      }
    },
    ComponentMapping: {}
  }
})

jest.mock('./ReactAuthorableModal', () => (props: any) => <div data-testid='react-authorable-modal'>{props.children}</div>)

jest.mock(
  '../shared/IsInEditorText',
  () => (isInEditor: boolean, text: string) => isInEditor ? <div data-testid='editor-text'>{text}</div> : null
)

describe('ReactModal', () => {
  const defaultProps: ReactModalProps = {
    modal: 'basicModal',
    isInEditor: false,
    baseCssClass: 'test-class',
    modalId: 'test-id',
    backgroundStyle: 'default',
    id: 'test-id',
    allowedComponents: {
      applicable: true,
      components: [
        {
          path: 'path/to/component',
          title: 'Component Title'
        }
      ]
    },
    title: 'Test Title',
    cqItems: {},
    cqItemsOrder: [],
    cqPath: '',
    gridClassNames: '',
    columnClassNames: {}
  }
  test('renders the ReactAuthorableModal component', () => {
    render(
      <AppDataProvider>
        <ReactModal {...defaultProps} />
      </AppDataProvider>
    )
    expect(screen.getByTestId('react-authorable-modal')).toBeInTheDocument()
  })

  test('renders correctly in editor mode with modal component', () => {
    render(<ReactModal {...defaultProps} isInEditor={true} />)
    expect(screen.getByTestId('editor-text')).toBeInTheDocument()
    expect(screen.getByText(`(${defaultProps.modalId}) Modal`)).toBeInTheDocument()
    expect(screen.getAllByTestId('child-components')).toHaveLength(2)
    expect(screen.getAllByTestId('placeholder')).toHaveLength(2)
    expect(screen.getByTestId('react-authorable-modal')).toBeInTheDocument()
  })

  test('renders correctly outside editor mode without youtubeVideoId', () => {
    const props = {
      ...defaultProps,
      isInEditor: false,
      cqItems: { embed: {} }
    }
    const { container } = render(<ReactModal {...props} />)
    expect(container.querySelector('[style="display: none;"]')).toBeInTheDocument()
    expect(container.querySelector(`.${props.baseCssClass}`)).toBeInTheDocument()
  })
  test('does not render hidden div if youtubeVideoId is present', () => {
    const props = {
      ...defaultProps,
      isInEditor: false,
      cqItems: { embed: { youtubeVideoId: 'abc123' } as any }
    }
    const { container } = render(<ReactModal {...props} />)
    expect(container.querySelector('[style="display: none;"]')).not.toBeInTheDocument()
  })
  test('does not render modal if modal type is not found', () => {
    const props = {
      ...defaultProps,
      modal: 'nonExistingModal'
    }
    render(<ReactModal {...props} />)
    expect(screen.queryByTestId('react-authorable-modal')).not.toBeInTheDocument()
  })
})
