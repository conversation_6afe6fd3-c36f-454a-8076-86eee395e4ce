import { ButtonStates, ButtonTypes } from '@cox/core-ui8/dist/Button'
import type { Meta, StoryObj } from '@storybook/react'

import ReactSticky from './ReactSticky'

const meta: Meta<typeof ReactSticky> = {
  title: 'Components/ReactSticky',
  component: ReactSticky,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
## ReactSticky Component

A secondary navigation banner that follows users down a page with sticky positioning.

### Features
- **Sticky Positioning**: Stays fixed to the top of the viewport as users scroll
- **Responsive Design**: Adapts layout for mobile (accordion) and desktop (horizontal) viewports
- **Flexible Content**: Supports sticky text, navigation links, and optional button
- **Smart Alignment**: Centers links only when no sticky text AND no button are present
- **Full Width**: Container extends to full screen width with grid constraints at ≥1400px

### Layout Variants
1. **Full Layout**: Sticky text + links + button (traditional usage)
2. **Text + Links**: Sticky text + right-aligned links (no button)
3. **Links Only**: Centered links across full width (no sticky text, no button)

### Responsive Behavior
- **Mobile (XS-L)**: Collapsible accordion with overlay
  - **Mobile Default Text**: When \`stickyText\` is empty on mobile, displays "On this page" as default
- **Desktop (XL+)**: Horizontal layout with space-between positioning

### Authoring Guidelines
- Leave BOTH \`text\` and \`stickyText\` fields empty for centered links layout
- Leave only \`text\` field empty to hide button (links remain right-aligned)
- Leave only \`stickyText\` field empty for links-only layout (shows "On this page" on mobile)
- Configure up to 7 links total (per latest specifications)
- Links support both anchor navigation and external URLs
- **Mobile**: All dropdown links are left-aligned regardless of desktop alignment
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    stickyText: {
      control: 'text',
      description:
        'Primary text displayed on the left side of the component. Leave empty for links-only layout. On mobile (XS-LG), when empty, displays "On this page" as default.',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '""' }
      }
    },
    text: {
      control: 'text',
      description: 'Button text. Leave empty to hide the button. Links only center when BOTH text and stickyText are empty.',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '""' }
      }
    },
    linkDetails: {
      control: 'object',
      description: 'Array of navigation links. Supports up to 7 links with anchor and external URL options.',
      table: {
        type: { summary: 'Array<LinkDetails>' }
      }
    },
    buttonStates: {
      control: 'select',
      options: ['ACTIVE', 'DISABLED'],
      description: 'Button state when button is rendered.',
      table: {
        type: { summary: 'ButtonStates' }
      }
    },
    buttonTypes: {
      control: 'select',
      options: ['PRIMARY', 'SECONDARY', 'TERTIARY'],
      description: 'Button styling type when button is rendered.',
      table: {
        type: { summary: 'ButtonTypes' }
      }
    },
    accordionIcon: {
      control: 'text',
      description: 'Icon path for mobile accordion expand/collapse chevron.',
      table: {
        type: { summary: 'string' }
      }
    },
    openInNewTab: {
      control: 'boolean',
      description: 'Whether button link opens in new tab.',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    isInEditor: {
      control: 'boolean',
      description: 'AEM editor mode flag for displaying component label.',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    lob: {
      control: 'text',
      description: 'Line of business theme class (e.g., "cox-resi", "cox-busi").',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '""' }
      }
    }
  }
}

export default meta
type Story = StoryObj<typeof ReactSticky>

export const StickyWithButton: Story = {
  args: {
    stickyText: 'Sticky text',
    accordionIcon: '/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg',
    text: 'Sticky button',
    buttonLink: {
      valid: false,
      url: 'wwww.cox.com'
    },
    linkDetails: [
      {
        linkText: 'Link1',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'button1'
      },
      {
        linkText: 'Link2',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'anchor1'
      },
      {
        linkText: 'Link3',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false
      },
      {
        linkText: 'Link4',
        openinnewtab: false
      },
      {
        linkText: 'Link5',
        openinnewtab: false
      },
      {
        linkText: 'Link6',
        openinnewtab: false
      }
    ],
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.SECONDARY,
    openInNewTab: false,
    isInEditor: false
  },
  parameters: {
    docs: {
      description: {
        story:
          'Traditional layout with sticky text on the left, navigation links, and a button on the right. Links are right-aligned against the button. This is the original design pattern.'
      }
    }
  }
}

export const StickyWithoutButton: Story = {
  args: {
    stickyText: 'Sticky text without button - links right-aligned',
    accordionIcon: '/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg',
    text: '', // Empty text means no button will be rendered
    buttonLink: {
      valid: false,
      url: 'wwww.cox.com'
    },
    linkDetails: [
      {
        linkText: 'Link1',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'button1'
      },
      {
        linkText: 'Link2',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'anchor1'
      },
      {
        linkText: 'Link3',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false
      },
      {
        linkText: 'Link4',
        openinnewtab: false
      }
    ],
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.SECONDARY,
    openInNewTab: false,
    isInEditor: false
  },
  parameters: {
    docs: {
      description: {
        story:
          'Layout with sticky text on the left and right-aligned navigation links. The button is hidden by leaving the `text` field empty. Links remain right-aligned because sticky text is present.'
      }
    }
  }
}

export const StickyLinksOnly: Story = {
  args: {
    stickyText: '', // No sticky text
    accordionIcon: '/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg',
    text: '', // No button
    buttonLink: {
      valid: false,
      url: 'wwww.cox.com'
    },
    linkDetails: [
      {
        linkText: 'Centered Link1',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'button1'
      },
      {
        linkText: 'Centered Link2',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'anchor1'
      },
      {
        linkText: 'Centered Link3',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false
      }
    ],
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.SECONDARY,
    openInNewTab: false,
    isInEditor: false
  },
  parameters: {
    docs: {
      description: {
        story:
          '**Links-Only Layout**: Navigation links are centered across the full width of the component. Both `stickyText` and `text` fields are empty, creating a clean, centered navigation experience. On mobile, dropdown links remain left-aligned and display "On this page" as the default accordion header text.'
      }
    }
  }
}

export const MobileDefaultText: Story = {
  args: {
    stickyText: '', // Empty sticky text to trigger mobile default
    accordionIcon: '/content/dam/cox/common/icons/ui_components/chevron-down-river-blue.svg',
    text: 'Get Started', // Include button to show all variants work
    buttonLink: {
      valid: false,
      url: 'wwww.cox.com'
    },
    linkDetails: [
      {
        linkText: 'Overview',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'overview'
      },
      {
        linkText: 'Features',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'features'
      },
      {
        linkText: 'Pricing',
        linkPath: '/content/cox/residential/corporate/pages/ria_react/stickyb2',
        openinnewtab: false,
        anchor: 'pricing'
      }
    ],
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.SECONDARY,
    openInNewTab: false,
    isInEditor: false
  },
  parameters: {
    docs: {
      description: {
        story:
          '**Mobile Default Text Demo**: This story demonstrates the mobile default text behavior. When `stickyText` is empty, mobile viewports (XS-LG) will display "On this page" as the default accordion header text. Desktop viewports will show no sticky text section. Mobile dropdown links are always left-aligned. Resize your browser to mobile width or use device simulation to see the mobile default text in action.'
      }
    },
    viewport: {
      defaultViewport: 'mobile1'
    }
  }
}

// Backward compatibility alias
export const Sticky: Story = StickyWithButton
