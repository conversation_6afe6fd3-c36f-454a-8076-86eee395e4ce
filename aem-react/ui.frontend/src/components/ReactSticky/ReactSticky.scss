@import './../../assets/styles/v3/globalStyles.scss';

.react-sticky-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 32px;
  background: var(--background-default);
  padding-bottom: 10px;
  height: auto;
  @media (min-width: $xl) {
    padding-bottom: 50px;
  }
  .sticky-mobile {
    &.expanded {
      position: absolute;
      width: -webkit-fill-available; /* For Chrome and Safari */
      width: -moz-available; /* For Firefox */
      width: stretch; /* For other browsers */
      // more than overlay
      z-index: $zindex-sticky-expanded;
      top: 0;
      left: 0;
      min-height: 220px;
      background-color: var(--background-default);
      transition: none;
    }

    .accordion-container.expanded {
      min-height: 220px;
      background-color: var(--background-default);
    }

    .accordion {
      --bs-accordion-active-bg: none;
      --bs-accordion-border-width: 0px;
      --bs-accordion-btn-focus-box-shadow: none;
      --bs-accordion-btn-icon: none;
      --bs-accordion-btn-active-icon: none;
      --bs-accordion-btn-padding-x: 0px;
      --bs-accordion-btn-padding-y: 0px;
      --bs-accordion-border-radius: 0px;
      width: 100%;
      .accordion-item {
        border: none;
      }
      .accordion-header {
        border-bottom: 1px solid var(--color-neutral-250);
        padding: 12px 24px;
      }
      .accordion-button:not(.collapsed) img {
        transform: rotate(180deg);
      }
      .accordion-button:not(.collapsed) {
        // expanded state of button inside accordion
        .sticky-text {
          @include cox-text-title3-bold;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-title3-bold;
          }
        }
      }
      .accordion-body {
        border-bottom: 1px solid var(--color-neutral-250);
        padding: 0;
      }
      .accordion-item:last-of-type .accordion-collapse {
        width: 100%;
        background: var(--background-default);
      }
    }
    .links-button-container-mobile {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 28px;
      padding: 24px;
    }
    .links-container-mobile {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 0px;
      gap: 28px;
      a {
        text-decoration: none;
      }
    }
    // expanded state of accordion
    &.expanded {
      .accordion {
        .accordion-header {
          border-bottom: 0px;
          padding-bottom: 0px;
        }
      }
    }
  }

  &.is-sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: $zindex-sticky;
  }
  .content-container {
    display: none;
  }
  .sticky-text {
    display: flex;
    @include cox-text-title4-bold;
    justify-content: space-between;
    width: 100%;
    .accordion-chevron {
      width: 24px;
      height: 24px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      display: flex;
      @include cox-text-title4-bold;
      justify-content: space-between;
      width: 100%;
      .accordion-chevron {
        width: 24px;
        height: 24px;
      }
    }
  }

  .content-button {
    display: flex;
  }

  .links-button-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 0px;
    gap: 28px;

    // Center alignment when no button (full width)
    &.justify-content-center {
      justify-content: center;
      width: 100%;
    }
  }

  .link-text {
    text-align: center;
  }

  .link-text {
    cursor: pointer;
  }

  .button .text-button .responsive-size {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 110px;
  }
  .button .text-button .responsive-size .button-text {
    @include cox-text-interactive3;
  }

  @media (min-width: $md) {
    .sticky-mobile {
      .accordion {
        .accordion-header {
          padding: 12px 48px;
        }
      }
      .links-button-container-mobile {
        padding: 24px 48px;
      }
    }
  }

  @media (min-width: $xl) {
    padding: 12px 72px;
    .sticky-text {
      // TODO: override: need to add cox-text-heading
      font-weight: 700;
      font-size: 1.5rem;
      line-height: 2rem;
      letter-spacing: -0.02em;

      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        font-weight: 700;
        font-size: 1.5rem;
        line-height: 2rem;
        letter-spacing: -0.02em;
      }
    }
    display: flex;
    align-items: flex-start;
    align-items: center;
    .sticky-mobile {
      // hide accordion in large screen
      display: none;
    }
    .content-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      gap: 32px;
      width: 100%;
      // &.is-sticky {
      //   justify-content: space-around;
      // }
    }
    .links-container {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      padding: 0px;
      gap: 28px;
      a {
        text-decoration: none;
      }

      // Center alignment when no button (full width)
      &.justify-content-center {
        justify-content: center;
        width: 100%;
      }
    }

    .sticky-text {
      width: unset;

      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        width: unset;
      }
    }
  }
  @media (min-width: $xxl) {
    padding: 12px 0;
  }
}

.cmp-container {
  .react-sticky-container > .content-container {
    padding-left: 0 !important;
    padding-right: 0 !important;

    &.is-sticky {
      padding-left: 24px !important;
      padding-right: 24px !important;
    }
  }
}
