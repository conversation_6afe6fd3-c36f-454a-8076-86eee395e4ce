import { ButtonStates, ButtonTypes, LinkTypes } from '@cox/core-ui8/dist/Button'

export enum ButtonAlignment {
  LEFT = 'left',
  CENTER = 'center'
}

export interface ReactStickyProps {
  stickyText: string
  linkDetails: Array<LinkDetails>
  anchor?: string
  icon: string
  text: string
  buttonStates: ButtonStates
  buttonTypes: ButtonTypes
  buttonLink: ButtonLink
  accessibilityLabel?: string
  isInEditor?: boolean
  openInNewTab?: boolean
  linkUrl?: string
  accordionIcon?: string
  altText?: string
  linkType?: LinkTypes
  linkId?: string
  lob?: string
  alignment?: ButtonAlignment | string
}

export interface ButtonLink {
  valid: boolean
  url: string
}

export interface LinkDetails {
  linkText: string
  anchor?: string
  linkPath?: string
  openinnewtab?: boolean
}
