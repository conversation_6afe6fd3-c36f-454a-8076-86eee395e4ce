// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReactSticky component Button conditional rendering and center alignment renders snapshot without button for center alignment 1`] = `
<div
  className="react-sticky-container  "
  data-testid="react-sticky-container"
>
  <div
    className="sticky-mobile "
    id="react-sticky"
  >
    <div
      className="accordion-container "
    >
      <div
        data-testid="accordion"
      >
        <div
          data-testid="accordion-item"
        >
          <div
            data-testid="accordion-header"
          >
            <div
              className="sticky-text"
            >
              <div
                className="word-wrap rte   "
                data-rte-editelement={true}
                data-testid="rich-text-container"
                id="rich-text"
              >
                Sticky Text
              </div>
              <img
                className="accordion-chevron"
              />
            </div>
          </div>
          <div
            data-testid="accordion-body"
          >
            <div
              className="links-button-container-mobile align-items-center"
            >
              <div
                className="links-container-mobile align-items-center"
              >
                <a
                  aria-label="link1"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link1"
                >
                  link1
                </a>
                <a
                  aria-label="link2"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link2"
                >
                  link2
                </a>
                <a
                  aria-label="link3"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link3"
                >
                  link3
                </a>
                <a
                  aria-label="link4"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link4"
                >
                  link4
                </a>
                <a
                  aria-label="link5"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link5"
                >
                  link5
                </a>
                <a
                  aria-label="link6link1"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link6link1"
                >
                  link6link1
                </a>
                <a
                  aria-label="link7"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link7"
                >
                  link7
                </a>
                <a
                  aria-label="link8"
                  className="link-text cox-text-interactive3"
                  onClick={[Function]}
                  onKeyDown={[Function]}
                  role="button"
                  tabIndex={0}
                  title="link8"
                >
                  link8
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`ReactSticky component Mobile default text behavior mobile default text snapshot 1`] = `
<div
  className="react-sticky-container  "
  data-testid="react-sticky-container"
>
  <div
    className="content-container  container"
  >
    <div
      className="links-button-container"
    >
      <div
        className="links-container"
      >
        <a
          aria-label="link1"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link1"
        >
          link1
        </a>
        <a
          aria-label="link2"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link2"
        >
          link2
        </a>
        <a
          aria-label="link3"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link3"
        >
          link3
        </a>
        <a
          aria-label="link4"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link4"
        >
          link4
        </a>
        <a
          aria-label="link5"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link5"
        >
          link5
        </a>
        <a
          aria-label="link6link1"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link6link1"
        >
          link6link1
        </a>
        <a
          aria-label="link7"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link7"
        >
          link7
        </a>
        <a
          aria-label="link8"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link8"
        >
          link8
        </a>
      </div>
      <div
        className="content-button"
      >
        <button
          anchor="checkbox-1"
          aria-label="alt-text"
          buttonStates="active"
          buttonTypes="primary"
          data-testid="react-button"
          id="sticky-navigation-button"
          linkType="page"
          linkUrl="/#"
          size="small"
          text="cox-button"
        >
          cox-button
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`ReactSticky component it renders the correct snapshot for ReactSticky 1`] = `
<div
  className="react-sticky-container  "
  data-testid="react-sticky-container"
>
  <div
    className="content-container  container"
  >
    <div
      className="sticky-text"
    >
      <div
        className="word-wrap rte   "
        data-rte-editelement={true}
        data-testid="rich-text-container"
        id="rich-text"
      >
        Sticky Text
      </div>
    </div>
    <div
      className="links-button-container"
    >
      <div
        className="links-container"
      >
        <a
          aria-label="link1"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link1"
        >
          link1
        </a>
        <a
          aria-label="link2"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link2"
        >
          link2
        </a>
        <a
          aria-label="link3"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link3"
        >
          link3
        </a>
        <a
          aria-label="link4"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link4"
        >
          link4
        </a>
        <a
          aria-label="link5"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link5"
        >
          link5
        </a>
        <a
          aria-label="link6link1"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link6link1"
        >
          link6link1
        </a>
        <a
          aria-label="link7"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link7"
        >
          link7
        </a>
        <a
          aria-label="link8"
          className="link-text cox-text-interactive3"
          onClick={[Function]}
          onKeyDown={[Function]}
          role="button"
          tabIndex={0}
          title="link8"
        >
          link8
        </a>
      </div>
      <div
        className="content-button"
      >
        <button
          anchor="checkbox-1"
          aria-label="alt-text"
          buttonStates="active"
          buttonTypes="primary"
          data-testid="react-button"
          id="sticky-navigation-button"
          linkType="page"
          linkUrl="/#"
          size="small"
          text="cox-button"
        >
          cox-button
        </button>
      </div>
    </div>
  </div>
</div>
`;
