import { useEffect, useRef, useState } from 'react'

import ReactDOM from 'react-dom'

import { ButtonAlignment, LinkDetails, ReactStickyProps } from './types'
import { STICKY_OVERLAY_ZINDEX } from '../../constants'
import useClickAwayListener from '../../hooks/useClickAwayListener'
import { appendHash } from '../../utils/helper-util'
import { getImageSrc } from '../../utils/local-util'
import { ScreenSizes } from '../../utils/types'
import ReactButton from '../ReactButton'
import ReactText from '../ReactText'
import IsInEditorText from '../shared/IsInEditorText'
import { Accordion } from '../shared/ReactBootstrap'
import './ReactSticky.scss'
import ReactOverlay from '../shared/ReactOverlay'

const ReactSticky = (props: ReactStickyProps) => {
  const {
    text,
    anchor,
    openInNewTab,
    stickyText,
    linkDetails,
    linkUrl,
    buttonStates,
    buttonTypes,
    accessibilityLabel,
    accordionIcon,
    altText,
    isInEditor = false,
    linkType,
    linkId,
    lob = '',
    alignment = ButtonAlignment.CENTER
  } = props
  const [isMobileScreen, setIsMobileScreen] = useState(false)
  const [isSticky, setIsSticky] = useState('')
  const [applyOverlay, setApplyOverlay] = useState(false)
  const hasButton = text && text.trim() !== ''

  const linkClick = (url: string, newTab: boolean, anchorId: string) => {
    const anchorUrl = appendHash(url, anchorId)
    if (anchorId) {
      newTab ? window.open(anchorUrl, '_blank') : window.open(anchorUrl, '_self')
      const stickyNav = document.querySelector('.react-sticky-container.is-sticky')
      const targetElement = document.querySelector(`[id="${anchorId}"]`)
      if (targetElement) {
        const y =
          targetElement?.getBoundingClientRect()?.top + window.scrollY - (stickyNav?.getBoundingClientRect()?.height || 120)
        window.scrollTo({ top: y, behavior: 'smooth' })
      }
    } else if (url) {
      newTab ? window.open(url, '_blank') : window.open(url, '_self')
    }
    hideOverlay()
  }

  const LinkDetails = ({ linkDetails }: { linkDetails: Array<LinkDetails> }) => {
    if (linkDetails?.length < 0) return null
    return (
      <>
        {linkDetails?.length > 0 &&
          linkDetails.map((link: LinkDetails) => {
            const { linkText = '', linkPath = '', openinnewtab = false, anchor = '' } = link
            return (
              <>
                {linkText && (
                  <a
                    role='button'
                    tabIndex={0}
                    aria-label={linkText}
                    title={linkText}
                    className='link-text cox-text-interactive3'
                    onClick={() => linkClick(linkPath, openinnewtab, anchor)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        linkClick(linkPath, openinnewtab, anchor)
                      }
                    }}
                  >
                    {linkText}
                  </a>
                )}
              </>
            )
          })}
      </>
    )
  }

  const stickyRef = useRef(null)

  const resizeSticky = () => {
    window.innerWidth < ScreenSizes.XL ? setIsMobileScreen(true) : (setIsMobileScreen(false), setApplyOverlay(false))
  }
  const applyStickyPosition = () => {
    const stickyElement = ReactDOM.findDOMNode(stickyRef.current) as Element
    const compTop = stickyElement?.getBoundingClientRect().top

    window.scrollY > compTop ? setIsSticky('is-sticky') : setIsSticky('')
  }

  useEffect(() => {
    resizeSticky()
    window.addEventListener('scroll', applyStickyPosition)
    window.addEventListener('resize', resizeSticky)
    return () => {
      window.removeEventListener('scroll', applyStickyPosition)
      window.removeEventListener('resize', resizeSticky)
    }
  }, [])

  const LinksRenderer = () => {
    const mobileClass = isMobileScreen ? '-mobile' : ''

    // Determine alignment classes based on alignment prop and content
    const getAlignmentClass = () => {
      if (isMobileScreen) return ''

      // If no button and no sticky text, center the links
      if (!hasButton && !stickyText) {
        return ' justify-content-center'
      }

      // Apply alignment based on the alignment prop
      if (alignment === ButtonAlignment.LEFT) {
        return ' justify-content-start'
      } else if (alignment === ButtonAlignment.CENTER) {
        return ' justify-content-center'
      }

      // Default to flex-end (right alignment) for backward compatibility
      return ''
    }

    const alignmentClass = getAlignmentClass()

    return (
      <div className={`links-button-container${mobileClass}${alignmentClass}`}>
        <div className={`links-container${mobileClass}${alignmentClass}`}>
          <LinkDetails linkDetails={linkDetails} />
        </div>
        {hasButton && (
          <div className='content-button'>
            <ReactButton
              text={text}
              buttonStates={buttonStates}
              buttonTypes={buttonTypes}
              id='sticky-navigation-button'
              anchor={anchor}
              aria-label={accessibilityLabel}
              linkType={linkType}
              linkId={linkId}
              linkUrl={linkUrl}
              openInNewTab={openInNewTab}
              size={'small'}
            />
          </div>
        )}
      </div>
    )
  }

  const handleClickAway = async () => {
    // TODO: Refactor without using document selector
    const button = document.querySelector('#react-sticky .accordion-button') as HTMLElement
    const buttonaria = document.querySelector('#react-sticky button[aria-expanded="true"]')
    const accordionBody = document.querySelector('#react-sticky .accordion-collapse')
    await button?.classList.add('collapsed')
    await buttonaria?.setAttribute('aria-expanded', 'false')
    await accordionBody?.classList.remove('show')
    button && buttonaria && button?.click()
  }
  const clickAwayRef = useClickAwayListener(handleClickAway)

  const showOverlay = () => {
    setApplyOverlay(true)
    /* setApplyOverlay((applyOverlay: boolean) => {
      if (!applyOverlay) {
        // TODO: Refactor without using document selector
        const button = document.querySelector('#react-sticky .accordion-button') as HTMLElement
        const buttonaria = document.querySelector('#react-sticky button[aria-expanded="false"]')
        const accordionBody = document.querySelector('#react-sticky .accordion-collapse')
        button?.classList.remove('collapsed')
        buttonaria?.setAttribute('aria-expanded', 'true')
        accordionBody?.classList.add('show')
      }
      return !applyOverlay
    }) */
  }
  const hideOverlay = () => {
    setApplyOverlay(false)
  }
  const StickyContentContainer = () => {
    const expanded = applyOverlay ? 'expanded' : ''
    const displayText = isMobileScreen && (!stickyText || stickyText.trim() === '') ? 'On this page' : stickyText

    return isMobileScreen ? (
      <div className={`sticky-mobile ${expanded}`} id={'react-sticky'}>
        <div className={`accordion-container ${expanded}`}>
          <Accordion ref={clickAwayRef} defaultActiveKey={applyOverlay ? '0' : null}>
            <Accordion.Item eventKey='0'>
              <Accordion.Header as={'h3'}>
                <div className='sticky-text'>
                  {displayText && <ReactText text={displayText} />}
                  <img src={getImageSrc(accordionIcon as string)} className='accordion-chevron' aria-label={altText} />
                </div>
              </Accordion.Header>
              <Accordion.Body onEntered={showOverlay} onExited={hideOverlay}>
                <LinksRenderer />
              </Accordion.Body>
            </Accordion.Item>
          </Accordion>
        </div>
      </div>
    ) : (
      <div className={`content-container ${isSticky} container`}>
        {stickyText && (
          <div className='sticky-text'>
            <ReactText text={stickyText} />
          </div>
        )}
        <LinksRenderer />
      </div>
    )
  }

  return (
    <>
      {IsInEditorText(isInEditor, 'React Sticky')}
      <div data-testid={`react-sticky-container`} className={`react-sticky-container ${lob} ${isSticky}`} ref={stickyRef}>
        <StickyContentContainer />
        <ReactOverlay
          active={applyOverlay}
          onClick={hideOverlay}
          zIndex={STICKY_OVERLAY_ZINDEX}
          backgroundColor='rgba(69, 80, 81, 0.70)'
        />
      </div>
    </>
  )
}
export default ReactSticky
