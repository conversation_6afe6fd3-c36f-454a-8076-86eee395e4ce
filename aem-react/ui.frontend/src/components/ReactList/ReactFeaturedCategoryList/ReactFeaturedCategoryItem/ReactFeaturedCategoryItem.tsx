import './ReactFeaturedCategoryItem.scss'
import { ReactFeaturedCategoryItemProps } from './types'
import { getImageSrc } from '../../../../utils/local-util'
import { getTokenValues } from '../../../../utils/rule-builder-util'
import ReactText from '../../../ReactText'
import { FCLIstType } from '../types'

const ReactFeaturedCategoryItem = (props: ReactFeaturedCategoryItemProps): JSX.Element => {
  const {
    name,
    description = '',
    link,
    target,
    imgSrc,
    variation,
    anchor,
    altIconText,
    listType = '',
    tokenProperties = '',
    useRte = false
  } = props

  const hasLink = anchor || link
  const targetLink = anchor ? window.location.href + `#` + anchor : link
  const openinnewtab = target === 'true' ? '_blank' : '_self'
  const encodeTargetLink = targetLink && encodeURI(targetLink)
  const tokenName = name && (getTokenValues(name, tokenProperties) as string)

  const getCategoryName = () => {
    if (useRte) {
      return tokenName && <ReactText text={tokenName} isParsed={true} />
    }

    return (
      <div className='category-name-section'>
        {hasLink ? (
          <a href={encodeTargetLink} target={openinnewtab} className='category-name'>
            {tokenName}
          </a>
        ) : (
          <span className='category-name'>{tokenName}</span>
        )}
      </div>
    )
  }

  return (
    <div data-testid={`react-featured-category-item`} className={`react-featured-category-item`}>
      <div className={`category-card variation-${variation}`}>
        {listType == FCLIstType.WITH_ICON && (
          <div className='category-icon'>
            {hasLink ? (
              <a aria-hidden='true' tabIndex={-1} href={encodeTargetLink} target={openinnewtab}>
                <img src={getImageSrc(imgSrc)} alt={altIconText} />
              </a>
            ) : (
              <img src={getImageSrc(imgSrc)} alt={altIconText} />
            )}
          </div>
        )}
        <div className='category-details'>
          {getCategoryName()}

          {description && (
            <div className='category-description'>{<ReactText text={description} tokenProperties={tokenProperties} />}</div>
          )}
        </div>
      </div>
    </div>
  )
}
export default ReactFeaturedCategoryItem
