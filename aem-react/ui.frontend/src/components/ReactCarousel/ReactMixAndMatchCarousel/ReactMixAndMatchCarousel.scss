@import '../../../assets/styles/v3/globalStyles.scss';

.react-mixandmatch-carousel {
  .mixandmatch-carousel-container {
    display: flex;
    flex-direction: column;
  }

  .react-multi-carousel-list {
    order: 2;
    margin-bottom: 20px;

    @media (min-width: $xl) {
      margin-bottom: 48px;
    }
  }

  .react-multi-carousel-dot-list {
    position: relative;
    order: 99;
    margin-bottom: 40px;

    @media (min-width: $xl) {
      display: none;
    }
  }

  .description {
    display: flex;
    align-items: flex-end;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      @include cox-text-paragraph2-regular;

      word-break: normal;
      word-wrap: break-word;
    }
  }

  .carousel-header-container {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 40px;

    @media (min-width: $md) {
      margin-bottom: 40px;
    }

    @media (min-width: $xl) {
      margin-bottom: 48px;
    }
  }

  $widths: (
    $xs: 272px,
    $sm: 528px,
    $md: 672px,
    $lg: 769px,
    $xl: 876px,
    $xxl: 1002px
  );

  @each $size, $width in $widths {
    @media (min-width: $size) {
      .carousel-header-text-container {
        width: $width;
      }
    }
  }

  .carousel-button-container {
    position: relative;
  }

  @media (min-width: $lg) {
    .action-button-group-container {
      align-self: flex-start;
      padding-right: 50px !important; // this override accounts for the extra 10px seen on the carousel
    }
  }

  @media (min-width: $xxl) {
    .action-button-group-container {
      padding-right: 24px !important; // this override accounts for the lack of extra space
    }
  }

  .carousel-button-group {
    @media (min-width: $xl) {
      display: flex;
      justify-content: space-between;
    }
  }

  .react-multi-carousel-item {
    display: flex;

    @media (max-width: $lg) {
      padding-right: 12px;
    }
  }

  .carousel-header-text-container {
    width: 100%;
    overflow: hidden;
    overflow-wrap: break-word;
    word-break: break-all;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    @media (min-width: $lg) {
      align-items: flex-start;
    }

    .title {
      @include cox-text-heading5;
      color: var(--color-neutral-600);
      word-break: break-word;
    }
  }

  // CAROUSEL ITEMS
  $items: $xs 28px 28px, $sm 28px 28px, $md 28px 28px, $lg 44px 32px, $xl 44px 36px, $xxl 44px 36px;

  @each $size, $width, $padding-y, $padding-x in $items {
    @media (min-width: $size) {
      .carousel-item-container {
        width: 100%;
        padding: $padding-y $padding-x;
      }
    }
  }

  .carousel-item-wrapper {
    width: 100%;
  }

  .carousel-item-container {
    align-self: stretch;
    margin-right: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    background: var(--color-neutral-100);
    border: 2px solid var(--color-neutral-250);
    border-radius: 20px;
    align-items: center;
    justify-content: center;
  }
  & .line-active {
    border: 1px solid var(--icon-speed-100);
    gap: 24px;

    .line-card-header {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 100%;

      .top-container {
        display: flex;
        justify-content: flex-start;
        position: relative;
        align-items: flex-start;
        gap: 26px;

        .image-container {
          border-radius: 99px;
          background-color: var(--color-green-400);
          display: flex;
          flex: 0 1 auto;
          padding: 28px;
          flex-direction: column;
          align-items: center;
          .line-image {
            width: 64px;
            height: 64px;
          }
        }

        .close-line {
          cursor: pointer;
          margin-left: auto;
          .x-blue-icon {
            width: 24px;
            height: 24px;
          }
        }
      }
      .disable-close {
        justify-content: center;
        align-items: center;
      }
      .close-visible {
        & .image-container {
          left: 50%;
          transform: translate(-50%);
          position: relative;
        }
      }

      .line-number-text {
        text-align: center;
        margin-bottom: 0px;
      }
    }
    .plans-container {
      display: flex;
      padding: 16px;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      border-radius: 43px;
      background-color: var(--background-muted-1);

      .button-base {
        gap: 8px !important;
        padding: 16px 20px;

        .button-text {
          text-wrap: nowrap;
          line-height: 1.625rem;
          letter-spacing: 0.0225rem;
        }
        .icon {
          width: 24px;
          height: 24px;
        }
      }
      .mixandmatch {
        color: var(--color-neutral-100) !important;
        background-color: var(--color-blue-900) !important;
        border: none;
      }
      .mixandmatchalt {
        color: var(--color-blue-900) !important;
        background-color: transparent;
        border: none;
      }
    }
    .price-container {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      gap: 8px;

      .price {
        color: var(--accent-pricing-default);
        margin-bottom: 0px;
      }
      .scratch-price {
        @include cox-text-title2-medium;
        font-weight: 400;
        color: var(--foreground-on-default-4);
        text-decoration: line-through;
        margin-bottom: 0px;
      }
    }
  }
  & .line-inactive {
    border: 2px dashed var(--utility-on-default-2);

    .add-line-container {
      display: flex;
      flex-direction: row;
      gap: 8px;

      .add-line-text {
        text-wrap: nowrap;
        margin-bottom: 0px;
      }
      & :hover {
        cursor: pointer;
      }
    }

    .plus-icon {
      width: 16px;
      height: 16px;
    }
  }

  .react-mix-and-match-carousel-footer-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;

    @media (min-width: $md) {
      gap: 20px;
    }

    @media (min-width: $lg) {
      gap: 24px;
    }

    .total-summary-text {
      font-size: 1.125rem;
      font-weight: 400;
      line-height: 1.625rem;
      letter-spacing: -0.0225rem;
      margin-bottom: 0px;

      @media (min-width: $md) {
        font-size: 1rem;
        line-height: 1.375rem;
        letter-spacing: 0.02rem;
      }

      @media (min-width: $xl) {
        font-size: 1.5rem;
        line-height: 2rem;
        letter-spacing: -0.03rem;
      }

      .line-for-price-text {
        color: var(--accent-pricing-default);
        font-weight: 700;
      }

      .scratch-price-text {
        color: var(--foreground-on-default-4);
        text-decoration: line-through;
      }
    }

    .device-options-link {
      text-decoration: none;

      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-interactive2;
        margin-bottom: 0px;

        a {
          text-decoration: none;
        }
      }
    }
  }
}
