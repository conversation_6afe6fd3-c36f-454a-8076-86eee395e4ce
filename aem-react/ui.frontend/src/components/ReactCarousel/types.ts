import { CarouselV1Properties, CoreContainerState } from '@adobe/aem-core-components-react-spa'

import { Flexoffers, RuleType } from '../../utils/types'
import { BadgeVariation } from '../ReactBadge'

export interface ElementObject {
  dataType: string
  title: string
  value: string
  multiValue: boolean
  ':type': string
}

interface ContentBadgeElementObject {
  value: BadgeVariation
}

interface BulletPointsElementObject {
  value: string[]
}

export interface ReactCarouselProps extends CarouselV1Properties {
  ':itemsOrder'?: any
  ':items'?: any
  componentProperties?: any

  carouselType?: string
  id?: string
  items?: object
  eyebrow?: string
  description?: string

  flexOffers?: Flexoffers
  condition?: string
  ruleType?: RuleType
  tokenProperties?: string

  // AUTHORABLE ICONS
  activePip?: string
  inActivePip?: string
  nextButton?: string
  previousButton?: string
  // SPACING
  top?: boolean
  right?: boolean
  bottom?: boolean
  left?: boolean
  lob?: string
  selectedSlideId?: string
  hideMCP?: boolean
}

export interface IContentFragment {
  ':items': any
  ':itemsOrder': any
  ':type': string
  'cq:panelTitle'?: string
  dataLayer: any
  description: string
  elements: object
  elementsOrder: string[]
  id: string
  model: any
  title: string
}

export interface IReactCarouselProperties extends CarouselV1Properties {
  accessibility: {
    play: string
    pause: string
    next: string
    previous: string
    slide: string
    indicator: string
    indicators: string
  }
  contentFragments: any[]
  carouselItems: any[]
  autopauseDisabled: boolean
  autoplay: boolean
  carouselType: string
  componentProperties: object
  controlsPrepended: boolean
  cqItems: any
  cqItemsOrder: string[]
  cqPath: string
  cqType: string
  dataLayer: object
  delay: number
  id: string
  isInEditor: boolean
  items: object
  eyebrow: string
  title: string
  description: string
}

export interface CarouselV1AccessibilityProperties {
  play: string
  pause: string
  next: string
  previous: string
  slide: string
  indicator: string
  indicators: string
}
export interface CarouselV1State extends CoreContainerState {
  activeIndex: number
  previousIndex: number
  isMouseEntered: boolean
  autoPlay: boolean
  width: number
  selectedSlideId?: string
}

export interface IContentFragment {
  ':items': any
  ':itemsOrder': any
  ':type': string
  'cq:panelTitle'?: string
  dataLayer: any
  description: string
  elements: object
  elementsOrder: string[]
  id: string
  model: any
  title: string

  flexOffers?: Flexoffers
  condition?: string
  ruleType?: RuleType
}

export interface IProductContentFragmentElements extends ContentFragmentElements {
  //rule builder
  ruleExpression?: string
  ruleType?: RuleType
  condition?: string
  index?: number
  priceTagIcon?: string
  elements?: ContentFragmentElements
  flexOffers?: Flexoffers
}

export interface IPSUContentFragmentElements extends ContentFragmentElements {
  //rule builder
  ruleExpression?: string
  ruleType?: RuleType
  condition?: string
  index?: number
  priceTagIcon?: string
  elements?: ContentFragmentElements
  hasContentBadge?: boolean
  flexOffers?: Flexoffers
}

export interface IArticleContentFragmentElements extends ContentFragmentElements {
  //rule builder
  ruleExpression?: string
  ruleType?: RuleType
  condition?: string
  index: number
  tokenProperties?: string
  videoIcon?: string
  videoAltTxt?: string
  youtubeAspectRatio?: string
  youtubeHeight?: string
  youtubeWidth?: string
  autoplay?: boolean
  layout?: string
}

export interface ContentFragmentElements {
  minPlacardHeight?: number
  index?: number
  placardRef: any
  image?: ElementObject
  imageParams?: ElementObject
  altImage?: ElementObject
  eyebrow?: ElementObject
  eyebrowIcon?: ElementObject
  eyebrowIconAltText?: ElementObject
  heading?: ElementObject
  additionalNote?: ElementObject
  description?: ElementObject
  linkText?: ElementObject
  linkTextIcon?: ElementObject
  linkIconAltText?: ElementObject
  linkType?: ElementObject
  linkUrl?: ElementObject
  accordionItems?: ElementObject
  bulletIcon?: ElementObject
  bulletIconAltText?: ElementObject
  title1?: ElementObject
  psuItems?: ElementObject
  bulletPoints?: BulletPointsElementObject
  buttonTextPrimary?: ElementObject
  buttonLinkPrimary?: ElementObject
  linkTypeButtonPrimary?: ElementObject
  buttonLinkNewTabPrimary?: ElementObject
  buttonLinkTypePrimary?: ElementObject
  linkIdButtonPrimary?: ElementObject
  buttonPrimarySetDisposition?: ElementObject
  buttonPrimaryDispositionList?: ElementObject
  buttonTextSecondary?: ElementObject
  buttonLinkSecondary?: ElementObject
  buttonLinkTypeSecondary?: ElementObject
  linkTypeButtonSecondary?: ElementObject
  linkIdButtonSecondary?: ElementObject
  buttonLinkNewTabSecondary?: ElementObject
  buttonTextTertiary?: ElementObject
  buttonLinkTertiary?: ElementObject
  buttonLinkNewTabTertiary?: ElementObject
  buttonLinkTypeTertiary?: ElementObject
  linkIdButtonTertiary?: ElementObject
  linkTypeButtonTertiary?: ElementObject
  planName?: ElementObject
  priceChangeText?: ElementObject
  price?: ElementObject
  scratchPrice?: ElementObject
  newBillText?: ElementObject
  term?: ElementObject
  discount?: ElementObject
  contentBadgeColor?: ContentBadgeElementObject
  contentBadgeText?: ElementObject
  contentBadgeText_json?: ElementObject
  tokenrule?: ElementObject
  videoId?: ElementObject
  resourceType?: string
  videoModalId?: ElementObject
  handleVideoSelection?: any //(videoId: string) => void

  ruleExpression?: string
  condition?: string
  ruleType?: RuleType
  tokenProperties?: string
  contentFragmentId?: any
  id?: any
}

export interface IArticleContentFragment extends IContentFragment {
  elements: ContentFragmentElements
  condition?: string
  ruleType?: RuleType
  index?: number
}

export interface ImageryContentFragment extends IContentFragment {
  elements: ContentFragmentElements
  isActive: boolean
  condition?: string
  ruleType?: RuleType
}

export interface IProductContentFragment extends IContentFragment {
  elements: IProductContentFragmentElements
}

export enum ActionButtonTypes {
  NEXT = 'next',
  PREVIOUS = 'previous'
}

export enum ActionButtonDisplay {
  BLOCK = 'block',
  NONE = 'none'
}

export enum CardState {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export enum CarouselTypes {
  ARTICLE = 'article',
  IMAGE = 'imagery',
  PRODUCT = 'product3up',
  MIX_AND_MATCH = 'mixandmatch',
  LOOKING_FOR_MORE = 'lookingformore',
  PSU = 'psu'
}

export enum SourceTypes {
  VIDEO = 'video',
  IMAGE = 'image',
  AUDIO = 'audio',
  LINK = 'link'
}

export const responsiveCarouselProps = {
  xxl: {
    breakpoint: { max: 10000, min: 1400 },
    items: 2,
    slidesToSlide: 1
  },
  xl: {
    breakpoint: { max: 1400, min: 1200 },
    items: 2
  },
  lg: {
    breakpoint: { max: 1200, min: 991 },
    items: 2
  },
  md: {
    breakpoint: { max: 991, min: 768 },
    items: 1
  },
  sm: {
    breakpoint: { max: 768, min: 576 },
    items: 1
  },
  xs: {
    breakpoint: { max: 576, min: 0 },
    items: 1
  }
}

export const responsiveProductCarouselProps = {
  xxl: {
    breakpoint: { max: 10000, min: 1400 },
    items: 3,
    slidesToSlide: 1,
    partialVisibilityGutter: 0
  },
  xl: {
    breakpoint: { max: 1400, min: 1200 },
    items: 3,
    partialVisibilityGutter: 10
  },
  lg: {
    breakpoint: { max: 1200, min: 992 },
    items: 3,
    partialVisibilityGutter: 10
  },
  md: {
    breakpoint: { max: 991, min: 768 },
    items: 1,
    partialVisibilityGutter: 10
  },
  sm: {
    breakpoint: { max: 768, min: 576 },
    items: 1,
    partialVisibilityGutter: 10
  },
  xs: {
    breakpoint: { max: 576, min: 0 },
    items: 1,
    partialVisibilityGutter: 10
  }
}

export const responsiveMixAndMatchCarouselProps = {
  xxl: {
    breakpoint: { max: 10000, min: 1400 },
    items: 4,
    slidesToSlide: 1,
    partialVisibilityGutter: 0
  },
  xl: {
    breakpoint: { max: 1400, min: 1200 },
    items: 3,
    partialVisibilityGutter: 10
  },
  lg: {
    breakpoint: { max: 1200, min: 992 },
    items: 3,
    partialVisibilityGutter: 10
  },
  md: {
    breakpoint: { max: 991, min: 768 },
    items: 2,
    partialVisibilityGutter: 10
  },
  sm: {
    breakpoint: { max: 768, min: 576 },
    items: 1,
    partialVisibilityGutter: 10
  },
  xs: {
    breakpoint: { max: 576, min: 0 },
    items: 1,
    partialVisibilityGutter: 10
  }
}

export interface AnimatedCarouselState extends CarouselV1State {
  additionalTransfrom: number
}
