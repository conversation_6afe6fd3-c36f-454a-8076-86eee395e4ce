@import './../../assets/styles/v3/globalStyles.scss';

.carousel-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(12, 1fr);

  .carousel-grid {
    grid-column: 1 / span 12;
    grid-row: 1 / span 12;
  }
}

// Multi Carousel V2
.react-carousel {
  color: var(--color-neutral-600);
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .carousel-header-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 40px;

    @media (min-width: $md) {
      margin-bottom: 48px;
    }

    @media (min-width: $lg) {
      flex-direction: row;
      align-items: flex-end;
    }

    @media (min-width: $xl) {
      margin-bottom: 56px;
    }
  }

  .carousel-header-text-container {
    overflow: hidden;
    word-break: break-word;
    display: flex;
    flex-direction: column;
  }

  .title {
    display: flex;
    align-items: flex-end;
    word-break: break-word;
  }

  .description {
    display: flex;
    align-items: flex-end;
    margin-top: 20px;

    @media (min-width: $lg) {
      padding-right: 20px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      @include cox-text-paragraph2-regular;
      margin-bottom: 0 !important;
      word-break: normal;
      word-wrap: break-word;

      a {
        color: var(--color-blue-600);
      }
    }
  }

  .carousel-button-container {
    position: relative;
  }

  .carousel-button-group {
    width: 102px;
    height: 40px;
    display: flex;
    justify-content: space-between;

    @media (min-width: $lg) {
      gap: 0.75rem;
    }
  }

  .indicator-container {
    align-items: center;
    justify-content: center;
  }

  .react-multi-carousel-list {
    justify-content: flex-start;

    margin-bottom: 25px;

    @media (min-width: $lg) {
      margin-bottom: 49px;
    }
  }

  .react-multi-carousel-item {
    display: inline-block;

    @media (min-width: $lg) {
      padding-right: 24px;
    }
  }

  .action-button-group-container {
    padding-right: 24px;
    margin-top: 20px;

    @media (min-width: $lg) {
      margin-top: 0px;
    }
  }

  .react-multi-carousel-dot-list {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1.75rem;
  }

  $widths: $xs 272px, $sm 528px, $md 672px, $lg 436px, $xl 516px, $xxl 592px;

  @each $size, $width in $widths {
    @media (min-width: $size) {
      .react-multi-carousel-item {
        width: $width;
      }
    }
  }

  $iconSizes: $xs 20px, $md 40px, $xl 60px;
  @each $size, $iconSize in $iconSizes {
    @media (min-width: $size) {
      .overlay-image img {
        height: $iconSize;
        width: $iconSize;
      }
    }
  }

  .react-video-list {
    $iconSizes: $xs 20px, $sm 30px, $md 40px, $lg 50px, $xl 60px, $xxl 70px;
    @each $size, $iconSize in $iconSizes {
      @media (min-width: $size) {
        .overlay-image img {
          height: $iconSize;
          width: $iconSize;
        }
      }
    }
  }

  .image-container a {
    width: 100%;
  }

  img,
  .image-placeholder {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
  }
}

// custom dot
.carousel-custom-pip {
  .active {
    height: 12px;
    width: 12px;
  }

  .inactive {
    height: 8px;
    width: 8px;
  }
}
