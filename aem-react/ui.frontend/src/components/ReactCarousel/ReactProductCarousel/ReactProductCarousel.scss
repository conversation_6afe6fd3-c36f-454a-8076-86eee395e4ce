@import '../../../assets/styles/v3/globalStyles.scss';

.product-carousel__content {
  .carousel-grid {
    grid-column: 1 / span 12;
    grid-row: 1 / span 12;
  }
}

.react-product-carousel {
  .product-carousel-container {
    display: flex;
    flex-direction: column;
  }

  .react-multi-carousel-list {
    order: 2;
  }

  .align-carousel-items-center {
    @media (min-width: $lg) {
      justify-content: center !important;
    }

    .react-multi-carousel-item {
      @media (min-width: $xxl) {
        max-width: 387px;
      }
    }
  }

  .react-multi-carousel-dot-list {
    position: relative;
    order: 99;
  }

  .description {
    display: flex;
    align-items: flex-end;
    margin-top: 10px;
    padding-right: 20px;
    word-break: normal;
    word-wrap: break-word;
    @include cox-text-paragraph2-regular;
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      @include cox-text-paragraph2-regular;
      word-break: normal;
      word-wrap: break-word;
    }
  }

  .carousel-header-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 40px;

    @media (min-width: $md) {
      margin-bottom: 48px;
    }

    @media (min-width: $lg) {
      flex-direction: row;
      align-items: flex-end;
    }

    @media (min-width: $xl) {
      margin-bottom: 56px;
    }
  }

  $widths: (
    $xs: 272px,
    $sm: 528px,
    $md: 672px,
    $lg: 769px,
    $xl: 876px,
    $xxl: 1002px
  );

  @each $size, $width in $widths {
    @media (min-width: $size) {
      .carousel-header-text-container {
        width: $width;
      }
    }
  }

  .carousel-button-container {
    position: relative;
  }

  @media (max-width: $md) {
    .action-button-group-container {
      margin-top: 20px;
    }
  }

  @media (min-width: $lg) {
    .action-button-group-container {
      padding-right: 50px !important; // this override accounts for the extra 10px seen on the carousel
    }
  }

  @media (min-width: $xxl) {
    .action-button-group-container {
      padding-right: 24px !important; // this override accounts for the lack of extra space
    }
  }

  .carousel-button-group {
    display: flex;
    justify-content: space-between;
  }

  .react-multi-carousel-item {
    display: flex;
    margin-bottom: 32px;

    @media (min-width: $lg) {
      margin-bottom: 43px;
    }

    @media (max-width: $lg) {
      padding-right: 12px;
    }
  }

  .carousel-header-text-container {
    width: 100%;
    overflow: hidden;
    overflow-wrap: break-word;
    word-break: break-all;
    display: flex;
    flex-direction: column;

    align-items: flex-start;

    @media (min-width: $lg) {
      align-items: flex-start;
    }

    .eyebrow {
      @include cox-text-eyebrow1;
      margin-bottom: 10px;
    }

    .title {
      color: var(--color-neutral-600);
      margin: 10px 0;
      word-break: break-word;
    }

    .title-description {
      margin: 10px 0 20px;

      a {
        color: var(--color-blue-600);
      }
    }
  }

  // CAROUSEL ITEMS
  $items: $xs 28px 28px, $sm 28px 28px, $md 28px 28px, $lg 44px 32px, $xl 44px 36px, $xxl 44px 36px;

  @each $size, $padding-y, $padding-x in $items {
    @media (min-width: $size) {
      .carousel-item-container {
        width: 100%;
        padding: $padding-y $padding-x;
      }
    }
  }

  .carousel-item-wrapper {
    width: 100%;
  }

  .carousel-item-container {
    align-self: stretch;
    margin-right: 24px;
    gap: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    background: var(--color-neutral-100);
    border: 2px solid var(--color-neutral-300);
    border-radius: 20px;

    @media (min-width: $lg) {
      gap: 32px;
    }

    .top-section {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .top-section-card-header {
        .carousel-icon {
          margin: 20px 0;

          img {
            border-radius: 50%;
            width: 54px;
            height: 54px;

            @media (min-width: $md) {
              width: 86px;
              height: 86px;
            }
          }

          .img-placeholder {
            border-radius: 50%;
            width: 54px;
            height: 54px;

            @media (min-width: $md) {
              width: 86px;
              height: 86px;
            }
          }
        }

        .item-eyebrow {
          display: flex;
          align-items: center;
          @include cox-text-eyebrow1;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-eyebrow1;
          }
          margin-bottom: 12px;

          .eyebrow-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 999px;
          }
        }

        .header {
          @include cox-text-title1-bold;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-title1-bold;
          }
          word-break: break-word;
        }

        .additional-note {
          @include cox-text-paragraph3-regular;
          color: var(--color-neutral-500);
          margin-top: 4px;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph3-regular;
            color: var(--color-neutral-500);
          }
        }
      }

      .top-section-card-body {
        .body-description {
          @include cox-text-paragraph2-regular;
          color: var(--color-neutral-400) !important;
          margin-bottom: 12px;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph2-regular;
            color: var(--color-neutral-400) !important;
          }
        }

        .bullet-list {
          .bullet-list-item {
            display: flex;
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              margin: 0;
            }
          }

          .bullet-icon {
            width: 16px;
            height: 20px;
            padding-top: 4px;
          }

          .bullet-text {
            @include cox-text-paragraph3-medium;
            color: var(--color-neutral-500);
            white-space: pre-wrap;
            word-break: break-word;
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-paragraph3-medium;
              color: var(--color-neutral-500);
              white-space: pre-wrap;
              word-break: break-word;
            }
          }
        }
      }
    }

    .bottom-section {
      .plan-section {
        .plan-name {
          @include cox-text-title2-bold;
          color: var(--color-neutral-400);
          margin-bottom: 8px;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-title2-bold;
            color: var(--color-neutral-400);
          }
        }

        .plan-price {
          display: flex;
          flex-wrap: wrap;

          // Price & scratch price alignment in cards:
          $price-alignments: $xs flex-start column, $sm center row, $lg flex-start column, $xl center row;
          @each $size, $align-items-value, $flex-direction-value in $price-alignments {
            @media (min-width: $size) {
              align-items: $align-items-value;
              flex-direction: $flex-direction-value;
            }
          }

          .plan-price-actual {
            @include cox-text-title1-bold;
            color: var(--color-blue-900);
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-title1-bold;
              color: var(--color-blue-900);
            }
            margin-right: 8px;
          }

          .plan-price-crossed-out {
            @include cox-text-paragraph3-medium;
            text-align: right;
            text-decoration-line: line-through;
            color: var(--color-neutral-400);
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-paragraph3-medium;
              text-align: right;
              text-decoration-line: line-through;
              color: var(--color-neutral-400);
            }
          }
        }

        .term-agreement {
          @include cox-text-paragraph3-medium;
          color: var(--color-neutral-600);
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph3-medium;
            color: var(--color-neutral-600);
          }
          margin-top: 4px;
          margin-bottom: 4px;
        }

        .discount-applied {
          display: flex;
          align-items: center;
          @include cox-text-paragraph5-medium;
          color: var(--color-neutral-600);
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph5-medium;
            color: var(--color-neutral-600);
            margin-bottom: 0;
          }

          .discount-icon {
            height: 13px;
            width: 13px;
            margin-right: 4px;
          }
        }
      }

      .product-carousel-button-section {
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        gap: 20px;

        @media (min-width: $lg) {
          margin-top: 32px;
        }

        .button-base {
          width: 100%;
        }
      }
    }
  }
}
