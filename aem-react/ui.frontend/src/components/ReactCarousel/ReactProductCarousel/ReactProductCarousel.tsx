import './ReactProductCarousel.scss'

import { useEffect, useRef } from 'react'

import { ButtonTypes } from '@cox/core-ui8/dist/Button'
import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'

import ReactProductCarouselSkeleton from './ReactProductCarouselSkeleton'
import { CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON, CAROUSEL_INACTIVE_ARROW_BUTTON, PRICE_TAG } from '../../../assets/images'
import { DynamicMediaComponents, getDynamicMediaFromSrc } from '../../../utils/dynamic-media-util'
import { scrollToCarouselElementWithOffset } from '../../../utils/helper-util'
import { getAttributeValue, getConditionResult, getOffersLength, hasCFConditionals } from '../../../utils/rule-builder-util'
import { ContentType, RuleType, ScreenSizes } from '../../../utils/types'
import ReactBadge from '../../ReactBadge'
import ReactButton from '../../ReactButton'
import ReactList, { ListType } from '../../ReactList'
import ReactText from '../../ReactText'
import ActionButton from '../ActionButton'
import CustomDot from '../CustomDot'
import ReactMultiCarouselV2 from '../ReactMultiCarouselV2'
import {
  ActionButtonTypes,
  IProductContentFragment,
  IProductContentFragmentElements,
  responsiveProductCarouselProps
} from '../types'
interface ProductCarouselRef {
  goToSlide: (slide: number) => void
}
const ReactProductCarousel = (props: any): JSX.Element => {
  const {
    id = '',
    eyebrow,
    title,
    description,
    carouselActiveButton,
    carouselDisabledButton,
    carouselActiveButtonAltText = 'Next',
    carouselDisabledButtonAltText = 'Previous',
    activePip,
    inActivePip,
    activePipAltText,
    inActivePipAltText,
    ruleExpression = '',
    condition,
    ruleType,
    tokenProperties,
    contentType,
    activeAuthoredIndex,
    priceTagIcon,
    maxSlides,
    contentFragments,
    selectedSlideId,
    resetSlideId,
    isInEditor = false
  } = props

  const flexOffers = useFlexOffers(ruleExpression, contentType, contentFragments, maxSlides, 'productcarousel')
  const slideIdMap = new Map()
  const productCarouselRef = useRef<ProductCarouselRef | any>(null)
  const offersLength = getOffersLength(maxSlides, flexOffers)

  useEffect(() => {
    if (selectedSlideId) {
      const index = slideIdMap.get(selectedSlideId)
      if (index > -1) {
        const cardsLength =
          contentType === ContentType.dynamic || contentType === ContentType.copy ? offersLength : contentFragments.length
        let indexToScrollTo = index
        if (window.innerWidth >= ScreenSizes.LG && index > cardsLength - 3) {
          indexToScrollTo = index - 2
          if (indexToScrollTo < 0) {
            indexToScrollTo = 0
          }
        }
        scrollToCarouselElementWithOffset({}, '#', selectedSlideId)
        productCarouselRef?.current?.goToSlide(indexToScrollTo)
      }
    }
    return () => {
      resetSlideId?.()
    }
  }, [selectedSlideId])

  const getCarouselItems = () => {
    const cfItem = contentFragments[0]

    if (contentType === ContentType.copy) {
      for (let i = 1; i < offersLength; i++) {
        contentFragments.push(cfItem)
      }
    }

    const carouselItems = contentFragments.map((cf: IProductContentFragment, index: any) => {
      const getcfValue = (attribute: any, index: number, propertyName?: string) => {
        if (hasCFConditionals(attribute)) {
          const jsonAttribute = (props as any)?.[`${propertyName}_json`]
          return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
        }
        return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
      }
      const slideIdToSet = cf?.elements.contentFragmentId?.value
        ? getcfValue(cf?.elements.contentFragmentId, index, 'contentFragmentId')
        : getcfValue(cf?.id, index, 'id')
      slideIdMap.set(slideIdToSet, index)
      const displayCards =
        (index < offersLength && contentType === ContentType.dynamic) ||
        (index < offersLength && contentType === ContentType.copy) ||
        contentType === ContentType.static

      if (flexOffers?.loading) {
        return <ReactProductCarouselSkeleton />
      } else if (displayCards) {
        return (
          <ProductCarouselSlide
            key={`${cf.id}_product`}
            ruleExpression={ruleExpression}
            condition={condition}
            ruleType={ruleType}
            tokenProperties={tokenProperties}
            {...cf.elements}
            elements={cf.elements}
            index={index}
            priceTagIcon={priceTagIcon}
            flexOffers={flexOffers}
            id={cf?.id}
          />
        )
      }
      return null
    })
    //filters out any empty values
    return carouselItems.filter((elm: any) => elm)
  }

  const carouselItems = getCarouselItems()
  return (
    <div data-testid={`react-product-carousel`} className={`react-product-carousel`}>
      <div className='product-carousel-container'>
        <div className='react-carousel responsive-size'>
          <ReactMultiCarouselV2
            ref={productCarouselRef}
            //custom props
            id={id}
            eyebrow={eyebrow}
            title={title}
            description={description}
            //multicarousel props
            ssr={true}
            partialVisible={true}
            responsive={responsiveProductCarouselProps}
            arrows={false}
            renderButtonGroupOutside={carouselItems.length > 2}
            customButtonGroup={
              <ButtonGroup
                nextButton={carouselActiveButton}
                previousButton={carouselDisabledButton}
                nextButtonAltText={carouselActiveButtonAltText}
                previousButtonAltText={carouselDisabledButtonAltText}
              />
            }
            showDots={carouselItems.length > 1}
            renderDotsOutside={true}
            swipeable={true}
            customDot={
              <CustomDot
                activePip={activePip}
                inActivePip={inActivePip}
                activePipAltText={activePipAltText}
                inActivePipAltText={inActivePipAltText}
              />
            }
            flexOffers={flexOffers}
            activeAuthoredIndex={activeAuthoredIndex}
            className={carouselItems.length < 3 ? 'align-carousel-items-center' : 'align-carousel-items-flex-start'}
            isInEditor={isInEditor}
          >
            {carouselItems.length > 0 &&
              carouselItems?.map((carouselItem: any) => {
                return (
                  <div className='carousel-item-wrapper' key={JSON.stringify(carouselItem.key)}>
                    {carouselItem}
                  </div>
                )
              })}
          </ReactMultiCarouselV2>
        </div>
      </div>
    </div>
  )
}

// get value if it is dynamic, or else return value authored value

const ProductCarouselSlide = (props: IProductContentFragmentElements) => {
  const {
    image,
    imageParams,
    altImage,
    eyebrow,
    eyebrowIcon,
    eyebrowIconAltText,
    heading,
    additionalNote,
    description,
    bulletPoints,

    buttonTextPrimary,
    buttonLinkPrimary,
    buttonLinkNewTabPrimary,
    buttonLinkTypePrimary,
    linkTypeButtonPrimary,
    linkIdButtonPrimary,

    buttonTextSecondary,
    buttonLinkSecondary,
    buttonLinkNewTabSecondary,
    buttonLinkTypeSecondary,
    linkIdButtonSecondary,
    linkTypeButtonSecondary,

    buttonTextTertiary,
    buttonLinkTertiary,
    buttonLinkNewTabTertiary,
    buttonLinkTypeTertiary,
    linkIdButtonTertiary,
    linkTypeButtonTertiary,

    planName,
    price,
    scratchPrice,
    term,
    discount,
    contentBadgeColor,
    contentBadgeText,

    ruleExpression = '',
    index,
    condition,
    ruleType,
    tokenProperties,
    priceTagIcon = PRICE_TAG,
    elements,
    contentFragmentId = { value: '' },
    id = '',
    flexOffers
  } = props

  const getValue = (attribute: any, propertyName?: string) => {
    if (hasCFConditionals(attribute)) {
      const jsonAttribute = (props as any)?.[`${propertyName}_json`]
      return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
    }
    return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
  }

  // flex offers success
  let conditionResult = true
  if (flexOffers?.success && ruleType === RuleType.conditional && condition) {
    conditionResult = getConditionResult(condition, flexOffers)
  }

  if (conditionResult) {
    const contentFragmentIdValue = getValue(contentFragmentId, 'contentFragmentId')
    const idValue = getValue(id, 'id')
    const slideId = contentFragmentIdValue || idValue
    const contentBadgeTextValue = getValue(contentBadgeText, 'contentBadgeText')
    const contentBadgeColorValue = getValue(contentBadgeColor, 'contentBadgeColor')
    const altImageValue = getValue(altImage, 'altImage')
    const imageParamsValue = getValue(imageParams, 'imageParams')
    const imageValue = getDynamicMediaFromSrc(
      getValue(image, 'image') as string,
      (imageParamsValue as DynamicMediaComponents) || DynamicMediaComponents.CAROUSEL_PRODUCT
    )
    const eyebrowIconValue = getValue(eyebrowIcon, 'eyebrowIcon')
    const eyebrowIconAltTextValue = getValue(eyebrowIconAltText, 'eyebrowIconAltText')
    const eyebrowValue = getValue(eyebrow, 'eyebrow')
    const headingValue = getValue(heading, 'heading')
    const additionalNoteValue = getValue(additionalNote, 'additionalNote')
    const descriptionValue = getValue(description, 'description')
    const planNameValue = getValue(planName, 'planName')
    const priceValue = getValue(price, 'price')
    const scratchPriceValue = getValue(scratchPrice, 'scratchPrice')
    const termValue = getValue(term, 'term')
    const discountValue = getValue(discount, 'discount')
    const buttonTextPrimaryValue = getValue(buttonTextPrimary, 'buttonTextPrimary')
    const buttonTextSecondaryValue = getValue(buttonTextSecondary, 'buttonTextSecondary')
    const buttonLinkPrimaryValue = getValue(buttonLinkPrimary, 'buttonLinkPrimary')
    const buttonLinkTypePrimaryValue = getValue(buttonLinkTypePrimary, 'buttonLinkTypePrimary')
    const linkTypeButtonPrimaryValue = getValue(linkTypeButtonPrimary, 'linkTypeButtonPrimary')
    const linkIdButtonPrimaryValue = getValue(linkIdButtonPrimary, 'linkIdButtonPrimary')
    const buttonLinkSecondaryValue = getValue(buttonLinkSecondary, 'buttonLinkSecondary')
    const buttonLinkTypeSecondaryValue = getValue(buttonLinkTypeSecondary, 'buttonLinkTypeSecondary')
    const linkTypeButtonSecondaryValue = getValue(linkTypeButtonSecondary, 'linkTypeButtonSecondary')
    const linkIdButtonSecondaryValue = getValue(linkIdButtonSecondary, 'linkIdButtonSecondary')
    const buttonLinkNewTabPrimaryValue = getValue(buttonLinkNewTabPrimary, 'buttonLinkNewTabPrimary')
    const buttonLinkNewTabSecondaryValue = getValue(buttonLinkNewTabSecondary, 'buttonLinkNewTabSecondary')
    const hasEyebrowIcon = eyebrowIcon?.value || eyebrowIconAltText?.value
    const buttonTextTertiaryValue = getValue(buttonTextTertiary, 'buttonTextTertiary')
    const buttonLinkTertiaryValue = getValue(buttonLinkTertiary, 'buttonLinkTertiary')
    const buttonLinkTypeTertiaryValue = getValue(buttonLinkTypeTertiary, 'buttonLinkTypeTertiary')
    const linkTypeButtonTertiaryValue = getValue(linkTypeButtonTertiary, 'linkTypeButtonTertiary')
    const linkIdButtonTertiaryValue = getValue(linkIdButtonTertiary, 'linkIdButtonTertiary')
    const buttonLinkNewTabTertiaryValue = getValue(buttonLinkNewTabTertiary, 'buttonLinkNewTabTertiary')

    const primaryButtonType = buttonLinkTypePrimaryValue || ButtonTypes.PRIMARY
    const secondaryButtonType = buttonLinkTypeSecondaryValue || ButtonTypes.SECONDARY
    const tertiaryButtonType = buttonLinkTypeTertiaryValue || ButtonTypes.TERTIARY

    return (
      <div className='carousel-item-container' id={slideId}>
        {/* Top */}
        <div className='top-section'>
          <div className='top-section-card-header'>
            {/* Badge */}
            {contentBadgeTextValue && (
              <div className='carousel-badge'>
                <ReactBadge
                  contentBadgeText={contentBadgeTextValue}
                  contentBadgeColor={contentBadgeColorValue}
                  isInContainer={true}
                />
              </div>
            )}
            {/* Image */}
            {imageValue && (
              <div className='carousel-icon'>
                <img src={imageValue} alt={altImageValue} title={altImageValue}></img>
              </div>
            )}
            {/* Eyebrow with Image */}
            {eyebrowValue && (
              <div className='item-eyebrow'>
                {hasEyebrowIcon && <img src={eyebrowIconValue} alt={eyebrowIconAltTextValue} className='eyebrow-icon' />}
                {/* {eyebrowValue} */}
                <ReactText text={eyebrowValue} isParsed />
              </div>
            )}
            {/* Header */}
            {headingValue && <ReactText text={headingValue} isParsed className='header' />}
            {/* Additional Note */}
            {additionalNoteValue && <ReactText text={additionalNoteValue} isParsed className='additional-note' />}
          </div>
          <div className='top-section-card-body'>
            {/* Description */}
            {descriptionValue && <ReactText text={descriptionValue} isParsed className='body-description' />}
            {/* Bulleted List */}
            {bulletPoints?.value && (
              <ReactList
                listFrom={ListType.customList}
                elements={elements}
                ruleExpression={ruleExpression}
                index={index}
                tokenProperties={tokenProperties}
              />
            )}
          </div>
        </div>

        {/* Bottom */}
        <div className='bottom-section'>
          {/* Plan Section */}
          <div className='plan-section'>
            {planNameValue && <ReactText className='plan-name' text={planNameValue} isParsed />}
            <div className='plan-price'>
              {priceValue && <ReactText className='plan-price-actual' text={priceValue} isParsed />}
              {scratchPriceValue && <ReactText className='plan-price-crossed-out' text={scratchPriceValue} isParsed />}
            </div>
            {termValue && <ReactText className='term-agreement' text={termValue} isParsed />}
            {discountValue && discountValue.length > 0 && (
              <div className='discount-applied'>
                {/* TODO: read pricetag and alt text from props */}
                {priceTagIcon && <img src={priceTagIcon} className='discount-icon' aria-label='discount-icon'></img>}
                <ReactText text={discountValue} isParsed />
              </div>
            )}
          </div>
          {/* Buttons */}
          <div className='product-carousel-button-section'>
            {buttonTextPrimaryValue.length > 0 && (
              <ReactButton
                text={buttonTextPrimaryValue}
                buttonTypes={primaryButtonType}
                linkType={linkTypeButtonPrimaryValue}
                linkId={linkIdButtonPrimaryValue}
                linkUrl={buttonLinkPrimaryValue}
                openInNewTab={buttonLinkNewTabPrimaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
            {buttonTextSecondaryValue.length > 0 && (
              <ReactButton
                text={buttonTextSecondaryValue}
                buttonTypes={secondaryButtonType}
                linkType={linkTypeButtonSecondaryValue}
                linkId={linkIdButtonSecondaryValue}
                linkUrl={buttonLinkSecondaryValue}
                openInNewTab={buttonLinkNewTabSecondaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
            {buttonTextTertiaryValue.length > 0 && (
              <ReactButton
                text={buttonTextTertiaryValue}
                buttonTypes={tertiaryButtonType}
                linkType={linkTypeButtonTertiaryValue}
                linkId={linkIdButtonTertiaryValue}
                linkUrl={buttonLinkTertiaryValue}
                openInNewTab={buttonLinkNewTabTertiaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
  return <></>
}

const ButtonGroup = (props: any) => {
  const {
    carouselState: { currentSlide, totalItems },
    next,
    previous,
    nextButton = CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON,
    previousButton = CAROUSEL_INACTIVE_ARROW_BUTTON,
    nextButtonAltText,
    previousButtonAltText
  } = props

  let isNextButtonDisabled = currentSlide === totalItems - 3

  if (window.innerWidth < ScreenSizes.LG) {
    isNextButtonDisabled = currentSlide === totalItems - 1
  }

  return (
    <div className='carousel-button-group'>
      <ActionButton
        buttonType={ActionButtonTypes.PREVIOUS}
        isDisabled={currentSlide === 0}
        type='button'
        onClick={() => previous()}
        aria-label={previousButtonAltText}
        aria-disabled={currentSlide === 0}
        //authorable icons
        carouselActiveButtonAltText={nextButtonAltText}
        carouselDisabledButtonAltText={previousButtonAltText}
        nextButton={nextButton}
        previousButton={previousButton}
        tabIndex={currentSlide === 0 ? -1 : 0}
      />
      <ActionButton
        buttonType={ActionButtonTypes.NEXT}
        isDisabled={isNextButtonDisabled}
        type='button'
        onClick={() => next()}
        aria-label={nextButtonAltText}
        aria-disabled={isNextButtonDisabled}
        //authorable icons
        carouselActiveButtonAltText={nextButtonAltText}
        carouselDisabledButtonAltText={previousButtonAltText}
        nextButton={nextButton}
        previousButton={previousButton}
        tabIndex={isNextButtonDisabled ? -1 : 0}
      />
    </div>
  )
}

export default ReactProductCarousel
