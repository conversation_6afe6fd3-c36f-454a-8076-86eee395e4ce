import './ReactLookingForMoreCarousel.scss'

import { useEffect, useRef } from 'react'

import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'

import { CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON, CAROUSEL_INACTIVE_ARROW_BUTTON } from '../../../assets/images'
import { DynamicMediaComponents, getDynamicMediaFromSrc } from '../../../utils/dynamic-media-util'
import { scrollToCarouselElementWithOffset } from '../../../utils/helper-util'
import { getAttributeValue, getConditionResult, getOffersLength, hasCFConditionals } from '../../../utils/rule-builder-util'
import { ContentType, RuleType, ScreenSizes } from '../../../utils/types'
import ReactImage from '../../ReactImage'
import ReactText from '../../ReactText'
import ActionButton from '../ActionButton'
import CustomDot from '../CustomDot'
import ReactMultiCarouselV2 from '../ReactMultiCarouselV2'
import { ActionButtonDisplay, ActionButtonTypes, IProductContentFragment, responsiveProductCarouselProps } from '../types'
interface LookingForMoreCarouselRef {
  goToSlide: (slide: number) => void
}
const ReactLookingForMoreCarousel = (props: any): JSX.Element => {
  const {
    title,
    carouselActiveButton,
    carouselDisabledButton,
    carouselActiveButtonAltText = 'Next',
    carouselDisabledButtonAltText = 'Previous',
    activePip,
    inActivePip,
    activePipAltText,
    inActivePipAltText,
    ruleExpression = '',
    condition,
    ruleType,
    tokenProperties,
    contentType,
    activeAuthoredIndex,
    maxSlides,
    contentFragments,
    selectedSlideId,
    resetSlideId,
    colorVariation = 'onwhitelilac',
    isInEditor = false
  } = props

  const flexOffers = useFlexOffers(ruleExpression, contentType, contentFragments, maxSlides, 'lookingformorecarousel')
  const slideIdMap = new Map()
  const lookingForMoreCarouselRef = useRef<LookingForMoreCarouselRef | any>(null)

  useEffect(() => {
    if (selectedSlideId) {
      const index = slideIdMap.get(selectedSlideId)
      let indexToScrollTo = index
      if (window.innerWidth >= ScreenSizes.LG && index > contentFragments?.length - 3) {
        indexToScrollTo = index - 2
        if (indexToScrollTo < 0) {
          indexToScrollTo = 0
        }
      }
      scrollToCarouselElementWithOffset({}, '#', selectedSlideId)
      lookingForMoreCarouselRef?.current?.goToSlide(indexToScrollTo)
    }
    return () => {
      resetSlideId?.()
    }
  }, [selectedSlideId])

  const getCarouselItems = () => {
    const cfItem = contentFragments[0]
    const offersLength = getOffersLength(maxSlides, flexOffers)
    if (contentType === ContentType.copy) {
      for (let i = 1; i < offersLength; i++) {
        contentFragments.push(cfItem)
      }
    }

    const carouselItems = contentFragments.map((cf: IProductContentFragment, index: any) => {
      const getcfValue = (attribute: any, index: number, propertyName?: string) => {
        if (hasCFConditionals(attribute)) {
          const jsonAttribute = (props as any)?.[`${propertyName}_json`]
          return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
        }
        return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
      }
      const slideIdToSet = cf?.elements.contentFragmentId?.value
        ? getcfValue(cf?.elements.contentFragmentId, index, 'contentFragmentId')
        : getcfValue(cf?.id, index, 'id')
      slideIdMap.set(slideIdToSet, index)
      const displayCards =
        (index < offersLength && contentType === ContentType.dynamic) ||
        (index < offersLength && contentType === ContentType.copy) ||
        contentType === ContentType.static

      if (displayCards) {
        return (
          <LookingForMoreCarouselSlide
            colorVariation={colorVariation}
            key={`${cf.id}_lookingformore`}
            ruleExpression={ruleExpression}
            condition={condition}
            ruleType={ruleType}
            tokenProperties={tokenProperties}
            {...cf.elements}
            elements={cf.elements}
            index={index}
            flexOffers={flexOffers}
            id={cf?.id}
          />
        )
      }
      return null
    })
    //filters out any empty values
    return carouselItems.filter((elm: any) => elm)
  }

  const carouselItems = getCarouselItems()

  let renderButtonGroups = carouselItems.length > 3
  if (window.innerWidth < ScreenSizes.LG) {
    renderButtonGroups = carouselItems.length >= 2
  }

  return (
    <div data-testid={`react-looking-for-more-carousel`} className={`react-looking-for-more-carousel`}>
      <div className='looking-for-more-carousel-container'>
        <div
          className={`react-carousel responsive-size ${
            carouselItems.length > 3 ? 'show-dot-list-in-desktop' : 'hide-dot-list-in-desktop'
          }`}
        >
          <ReactMultiCarouselV2
            ref={lookingForMoreCarouselRef}
            title={title}
            // Multicarousel props
            ssr={true}
            partialVisible={false}
            responsive={responsiveProductCarouselProps}
            arrows={false}
            renderButtonGroupOutside={renderButtonGroups}
            customButtonGroup={
              <ButtonGroup
                nextButton={carouselActiveButton}
                previousButton={carouselDisabledButton}
                nextButtonAltText={carouselActiveButtonAltText}
                previousButtonAltText={carouselDisabledButtonAltText}
                carouselItemsLength={carouselItems.length}
              />
            }
            showDots={carouselItems.length > 1}
            renderDotsOutside={true}
            swipeable={true}
            customDot={
              <CustomDot
                activePip={activePip}
                inActivePip={inActivePip}
                activePipAltText={activePipAltText}
                inActivePipAltText={inActivePipAltText}
              />
            }
            flexOffers={flexOffers}
            activeAuthoredIndex={activeAuthoredIndex}
            className={carouselItems.length < 3 ? 'align-carousel-items-center' : 'align-carousel-items-flex-start'}
            isInEditor={isInEditor}
          >
            {carouselItems.length > 0 &&
              carouselItems?.map((carouselItem: any) => {
                return (
                  <div className='carousel-item-wrapper' key={JSON.stringify(carouselItem.key)}>
                    {carouselItem}
                  </div>
                )
              })}
          </ReactMultiCarouselV2>
        </div>
      </div>
    </div>
  )
}

// get value if it is dynamic, or else return value authored value

const ConditionalWrapper = (props: any) => {
  const { condition, wrapper, children } = props
  return condition ? wrapper(children) : children
}

const LookingForMoreCarouselSlide = (props: any) => {
  const {
    colorVariation,
    image,
    imageParams,
    altImage,
    heading,
    headingLink,
    headingLinkNewTab,
    description,
    index,
    condition,
    ruleType,
    tokenProperties,
    contentFragmentId = { value: '' },
    id = '',
    flexOffers
  } = props

  const getValue = (attribute: any, propertyName?: string) => {
    if (hasCFConditionals(attribute)) {
      const jsonAttribute = (props as any)?.[`${propertyName}_json`]
      return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
    }
    return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
  }

  // flex offers success
  let conditionResult = true
  if (flexOffers?.success && ruleType === RuleType.conditional && condition) {
    conditionResult = getConditionResult(condition, flexOffers)
  }

  if (conditionResult) {
    const contentFragmentIdValue = getValue(contentFragmentId, 'contentFragmentId')
    const idValue = getValue(id, 'id')
    const slideId = contentFragmentIdValue || idValue
    const altImageValue = getValue(altImage, 'altImage')
    const imageParamsValue = getValue(imageParams, 'imageParams')
    const imageValue = getDynamicMediaFromSrc(
      getValue(image, 'image') as string,
      (imageParamsValue as DynamicMediaComponents) || DynamicMediaComponents.CAROUSEL_PRODUCT
    )

    const headingValue = getValue(heading, 'heading')
    const headingLinkValue = getValue(headingLink, 'headingLink')
    const headingLinkNewTabValue = getValue(headingLinkNewTab, 'headingLinkNewTab ')
    const descriptionValue = getValue(description, 'description')
    const openinnewtab = headingLinkNewTabValue === true ? '_blank' : '_self'

    return (
      <ConditionalWrapper
        condition={headingLinkValue}
        wrapper={(children: any) => (
          <a href={headingLinkValue} target={openinnewtab} className={`carousel-item-container-link ${colorVariation}-card`}>
            {children}
          </a>
        )}
      >
        <div className={`carousel-item-container ${colorVariation}-container`} id={slideId}>
          {/* Top Section */}
          <div className='top-section'>
            <div className='image-heading-pair'>
              {/* Image */}
              {imageValue && <ReactImage src={imageValue} alt={altImageValue} asBG />}
              {/* Link */}
              {headingValue && <div className='header'>{headingValue}</div>}
            </div>
          </div>
          {/* Bottom Section */}
          {/* Description */}
          {descriptionValue && <ReactText text={descriptionValue} isParsed className='heading-description' />}
        </div>
      </ConditionalWrapper>
    )
  }
  return <></>
}

const ButtonGroup = (props: any) => {
  const {
    carouselState: { currentSlide, totalItems },
    next,
    previous,
    nextButton = CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON,
    previousButton = CAROUSEL_INACTIVE_ARROW_BUTTON,
    nextButtonAltText,
    previousButtonAltText,
    carouselItemsLength
  } = props

  let isNextButtonDisabled = currentSlide === totalItems - 3
  let displayCarouselActionButton = carouselItemsLength === 3

  if (window.innerWidth < ScreenSizes.LG) {
    isNextButtonDisabled = currentSlide === totalItems - 1
    displayCarouselActionButton = carouselItemsLength === 1
  }

  return (
    <div className='carousel-button-group'>
      <div>
        <ActionButton
          buttonType={ActionButtonTypes.PREVIOUS}
          isDisabled={currentSlide === 0}
          type='button'
          onClick={() => previous()}
          aria-label={previousButtonAltText}
          aria-disabled={currentSlide === 0}
          //authorable icons
          carouselActiveButtonAltText={nextButtonAltText}
          carouselDisabledButtonAltText={previousButtonAltText}
          nextButton={nextButton}
          previousButton={previousButton}
          tabIndex={currentSlide === 0 ? -1 : 0}
          display={displayCarouselActionButton ? ActionButtonDisplay.NONE : ActionButtonDisplay.BLOCK}
        />
      </div>
      <div>
        <ActionButton
          buttonType={ActionButtonTypes.NEXT}
          isDisabled={isNextButtonDisabled}
          type='button'
          onClick={() => next()}
          aria-label={nextButtonAltText}
          aria-disabled={isNextButtonDisabled}
          //authorable icons
          carouselActiveButtonAltText={nextButtonAltText}
          carouselDisabledButtonAltText={previousButtonAltText}
          nextButton={nextButton}
          previousButton={previousButton}
          tabIndex={isNextButtonDisabled ? -1 : 0}
          display={displayCarouselActionButton ? ActionButtonDisplay.NONE : ActionButtonDisplay.BLOCK}
        />
      </div>
    </div>
  )
}

export default ReactLookingForMoreCarousel
