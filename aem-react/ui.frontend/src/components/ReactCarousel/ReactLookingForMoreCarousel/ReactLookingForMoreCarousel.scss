@import '../../../assets/styles/v3/globalStyles.scss';

.product-carousel__content {
  .carousel-grid {
    grid-column: 1 / span 12;
    grid-row: 1 / span 12;
  }
}

.react-looking-for-more-carousel {
  .looking-for-more-carousel-container {
    display: flex;
    flex-direction: column;
  }

  .react-multi-carousel-list {
    order: 2;
    margin-bottom: 32px !important;
    // To be able to see the bottom box-shadow on hover:
    overflow: visible;

    @media (min-width: $lg) {
      margin-bottom: 56px !important;
    }
  }

  .align-carousel-items-center {
    @media (min-width: $lg) {
      justify-content: center !important;
    }

    .react-multi-carousel-item {
      @media (min-width: $xxl) {
        max-width: 387px;
      }
    }
  }

  // Dot list:
  .react-multi-carousel-dot-list {
    position: relative;
    order: 99;
  }

  // When less than 3 cards in larger screens, dot list should be hidden:
  .hide-dot-list-in-desktop {
    .react-multi-carousel-dot-list {
      @media (min-width: $lg) {
        display: none;
      }
    }
  }

  .description {
    display: flex;
    align-items: flex-end;
    margin-top: 10px;
    padding-right: 20px;
    word-break: normal;
    word-wrap: break-word;
    @include cox-text-paragraph2-regular;
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      @include cox-text-paragraph2-regular;
      word-break: normal;
      word-wrap: break-word;
    }
  }

  .carousel-header-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 48px;

    @media (min-width: $lg) {
      flex-direction: row;
      align-items: flex-end;
    }

    @media (min-width: $xl) {
      margin-bottom: 56px;
    }
  }

  $widths: (
    $xs: 272px,
    $sm: 528px,
    $md: 672px,
    $lg: 769px,
    $xl: 876px,
    $xxl: 1002px
  );

  @each $size, $width in $widths {
    @media (min-width: $size) {
      .carousel-header-text-container {
        width: $width;
      }
    }
  }

  .action-button-group-container {
    padding-right: 36px;
    margin-top: 20px;

    @media (min-width: $lg) {
      margin-top: 0px;
    }

    .carousel-button-group {
      display: flex;

      @media (min-width: $lg) {
        gap: 24px !important;
      }
    }
  }

  $react-multi-carousel-items: $xs 272px, $sm 528px, $md 672px, $lg 283px, $xl 336px, $xxl 386px;

  .react-multi-carousel-item {
    display: flex;

    @media (max-width: $lg) {
      padding-right: 12px;
    }
  }

  .carousel-header-text-container {
    width: 100%;
    overflow: hidden;
    overflow-wrap: break-word;
    word-break: break-all;
    display: flex;
    flex-direction: column;

    align-items: flex-start;

    @media (min-width: $lg) {
      align-items: flex-start;
    }

    .eyebrow {
      @include cox-text-eyebrow1;
      margin-bottom: 10px;
    }

    .title {
      color: var(--color-neutral-600);
      margin: 10px 0;
      word-break: break-word;
    }

    .title-description {
      margin: 10px 0 20px;

      a {
        color: var(--color-blue-600);
      }
    }
  }

  .carousel-item-wrapper {
    width: 100%;
  }

  .carousel-item-container-link {
    text-decoration: none;
  }

  .carousel-item-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-self: stretch;
    height: 100%;
    width: 100%;
    padding: 32px;
    border-radius: 20px;

    @media (min-width: $md) {
      padding: 36px;
    }

    @media (min-width: $xl) {
      gap: 24px;
    }

    .top-section {
      .image-heading-pair {
        display: flex;
        flex-direction: row;
        gap: 16px;

        .bg-image {
          width: 54px;
          height: 54px;
          background-size: contain;

          @media (min-width: $xl) {
            width: 86px;
            height: 86px;
          }
        }

        .img-placeholder {
          border-radius: 50%;
          width: 54px;
          height: 54px;

          @media (min-width: $md) {
            width: 86px;
            height: 86px;
          }
        }

        // Header for the item
        .header {
          @include cox-text-interactive2;
          justify-content: center;
          align-self: center;
          word-break: break-word;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-interactive2;
          }
        }
      }
    }

    // Heading description for the item
    .heading-description {
      @include cox-text-paragraph2-regular;
      color: var(--color-neutral-500) !important;
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        @include cox-text-paragraph2-regular;
        color: var(--color-neutral-500) !important;
      }
    }
  }

  /*************************** BACKGROUND VARIATIONS ***************************/
  /********* TRANSITION ANIMATION *********/
  .onwhitelilac-container,
  .onwhite-container {
    transition: background-color 300ms ease-in-out, box-shadow 300ms ease-in-out;
  }

  /********* VARIATION: onwhitelilac *********/
  .onwhitelilac-container {
    background: var(--color-neutral-200);
    border: 2px solid rgba(255, 255, 255, 0);
  }

  .onwhitelilac-container:hover {
    background-color: var(--color-blue-100);
    box-shadow: $elevation-1;
  }

  .onwhitelilac-container:focus {
    border: 2px solid var(--color-blue-600);
    background: var(--color-neutral-200);
  }

  /********* VARIATION: onwhite *********/
  .onwhite-container {
    background: var(--color-neutral-100);
    border: 2px solid var(--color-neutral-300);
  }

  .onwhite-container:hover {
    background-color: var(--color-blue-100);
    box-shadow: $elevation-1;
  }

  .onwhite-container:focus {
    border: 2px solid var(--color-blue-500);
  }
}
