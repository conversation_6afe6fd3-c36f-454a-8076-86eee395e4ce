import './ReactPSUCarousel.scss'

import { useEffect, useRef } from 'react'

import { Accordion, ACCORDION_VARIATION } from '@cox/core-ui8/dist/Accordion'
import { ButtonTypes } from '@cox/core-ui8/dist/Button'
import { useFlexOffers } from '@cox/core-ui8/dist/useFlexOffers'
import parse from 'html-react-parser'
import { renderToString } from 'react-dom/server'

import ReactPSUCarouselSkeleton from './ReactPSUCarouselSkeleton'
import {
  CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON,
  CAROUSEL_INACTIVE_ARROW_BUTTON,
  PRICE_TAG,
  SM_CHEVRON_DOWN_RIVER_BLUE
} from '../../../assets/images'
import { DynamicMediaComponents, getDynamicMediaFromSrc } from '../../../utils/dynamic-media-util'
import { groupBy, scrollToCarouselElementWithOffset } from '../../../utils/helper-util'
import { get<PERSON>ttribute<PERSON><PERSON>ue, getCondition<PERSON><PERSON><PERSON>, getOffers<PERSON>ength, hasCF<PERSON>onditionals } from '../../../utils/rule-builder-util'
import { ContentType, RuleType, ScreenSizes } from '../../../utils/types'
import ReactBadge from '../../ReactBadge'
import ReactButton from '../../ReactButton'
import ReactPrice, { PriceVariant } from '../../ReactPrice'
import ReactText from '../../ReactText'
import ActionButton from '../ActionButton'
import CustomDot from '../CustomDot'
import ReactMultiCarouselV2 from '../ReactMultiCarouselV2'
import {
  ActionButtonTypes,
  IProductContentFragment,
  IPSUContentFragmentElements,
  responsiveProductCarouselProps
} from '../types'

interface PSUCarouselRef {
  goToSlide: (slide: number) => void
}
const ReactPSUCarousel = (props: any): JSX.Element => {
  const {
    eyebrow,
    title,
    description,
    carouselActiveButton,
    carouselDisabledButton,
    carouselActiveButtonAltText = 'Next',
    carouselDisabledButtonAltText = 'Previous',
    activePip,
    inActivePip,
    activePipAltText,
    inActivePipAltText,
    ruleExpression = '',
    condition,
    ruleType,
    tokenProperties,
    contentType,
    activeAuthoredIndex,
    priceTagIcon,
    maxSlides,
    contentFragments,
    selectedSlideId,
    resetSlideId,
    isInEditor = false
  } = props

  const contentBadgeTextVal = contentFragments.some(
    (cf: IPSUContentFragmentElements) => cf?.elements?.['contentBadgeText']?.value
  )
  const contentBadgeText_jsonVal = contentFragments.some(
    (cf: IPSUContentFragmentElements) => cf?.elements?.['contentBadgeText_json']?.value
  )
  const hasContentBadge = contentFragments.length > 0 ? contentBadgeTextVal || contentBadgeText_jsonVal : false

  const flexOffers = useFlexOffers(ruleExpression, contentType, contentFragments, maxSlides, 'psucarousel')
  const slideIdMap = new Map()
  const psuCarouselRef = useRef<PSUCarouselRef | any>(null)
  const offersLength = getOffersLength(maxSlides, flexOffers)

  useEffect(() => {
    if (selectedSlideId) {
      const index = slideIdMap.get(selectedSlideId)
      if (index > -1) {
        const cardsLength =
          contentType === ContentType.dynamic || contentType === ContentType.copy ? offersLength : contentFragments.length
        let indexToScrollTo = index
        if (window.innerWidth >= ScreenSizes.LG && index > cardsLength - 3) {
          indexToScrollTo = index - 2
          if (indexToScrollTo < 0) {
            indexToScrollTo = 0
          }
        }
        scrollToCarouselElementWithOffset({}, '#', selectedSlideId)
        psuCarouselRef?.current?.goToSlide(indexToScrollTo)
      }
    }
    return () => {
      resetSlideId?.()
    }
  }, [selectedSlideId])

  const getCarouselItems = () => {
    const cfItem = contentFragments[0]

    if (contentType === ContentType.copy) {
      for (let i = 1; i < offersLength; i++) {
        contentFragments.push(cfItem)
      }
    }

    const carouselItems = contentFragments.map((cf: IProductContentFragment, index: any) => {
      const getcfValue = (attribute: any, index: number, propertyName?: string) => {
        if (hasCFConditionals(attribute)) {
          const jsonAttribute = (props as any)?.[`${propertyName}_json`]
          return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
        }
        return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
      }
      const slideIdToSet = cf?.elements.contentFragmentId?.value
        ? getcfValue(cf?.elements.contentFragmentId, index, 'contentFragmentId')
        : getcfValue(cf?.id, index, 'id')
      slideIdMap.set(slideIdToSet, index)
      const displayCards =
        (index < offersLength && contentType === ContentType.dynamic) ||
        (index < offersLength && contentType === ContentType.copy) ||
        contentType === ContentType.static

      if (flexOffers?.loading) {
        return <ReactPSUCarouselSkeleton />
      } else if (displayCards) {
        return (
          <PSUCarouselSlide
            key={`${cf.id}_psu`}
            ruleExpression={ruleExpression}
            condition={condition}
            ruleType={ruleType}
            tokenProperties={tokenProperties}
            {...cf.elements}
            elements={cf.elements}
            index={index}
            priceTagIcon={priceTagIcon}
            flexOffers={flexOffers}
            id={cf?.id}
            hasContentBadge={hasContentBadge}
          />
        )
      }
      return null
    })
    //filters out any empty values
    return carouselItems.filter((elm: any) => elm)
  }

  const carouselItems = getCarouselItems()
  return (
    <div data-testid={`react-psu-carousel`} className={`react-psu-carousel`}>
      <div className='psu-carousel-container'>
        <div className='react-carousel responsive-size'>
          <ReactMultiCarouselV2
            ref={psuCarouselRef}
            //custom props
            eyebrow={eyebrow}
            title={title}
            description={description}
            //multicarousel props
            ssr={true}
            partialVisible={false}
            responsive={responsiveProductCarouselProps}
            arrows={false}
            renderButtonGroupOutside={carouselItems.length > 2}
            customButtonGroup={
              <ButtonGroup
                nextButton={carouselActiveButton}
                previousButton={carouselDisabledButton}
                nextButtonAltText={carouselActiveButtonAltText}
                previousButtonAltText={carouselDisabledButtonAltText}
              />
            }
            showDots={carouselItems.length > 1}
            renderDotsOutside={true}
            swipeable={true}
            customDot={
              <CustomDot
                activePip={activePip}
                inActivePip={inActivePip}
                activePipAltText={activePipAltText}
                inActivePipAltText={inActivePipAltText}
              />
            }
            flexOffers={flexOffers}
            activeAuthoredIndex={activeAuthoredIndex}
            className={carouselItems.length < 3 ? 'align-carousel-items-center' : 'align-carousel-items-flex-start'}
            tokenProperties={tokenProperties}
            isInEditor={isInEditor}
          >
            {carouselItems.length > 0 &&
              carouselItems?.map((carouselItem: any) => {
                return (
                  <div className='carousel-item-wrapper' key={JSON.stringify(carouselItem.key)}>
                    {carouselItem}
                  </div>
                )
              })}
          </ReactMultiCarouselV2>
        </div>
      </div>
    </div>
  )
}

const PSUCarouselSlide = (props: IPSUContentFragmentElements) => {
  const {
    image,
    altImage,
    hasContentBadge,
    contentBadgeColor,
    contentBadgeText,
    eyebrow,
    eyebrowIcon,
    eyebrowIconAltText,
    heading,
    additionalNote,
    description,

    // Props for static and dynamic accordions:
    accordionItems,
    bulletIcon,

    // Props for conditional accordions:
    title1,
    psuItems,
    bulletPoints,

    buttonTextPrimary,
    buttonLinkPrimary,
    buttonLinkNewTabPrimary,
    buttonLinkTypePrimary,
    linkTypeButtonPrimary,
    linkIdButtonPrimary,
    buttonPrimarySetDisposition,
    buttonPrimaryDispositionList,

    buttonTextSecondary,
    buttonLinkSecondary,
    buttonLinkNewTabSecondary,
    buttonLinkTypeSecondary,
    linkIdButtonSecondary,
    linkTypeButtonSecondary,

    buttonTextTertiary,
    buttonLinkTertiary,
    buttonLinkNewTabTertiary,
    buttonLinkTypeTertiary,
    linkIdButtonTertiary,
    linkTypeButtonTertiary,

    // Price section props:
    planName,
    priceChangeText,
    price,
    scratchPrice,
    newBillText,
    term,
    discount,

    ruleExpression = '',
    index,
    condition,
    ruleType,
    tokenProperties,
    priceTagIcon = PRICE_TAG,
    // elements,
    contentFragmentId = { value: '' },
    id = '',
    flexOffers
  } = props

  const getValue = (attribute: any, propertyName?: string) => {
    if (hasCFConditionals(attribute)) {
      const jsonAttribute = (props as any)?.[`${propertyName}_json`]
      return getAttributeValue(jsonAttribute, flexOffers, index, tokenProperties, propertyName)
    }
    return getAttributeValue(attribute, flexOffers, index, tokenProperties, propertyName)
  }

  // flex offers success
  let conditionResult = true
  if (flexOffers?.success && ruleType === RuleType.conditional && condition) {
    conditionResult = getConditionResult(condition, flexOffers)
  }

  if (conditionResult) {
    const contentFragmentIdValue = getValue(contentFragmentId, 'contentFragmentId')
    const idValue = getValue(id, 'id')
    const slideId = contentFragmentIdValue || idValue
    const contentBadgeTextValue = getValue(contentBadgeText, 'contentBadgeText')
    const contentBadgeColorValue = getValue(contentBadgeColor, 'contentBadgeColor')
    const imageValue = getDynamicMediaFromSrc(getValue(image, 'image'), DynamicMediaComponents.CAROUSEL_PSU)
    const altImageValue = getValue(altImage, 'altImage')
    const eyebrowIconValue = getValue(eyebrowIcon, 'eyebrowIcon')
    const eyebrowIconAltTextValue = getValue(eyebrowIconAltText, 'eyebrowIconAltText')
    const eyebrowValue = getValue(eyebrow, 'eyebrow')
    const headingValue = getValue(heading, 'heading')
    const additionalNoteValue = getValue(additionalNote, 'additionalNote')
    const descriptionValue = getValue(description, 'description')
    const planNameValue = getValue(planName, 'planName')
    const priceChangeTextValue = getValue(priceChangeText, 'priceChangeText')
    const priceValue = getValue(price, 'price')
    const scratchPriceValue = getValue(scratchPrice, 'scratchPrice')
    const newBillTextValue = getValue(newBillText, 'newBillText')
    const termValue = getValue(term, 'term')
    const discountValue = getValue(discount, 'discount')
    const buttonTextPrimaryValue = getValue(buttonTextPrimary, 'buttonTextPrimary')
    const buttonTextSecondaryValue = getValue(buttonTextSecondary, 'buttonTextSecondary')
    const buttonLinkPrimaryValue = getValue(buttonLinkPrimary, 'buttonLinkPrimary')
    const buttonLinkTypePrimaryValue = getValue(buttonLinkTypePrimary, 'buttonLinkTypePrimary')
    const linkTypeButtonPrimaryValue = getValue(linkTypeButtonPrimary, 'linkTypeButtonPrimary')
    const linkIdButtonPrimaryValue = getValue(linkIdButtonPrimary, 'linkIdButtonPrimary')
    const buttonPrimarySetDispositionValue = getValue(buttonPrimarySetDisposition, 'buttonPrimarySetDisposition')
    const buttonPrimaryDispositionListValue = getValue(buttonPrimaryDispositionList, 'buttonPrimaryDispositionList')
    const buttonLinkSecondaryValue = getValue(buttonLinkSecondary, 'buttonLinkSecondary')
    const buttonLinkTypeSecondaryValue = getValue(buttonLinkTypeSecondary, 'buttonLinkTypeSecondary')
    const linkTypeButtonSecondaryValue = getValue(linkTypeButtonSecondary, 'linkTypeButtonSecondary')
    const linkIdButtonSecondaryValue = getValue(linkIdButtonSecondary, 'linkIdButtonSecondary')
    const buttonLinkNewTabPrimaryValue = getValue(buttonLinkNewTabPrimary, 'buttonLinkNewTabPrimary')
    const buttonLinkNewTabSecondaryValue = getValue(buttonLinkNewTabSecondary, 'buttonLinkNewTabSecondary')
    const hasEyebrowIcon = eyebrowIcon?.value || eyebrowIconAltText?.value
    const bulletIconValue = getValue(bulletIcon, 'bulletIcon')
    const buttonTextTertiaryValue = getValue(buttonTextTertiary, 'buttonTextTertiary')
    const buttonLinkTertiaryValue = getValue(buttonLinkTertiary, 'buttonLinkTertiary')
    const buttonLinkTypeTertiaryValue = getValue(buttonLinkTypeTertiary, 'buttonLinkTypeTertiary')
    const linkTypeButtonTertiaryValue = getValue(linkTypeButtonTertiary, 'linkTypeButtonTertiary')
    const linkIdButtonTertiaryValue = getValue(linkIdButtonTertiary, 'linkIdButtonTertiary')
    const buttonLinkNewTabTertiaryValue = getValue(buttonLinkNewTabTertiary, 'buttonLinkNewTabTertiary')

    let accordionItemsValue = []

    if (accordionItems) {
      // If accordion is static or dynamic, then accordionItems are passed and processed as follows:
      for (let i = 0; i < accordionItems?.value.length; i++) {
        const accordionItem = JSON.parse(accordionItems.value[i])

        const title1Value = getValue(accordionItem.title1, 'title1')
        const title2Value = getValue(accordionItem.title2, 'title2')
        const accordionIconValue = getValue(accordionItem.accordionIcon, 'accordionIcon')
        const accordionIconAlttxtValue = getValue(accordionItem.accordionIconAlttxt, 'accordionIconAlttxt')
        const bulletPointsValue = getValue(accordionItem.bulletPoints, 'bulletPoints')

        accordionItem.title1 = title1Value
        accordionItem.title2 = title2Value
        accordionItem.accordionIcon = accordionIconValue
        accordionItem.accordionIconAlttxt = accordionIconAlttxtValue
        const filteredBulletPointsValue = bulletPointsValue
          .toString()
          .split('\n')
          .filter((item: string) => item.length > 1)
        accordionItem.bulletPoints = filteredBulletPointsValue
        accordionItem.bulletIcon = bulletIconValue

        if (accordionItem.title1 && accordionItem.bulletPoints.length > 0) {
          accordionItemsValue.push(accordionItem)
        }
      }
    } else if (psuItems && title1 && bulletPoints) {
      // If conditional accordion, then psuItems, title1, & bulletPoints props are passed and processed as follows:
      const psuItemsValue = psuItems?.value
      const title1Value = title1?.value
      const bulletPointsValue = bulletPoints?.value

      /**** BASELINE ACCORDION ITEM ****/

      // To get baseline accordionItems based on the psuItems passed:
      for (const element of psuItemsValue) {
        const accordionItem = JSON.parse(element)

        const productNameValue = getValue(accordionItem.productName, 'productName')
        const title2Value = getValue(accordionItem.title2, 'title2')

        accordionItem.productName = productNameValue
        accordionItem.title2 = title2Value

        accordionItemsValue.push(accordionItem)
      }

      /**** TITLE1 ****/

      // Get title1 rule expressions by product name:
      const title1RuleExpressions = []
      for (const element of title1Value) {
        const title1Item = JSON.parse(element)
        title1RuleExpressions.push(title1Item)
      }
      const groupedByProductName = groupBy(title1RuleExpressions, (product: any) => product.productName)

      // Get unique productNames (these correspond to unique accordionItems):
      const productItems = Object.keys(groupedByProductName)

      // Get title1 values evaluated by productName:
      for (const productItem of productItems) {
        const title1Item = getAttributeValue(
          { value: groupedByProductName[productItem], dataType: 'string' },
          flexOffers,
          index,
          tokenProperties,
          'title1',
          false
        )

        const accordionIconItem = getAttributeValue(
          { value: groupedByProductName[productItem], dataType: 'string' },
          flexOffers,
          index,
          tokenProperties,
          'accordionIcon',
          false,
          'accordionIcon'
        )

        const accordionIconAlttxtItem = getAttributeValue(
          { value: groupedByProductName[productItem], dataType: 'string' },
          flexOffers,
          index,
          tokenProperties,
          'accordionIconAlttxt',
          false,
          'accordionIconAlttxt'
        )

        for (const accordionItem of accordionItemsValue) {
          if (accordionItem.productName == productItem) {
            accordionItem.title1 = title1Item
            accordionItem.accordionIcon = accordionIconItem
            accordionItem.accordionIconAlttxt = accordionIconAlttxtItem
          }
        }
      }

      /**** BULLET POINTS ****/

      // Get bullet point rule expressions by product name:
      const bulletPointsRuleExpressions = []
      for (const element of bulletPointsValue) {
        const bulletPointItem = JSON.parse(element)
        bulletPointsRuleExpressions.push(bulletPointItem)
      }
      const groupedBulletsByProductName = groupBy(bulletPointsRuleExpressions, (product: any) => product.productName)

      // Get bullet point values evaluated by productName:
      for (const productItem of productItems) {
        const bulletItems = getAttributeValue(
          { value: groupedBulletsByProductName[productItem], dataType: 'string' },
          flexOffers,
          index,
          tokenProperties,
          'bulletPoints'
        )

        for (const accordionItem of accordionItemsValue) {
          if (accordionItem.productName == productItem) {
            const filteredBulletPointsValue = renderToString(bulletItems)
              .split('\n')
              .filter((item: string) => item.length > 1)

            // Convert filteredBulletPointsValue object to array and parse each bullet point to render HTML:
            const filteredBulletPointsArray = Object.entries(filteredBulletPointsValue)
            const bulletPointsArray = []
            for (let i = 0; i < filteredBulletPointsArray.length; i++) {
              bulletPointsArray.push(parse(filteredBulletPointsArray[i][1]))
            }

            accordionItem.bulletPoints = bulletPointsArray
          }
        }
      }

      // Filters out any accordions without a title1 or any bullet points:
      accordionItemsValue = accordionItemsValue.filter(
        (accordionItem) => accordionItem?.title1?.length > 1 && accordionItem.bulletPoints.length > 0
      )
    }

    const primaryButtonType = buttonLinkTypePrimaryValue || ButtonTypes.PRIMARY
    const secondaryButtonType = buttonLinkTypeSecondaryValue || ButtonTypes.TERTIARY
    const tertiaryButtonType = buttonLinkTypeTertiaryValue || ButtonTypes.TERTIARY
    return (
      <div className='carousel-item-container' id={slideId}>
        {/* Top */}
        <div className='top-section'>
          {/* Badge */}
          {hasContentBadge && (
            <ReactBadge
              contentBadgeText={contentBadgeTextValue}
              contentBadgeColor={contentBadgeColorValue}
              isInContainer={true}
            />
          )}
          {/* Image */}
          {imageValue && (
            <div className='carousel-icon'>
              <img src={imageValue} alt={altImageValue} title={altImageValue}></img>
            </div>
          )}
          {/* Eyebrow with Image */}
          {eyebrowValue && (
            <div className='item-eyebrow'>
              {hasEyebrowIcon && <img src={eyebrowIconValue} alt={eyebrowIconAltTextValue} className='eyebrow-icon' />}
              <ReactText text={eyebrowValue} isParsed />
            </div>
          )}
          {/* Header */}
          {headingValue && <ReactText text={headingValue} isParsed className='header' />}
          {/* Additional Note */}
          {additionalNoteValue && <ReactText text={additionalNoteValue} isParsed className='additional-note' />}
          {/* Description */}
          {descriptionValue && <ReactText text={descriptionValue} isParsed className='heading-description' />}
          {/* Accordion */}
          <Accordion
            accordionIcon={SM_CHEVRON_DOWN_RIVER_BLUE}
            variation={ACCORDION_VARIATION.PSU}
            accordionItems={accordionItemsValue}
            defaultActiveKey={'accordion-item-1'}
          />
        </div>

        {/* Bottom */}
        <div className='bottom-section'>
          {/* Price Section */}
          <ReactPrice
            variant={PriceVariant.PSU}
            planName={planNameValue}
            priceChangeText={priceChangeTextValue}
            price={priceValue}
            scratchPrice={scratchPriceValue}
            newBillText={newBillTextValue}
            discount={discountValue}
            priceTagIcon={priceTagIcon}
            term={termValue}
          />
          {/* Buttons */}
          <div className='psu-carousel-button-section'>
            {buttonTextPrimaryValue.length > 0 && (
              <ReactButton
                text={buttonTextPrimaryValue}
                buttonTypes={primaryButtonType}
                linkType={linkTypeButtonPrimaryValue}
                linkId={linkIdButtonPrimaryValue}
                linkUrl={buttonLinkPrimaryValue}
                openInNewTab={buttonLinkNewTabPrimaryValue}
                setDisposition={buttonPrimarySetDispositionValue}
                offerId={slideId}
                dispositionList={buttonPrimaryDispositionListValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
            {buttonTextSecondaryValue.length > 0 && (
              <ReactButton
                text={buttonTextSecondaryValue}
                buttonTypes={secondaryButtonType}
                linkType={linkTypeButtonSecondaryValue}
                linkId={linkIdButtonSecondaryValue}
                linkUrl={buttonLinkSecondaryValue}
                openInNewTab={buttonLinkNewTabSecondaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
            {buttonTextTertiaryValue.length > 0 && (
              <ReactButton
                text={buttonTextTertiaryValue}
                buttonTypes={tertiaryButtonType}
                linkType={linkTypeButtonTertiaryValue}
                linkId={linkIdButtonTertiaryValue}
                linkUrl={buttonLinkTertiaryValue}
                openInNewTab={buttonLinkNewTabTertiaryValue}
                index={index}
                ruleExpression={ruleExpression}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
  return <></>
}

const ButtonGroup = (props: any) => {
  const {
    carouselState: { currentSlide, totalItems },
    next,
    previous,
    nextButton = CAROUSEL_DARK_BLUE_ACTIVE_ARROW_BUTTON,
    previousButton = CAROUSEL_INACTIVE_ARROW_BUTTON,
    nextButtonAltText,
    previousButtonAltText
  } = props

  let isNextButtonDisabled = currentSlide === totalItems - 3

  if (window.innerWidth < ScreenSizes.LG) {
    isNextButtonDisabled = currentSlide === totalItems - 1
  }

  return (
    <div className='carousel-button-group'>
      <ActionButton
        buttonType={ActionButtonTypes.PREVIOUS}
        isDisabled={currentSlide === 0}
        type='button'
        onClick={() => previous()}
        //authorable icons
        nextButton={nextButton}
        previousButton={previousButton}
        carouselActiveButtonAltText={nextButtonAltText}
        carouselDisabledButtonAltText={previousButtonAltText}
        tabIndex={currentSlide === 0 ? -1 : 0}
      />
      <ActionButton
        buttonType={ActionButtonTypes.NEXT}
        isDisabled={isNextButtonDisabled}
        type='button'
        onClick={() => next()}
        //authorable icons
        nextButton={nextButton}
        previousButton={previousButton}
        carouselActiveButtonAltText={nextButtonAltText}
        carouselDisabledButtonAltText={previousButtonAltText}
        aria-disabled={isNextButtonDisabled}
        tabIndex={isNextButtonDisabled ? -1 : 0}
      />
    </div>
  )
}

export default ReactPSUCarousel
