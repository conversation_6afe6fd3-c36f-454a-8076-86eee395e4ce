@import '../../../assets/styles/v3/globalStyles.scss';

.psu-carousel__content {
  .carousel-grid {
    grid-column: 1 / span 12;
    grid-row: 1 / span 12;
  }
}

.react-psu-carousel {
  .psu-carousel-container {
    display: flex;
    flex-direction: column;
  }

  .react-multi-carousel-list {
    margin-bottom: 0px !important;
    order: 2;
  }

  .align-carousel-items-center {
    @media (min-width: $lg) {
      justify-content: center !important;
    }

    .react-multi-carousel-item {
      padding-right: 23px !important;

      @media (min-width: $xxl) {
        max-width: 387px;
      }
    }
  }

  .react-multi-carousel-dot-list {
    position: relative;
    order: 99;
    margin-top: 40px;

    @media (min-width: $lg) {
      margin-top: 23px;
    }
  }

  .description {
    display: flex;
    align-items: flex-end;
    margin-top: 10px;
    padding-right: 20px;
    word-break: normal;
    word-wrap: break-word;
    @include cox-text-paragraph2-regular;
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      @include cox-text-paragraph2-regular;
      word-break: normal;
      word-wrap: break-word;
    }
  }

  .carousel-header-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 40px;

    @media (min-width: $lg) {
      flex-direction: row;
      align-items: flex-end;
      gap: 87px;
    }
  }

  $widths: (
    $xs: 272px,
    $sm: 528px,
    $md: 672px,
    $lg: 769px,
    $xl: 876px,
    $xxl: 1002px
  );

  @each $size, $width in $widths {
    @media (min-width: $size) {
      .carousel-header-text-container {
        width: $width;
      }
    }
  }

  .carousel-button-container {
    position: relative;
  }

  @media (max-width: $md) {
    .action-button-group-container {
      margin-top: 20px;
    }
  }

  @media (min-width: $lg) {
    .action-button-group-container {
      padding-right: 50px !important; // this override accounts for the extra 10px seen on the carousel
    }
  }

  @media (min-width: $xxl) {
    .action-button-group-container {
      padding-right: 24px !important; // this override accounts for the lack of extra space
    }
  }

  .carousel-button-group {
    display: flex;
    justify-content: space-between;
  }

  $react-multi-carousel-items: $xs 272px, $sm 528px, $md 672px, $lg 283px, $xl 336px, $xxl 386px;

  .react-multi-carousel-item {
    display: flex;

    @media (min-width: $lg) {
      margin-bottom: 43px;
    }

    @media (max-width: $lg) {
      padding-right: 12px;
    }
  }

  .carousel-header-text-container {
    width: 100%;
    overflow: hidden;
    overflow-wrap: break-word;
    word-break: break-all;
    display: flex;
    flex-direction: column;

    align-items: flex-start;

    @media (min-width: $lg) {
      align-items: flex-start;
    }

    .eyebrow {
      @include cox-text-eyebrow1;
      margin-bottom: 10px;
    }

    .title {
      color: var(--color-neutral-600) !important;
      margin: 10px 0;
      word-break: break-word;
    }

    .title-description {
      margin: 10px 0 20px;

      a {
        color: var(--color-blue-600) !important;
      }
    }
  }

  // CAROUSEL ITEMS
  $items: $xs 28px 28px, $sm 28px 28px, $md 28px 28px, $lg 44px 32px, $xl 44px 36px, $xxl 44px 36px;

  @each $size, $padding-y, $padding-x in $items {
    @media (min-width: $size) {
      .carousel-item-container {
        width: 100%;
        padding: $padding-y $padding-x;
      }
    }
  }

  .carousel-item-wrapper {
    width: 100%;

    .carousel-item-container {
      align-self: stretch;
      margin-right: 23px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      background: var(--color-neutral-100);
      border: 2px solid var(--color-neutral-300);
      border-radius: 20px;

      .top-section {
        .badge-container {
          margin-bottom: 20px;
        }

        .carousel-icon {
          margin-bottom: 20px;

          img {
            border-radius: 50%;
            width: 54px;
            height: 54px;

            @media (min-width: $md) {
              width: 86px;
              height: 86px;
            }
          }

          .img-placeholder {
            border-radius: 50%;
            width: 54px;
            height: 54px;

            @media (min-width: $md) {
              width: 86px;
              height: 86px;
            }
          }
        }

        .item-eyebrow {
          display: flex;
          align-items: center;
          @include cox-text-eyebrow1;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-eyebrow1;
          }
          margin-bottom: 12px;

          .eyebrow-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 999px;
          }
        }

        // header for the item
        .header {
          @include cox-text-title1-bold;
          margin-bottom: 4px;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-title1-bold;
          }
          word-break: break-word;
        }

        // additional note in the item
        .additional-note {
          @include cox-text-paragraph2-regular;
          color: var(--color-neutral-500) !important;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph2-regular;
            color: var(--color-neutral-500) !important;
          }
        }

        // heading description
        .heading-description {
          @include cox-text-paragraph2-regular;
          color: var(--color-neutral-400) !important;
          margin-top: 24px;
          margin-bottom: 12px;
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            @include cox-text-paragraph2-regular;
            color: var(--color-neutral-400) !important;
          }
        }

        // bullet list
        .bullet-list {
          margin: 20px 0;

          .bullet-list-item {
            display: flex;
            margin-bottom: 12px;

            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              margin: 0;
            }
          }

          .bullet-icon {
            width: 16px;
            height: 20px;
            margin-right: 8px;
            padding-top: 4px;
          }

          .bullet-text {
            @include cox-text-paragraph3-medium;
            color: var(--color-neutral-500) !important;
            white-space: pre-wrap;
            word-break: break-word;
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p {
              @include cox-text-paragraph3-medium;
              color: var(--color-neutral-500) !important;
              white-space: pre-wrap;
              word-break: break-word;
            }
          }
        }

        .accordion-container .accordion-item {
          .accordion-header {
            .accordion-title-icon {
              object-fit: fill;
            }
            .accordion-chevron {
              background-repeat: no-repeat;
            }
          }
          .accordion-body {
            .bullet-point-text {
              b {
                font-weight: 700;
              }
            }
          }
        }
      }

      .bottom-section {
        margin-top: 24px;

        @media (min-width: $lg) {
          margin-top: 32px;
        }

        .psu-carousel-button-section {
          display: flex;
          flex-direction: column;
          gap: 20px;
          margin-top: 24px;

          @media (min-width: $lg) {
            margin-top: 32px;
          }

          .button-base {
            width: 100%;
          }
        }
      }
    }
  }
}
