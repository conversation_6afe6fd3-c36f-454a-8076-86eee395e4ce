import { useEffect, useState } from 'react'

import { useWindowSize } from '@cox/core-ui8/dist/useWindowSize'
import axios from 'axios'

import { LayoutType, ReactVideoProps } from './types'
import { YOUTUBE_ICON } from '../../assets/images'
import { getSpacing } from '../../utils/spacing-util'
import { getVideoThumbnail, playIcon } from '../../utils/video-util'
import ReactImage from '../ReactImage'
import IsInEditorText from '../shared/IsInEditorText'
import ReactImageOverlay from '../shared/ReactImageOverlay'
import './ReactVideo.scss'

const ReactVideo = (props: ReactVideoProps) => {
  const {
    isInEditor = false,
    id,
    layout,
    accessibilityLabel = '',
    youtubeHeight = '315',
    youtubeWidth = '560',
    youtubeVideoId,
    youtubeAspectRatio = '0.5625',
    autoplay = false,
    videoIcon = YOUTUBE_ICON,
    videoAltTxt = '',
    youtubeLoop = false,
    youtubePlaysInline = false,
    youtubeRel = false,
    left = false,
    right = false,
    top = false,
    bottom = false
  } = props
  const [autoPlay, setAutoPlay] = useState(autoplay)
  const [mute, setMute] = useState(autoplay) //if autoplay is true, set mute also true.
  const windowSize = useWindowSize()
  //for autoplay from the begnning, mute has to be set as 1
  //but if the user interacts then, mute shall be set to 0 back
  const YT_SRC = `https://www.youtube.com/embed/${youtubeVideoId}?autoplay=${autoPlay ? 1 : 0}&mute=${mute ? 1 : 0}&rel=${
    youtubeRel ? 1 : 0
  }&playsinline=${youtubePlaysInline ? 1 : 0}&loop=${youtubeLoop ? 1 : 0}${youtubeLoop ? `&playlist=${youtubeVideoId}` : ''}`

  let youtubeAspectRatioPercentage
  if (layout === LayoutType.RESPONSIVE && autoPlay) {
    const percentage = (parseFloat(youtubeAspectRatio) * 100).toFixed(2)
    youtubeAspectRatioPercentage = `${percentage}%`
  }

  const handleAutoPlay = () => {
    setAutoPlay(true)
    setMute(true)
  }

  /* As not all YouTube videos have maxres versions of the thumbnail by default, 
  this is to get the highest resolution thumbnail as possible.
  */
  const [thumbnailFilename, setThumbnailFilename] = useState('mqdefault.jpg')
  const getThumbnailFilename = () => {
    axios
      .get(getVideoThumbnail(youtubeVideoId, 'maxresdefault.jpg'), {
        validateStatus: (status) => status >= 200
      })
      .then((response) => {
        if (response.status == 200) {
          setThumbnailFilename('maxresdefault.jpg')
        } else {
          axios
            .get(getVideoThumbnail(youtubeVideoId, 'hqdefault.jpg'), {
              validateStatus: (status) => status >= 200
            })
            .then((response) => {
              if (response.status == 200) {
                setThumbnailFilename('hqdefault.jpg')
              } else {
                axios
                  .get(getVideoThumbnail(youtubeVideoId, 'sddefault.jpg'), {
                    validateStatus: (status) => status >= 200
                  })
                  .then((response) => {
                    if (response.status == 200) {
                      setThumbnailFilename('sddefault.jpg')
                    } else {
                      setThumbnailFilename('mqdefault.jpg')
                    }
                  })
              }
            })
        }
      })
  }

  useEffect(() => {
    getThumbnailFilename()
  }, [])

  return (
    <>
      {IsInEditorText(isInEditor, 'React Video')}
      <div
        id={id}
        className={`react-video-container ${getSpacing(top, right, bottom, left)}`}
        onClick={() => {
          if (!autoPlay) handleAutoPlay()
        }}
      >
        {layout === LayoutType.FIXED && (
          <div className='yt-fixed-video' style={{ display: 'flex' }}>
            {!autoPlay && (
              <a href={'#'} role={'button'} onClick={(e) => e.preventDefault()}>
                <ReactImageOverlay
                  overlay={playIcon(windowSize.width, windowSize.height, videoIcon)}
                  onClick={handleAutoPlay}
                  alt={accessibilityLabel}
                  ariaLabel={videoAltTxt}
                >
                  <ReactImage
                    src={getVideoThumbnail(youtubeVideoId, thumbnailFilename)}
                    alt={videoAltTxt}
                    imageHeight={Number(youtubeHeight)}
                    imageWidth={Number(youtubeWidth)}
                    ariaLabel={accessibilityLabel}
                  />
                </ReactImageOverlay>
              </a>
            )}
            {autoPlay && (
              <iframe
                width={youtubeWidth}
                height={youtubeHeight}
                src={YT_SRC}
                aria-label={accessibilityLabel}
                allow='autoplay'
                title={id}
              />
            )}
          </div>
        )}
        {layout === LayoutType.RESPONSIVE && (
          <div className='yt-responsive-video' style={{ position: 'relative', paddingBottom: youtubeAspectRatioPercentage }}>
            {!autoPlay && (
              <a href={'#'} role={'button'} onClick={(e) => e.preventDefault()}>
                <ReactImageOverlay
                  overlay={playIcon(windowSize.width, windowSize.height, videoIcon)}
                  onClick={handleAutoPlay}
                  alt={accessibilityLabel}
                  ariaLabel={videoAltTxt}
                >
                  <ReactImage
                    src={getVideoThumbnail(youtubeVideoId, thumbnailFilename)}
                    alt={videoAltTxt}
                    imageHeight={100}
                    imageWidth={100}
                    imageUnit='%'
                    ariaLabel={accessibilityLabel}
                  />
                </ReactImageOverlay>
              </a>
            )}
            {autoPlay && (
              <iframe
                style={{ position: 'absolute', top: '0', left: '0' }}
                width='100%'
                height='100%'
                src={YT_SRC}
                allowFullScreen={true}
                allow='autoplay'
                title={id}
                aria-label={accessibilityLabel}
              />
            )}
          </div>
        )}
      </div>
    </>
  )
}
export default ReactVideo
