export type ReactVideoProps = {
  id?: string
  layout: LayoutType
  accessibilityLabel?: string
  youtubeHeight?: string
  youtubeWidth?: string
  youtubeVideoId: string
  isInEditor?: boolean
  autoplay?: boolean
  youtubeAspectRatio?: string
  videoIcon?: string
  videoAltTxt?: string
  youtubeLoop?: boolean
  youtubePlaysInline?: boolean
  youtubeRel?: boolean
  top?: boolean
  right?: boolean
  bottom?: boolean
  left?: boolean
}

export enum LayoutType {
  FIXED = 'fixed',
  RESPONSIVE = 'responsive'
}
