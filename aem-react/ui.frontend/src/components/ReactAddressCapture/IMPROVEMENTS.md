# ReactAddressCapture Improvements

## Overview

This document outlines the improvements made to the ReactAddressCapture component to address Google Maps loading issues, browser autocomplete conflicts, and unnecessary API costs.

## Issues Addressed

### 1. Native Browser Autocomplete Conflicts
**Problem**: Browser autocomplete dropdowns were overlapping with Google Maps suggestions, causing confusion and wasted API calls.

**Solution**: 
- Added browser autocomplete detection
- Conditionally disable Google Maps when browser autocomplete is active
- Added proper `autocomplete` attributes to input fields

### 2. Immediate Google Maps Loading
**Problem**: Google Maps scripts were loading immediately on component mount, regardless of user interaction.

**Solution**:
- Implemented lazy loading that only loads Google Maps when user interacts with the input
- Added loading state management and visual indicators

### 3. Missing Autocomplete Attributes
**Problem**: Input fields lacked proper `autocomplete` attributes, preventing browser native autocomplete.

**Solution**:
- Added `autocomplete="street-address"` to main address input
- Added `autocomplete="address-line2"` to unit input
- Added `autocomplete="postal-code"` to zip code input

### 4. Unnecessary API Calls
**Problem**: Google Maps was loading even when address was already captured and stored.

**Solution**:
- Added address state persistence checking
- Skip Google Maps loading when address is already available

## New Configuration Options

The component now supports the following configuration props:

```typescript
type ReactAddressCaptureProps = {
  // ... existing props
  enableLazyLoading?: boolean                    // Default: true
  enableBrowserAutocompleteDetection?: boolean  // Default: true
  disableGoogleMapsWhenAddressCaptured?: boolean // Default: true
}
```

## Usage Examples

### Basic Usage (with all improvements enabled)
```jsx
<ReactAddressCapture
  id="address-capture"
  theme="white"
  // All improvements are enabled by default
/>
```

### Disable Lazy Loading (load Google Maps immediately)
```jsx
<ReactAddressCapture
  id="address-capture"
  theme="white"
  enableLazyLoading={false}
/>
```

### Disable Browser Autocomplete Detection
```jsx
<ReactAddressCapture
  id="address-capture"
  theme="white"
  enableBrowserAutocompleteDetection={false}
/>
```

### Legacy Behavior (disable all improvements)
```jsx
<ReactAddressCapture
  id="address-capture"
  theme="white"
  enableLazyLoading={false}
  enableBrowserAutocompleteDetection={false}
  disableGoogleMapsWhenAddressCaptured={false}
/>
```

## Technical Implementation

### Lazy Loading
- Google Maps script is only loaded when user focuses or interacts with the address input
- Uses existing `loadScript` utility from `utag-util.ts`
- Includes proper error handling and loading states

### Browser Autocomplete Detection
- Detects when browser autocomplete is active using CSS pseudo-selectors
- Monitors input events for auto-filled values
- Automatically disables Google Maps suggestions when browser autocomplete is detected

### Address State Management
- Checks UDO context for existing address data
- Monitors local storage for captured address state
- Prevents unnecessary Google Maps loading when address is already available

## Benefits

1. **Reduced API Costs**: Google Maps only loads when actually needed
2. **Better User Experience**: No conflicts between browser and Google autocomplete
3. **Improved Performance**: Faster initial page load without immediate script loading
4. **Accessibility**: Proper autocomplete attributes improve form accessibility
5. **Flexibility**: Configuration options allow customization for different use cases

## Browser Support

The improvements maintain compatibility with all browsers supported by the original component:
- Chrome/Edge: Full support including `-webkit-autofill` detection
- Firefox: Full support including `:autofill` detection
- Safari: Full support with webkit-specific features
- IE11: Graceful degradation (autocomplete detection may be limited)

## Testing Recommendations

1. Test with browser autocomplete enabled/disabled
2. Verify Google Maps loads only on user interaction
3. Test with existing address data in UDO/localStorage
4. Verify proper form submission with autocomplete values
5. Test configuration options work as expected
