import './ReactAddressCapture.scss'

import { ChangeEvent, KeyboardEvent, MouseEvent, TouchEvent, useCallback, useEffect, useRef, useState } from 'react'

import { useAddressContext } from '@cox/core-ui8/dist/AddressContext'
import Banner, { BannerType, BannerVariaion } from '@cox/core-ui8/dist/Banner'
import { ButtonStates, ButtonTypes, LinkTypes, SimplifiedIcons } from '@cox/core-ui8/dist/Button'
import { Modal, ModalTypes } from '@cox/core-ui8/dist/Modal'
import { useUdoContext } from '@cox/core-ui8/dist/UdoContext'
import useLobExperience from '@cox/core-ui8/dist/useLobExperience'
import axios from 'axios'
import debounce from 'lodash/debounce'
import ReactDOM from 'react-dom'
import { renderToString } from 'react-dom/server'

import { addressFormat, ReactAddressCaptureProps } from './types'
import { ADDRESS_BANNER_WARNING, CHEVRON_RIGHT_WHITE, LOCATION_PIN } from '../../assets/images'
import {
  GOOGLE_MAP_KEY,
  MCP_ADDRESS_CAPTURE_EXACTMATCH,
  MCP_ADDRESS_CAPTURE_MISMATCH,
  SMART_MOVE_SEARCH_API,
  WEB_APIS
} from '../../constants'
import { sortArrayByKey } from '../../utils/helper-util'
import { clearLocalStorage, getLocalStorage, setLocalStorage } from '../../utils/local-storage-util'
import { triggerMCPEvent } from '../../utils/mcp-util'
import { getTokenValues } from '../../utils/rule-builder-util'
import { getSpacing } from '../../utils/spacing-util'
import { AddressServiceabilityStatus, ReactComponents, ScreenSizes } from '../../utils/types'
import { updateTealiumStatus } from '../../utils/utag-util'
import ReactButton, { ReactButtonProps } from '../ReactButton'
import ReactText from '../ReactText'
import IsInEditorText from '../shared/IsInEditorText'
import { Dropdown } from '../shared/ReactBootstrap'

const ReactAddressCapture = (props: ReactAddressCaptureProps): JSX.Element => {
  const {
    id = '',
    theme = 'white',
    response = AddressServiceabilityStatus.default,
    messages = '',
    configuredFor = 'resi-prospect',
    isInEditor = false,
    tokenProperties,
    locationPin = LOCATION_PIN,
    simplifiedIcon = CHEVRON_RIGHT_WHITE,
    top = false,
    right = false,
    bottom = false,
    left = false,
    multipleAddressChevron,
    multipleAddressChevronAlt,
    inLayoutContainer = false,
    lob = '',
    hideMCP = false
  } = props

  type defaultSuggestion = {
    id: ''
    label: ''
    value: ''
    normalPart: ''
    boldPart: ''
  }

  const { udo } = useUdoContext()
  const { setFullAddress } = useAddressContext()
  const { isBusiness } = useLobExperience()

  const [responseKey, setResponseKey] = useState(response)
  const [suggestions, setSuggestions] = useState(Array<defaultSuggestion>())
  const [suggestionIndex, setSuggestionIndex] = useState(-1)
  const [suggestionsActive, setSuggestionsActive] = useState(false)
  const [displayInputs, setDisplayInputs] = useState(false)
  const [isMultipleAddress, setIsMultipleAddress] = useState(false)
  const [isExactMatchCBAddress, setIsExactMatchCBAddress] = useState(false)

  const [value, setValue] = useState('')
  const [zipcode, setZipcode] = useState('')
  const [unit, setUnit] = useState('')
  const [city, setCity] = useState('')
  const [state, setState] = useState('')
  const [houseNumber, setHouseNumber] = useState('')
  const [unitNumber, setUnitNumber] = useState('')
  const [unitType, setUnitType] = useState('')
  const [addressSearchValue, setAddressSearchValue] = useState('')
  const [selectedAddress, setSelectedAddress] = useState<any | null>(null)
  const [addressList, setAddressList] = useState<any | null>(null)
  const [streetInputPlaceholder, setStreetInputPlaceholder] = useState('')

  const [searchProgress, setSearchProgress] = useState(false)
  const [searchCompleted, setSearchCompleted] = useState(false)

  const [heading, setHeading] = useState('')
  const [description, setDescription] = useState('')
  const [disclaimer, setDisclaimer] = useState('')
  const [buttonOneLabel, setButtonOneLabel] = useState('')
  const [buttonOneLink, setButtonOneLink] = useState('')
  const [buttonTwoLabel, setButtonTwoLabel] = useState('')
  const [buttonTwoLink, setButtonTwoLink] = useState('')
  const [displayButtons, setDisplayButtons] = useState(false)

  const [showModal, setShowModal] = useState(false)
  const [suggestionPosition, setSuggestionPosition] = useState({ offsetLeft: 0, offsetTop: 0 })

  const descriptionRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const unitRef = useRef<HTMLInputElement>(null)
  const zipRef = useRef<HTMLInputElement>(null)

  const handleResize = () => {
    const { current } = inputRef
    setTimeout(() => {
      if (current) {
        setSuggestionPosition({
          offsetLeft: current?.offsetLeft ?? 0,
          offsetTop: current?.offsetTop ?? 0
        })
      }
      setStreetInputPlaceholder(window.innerWidth < ScreenSizes.MD ? 'Street Address' : 'Street Address, Apt/Unit, Zip Code')
    }, 350)
  }

  useEffect(() => {
    if (isBusiness && isExactMatchCBAddress) {
      // TO DO: remove the console logs after QA testing
      console.info('Before GFN trigger')
      window?.refireGFN?.()
      console.info('After GFN trigger')
    }
  }, [isExactMatchCBAddress])

  useEffect(() => {
    loadGMScript(GOOGLE_MAP_KEY)

    handleResize()

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)

      debouncedChangeHandler.cancel()
    }
  }, [])

  useEffect(() => {
    if (udo) {
      const isError = udo?.error
      const isAddressCapture = !isBusiness && (udo?.pAddressCapture || udo?.moveAddressCapture)
      const isCBAddressCapture = isBusiness && udo?.pCBAddressCapture
      if (!isError && (isAddressCapture || isCBAddressCapture)) {
        onInitialLoad()
      }
      // retain cb only address if local value exist
      let cbOnlyAddressData: any = getLocalStorage('cbOnlyAddressSearch')
      cbOnlyAddressData = cbOnlyAddressData ? JSON.parse(cbOnlyAddressData) : null
      if (cbOnlyAddressData) {
        retainDefaultState(cbOnlyAddressData.address, cbOnlyAddressData.apt, cbOnlyAddressData.zipCode)
        handleVisibleState()
      }
    }
  }, [udo])

  const onInitialLoad = () => {
    const isServiceableActive = udo?.pAddressActive || udo?.moveAddressActive
    setResponseKey(
      isServiceableActive ? AddressServiceabilityStatus.serviceableActive : AddressServiceabilityStatus.serviceable
    )
    onEdit(true)
    handleVisibleState()
  }

  const handleVisibleState = () => {
    const focusInputStatus = getLocalStorage('addressCaptureFocus')
    if (focusInputStatus == 'true') {
      const { current } = descriptionRef
      const addressCaptureContainer = id && document.getElementById(id)
      setTimeout(() => {
        clearLocalStorage('addressCaptureFocus')
        if (current) {
          current?.parentElement?.scrollIntoView({ behavior: 'smooth', block: 'start' })
        } else {
          addressCaptureContainer && addressCaptureContainer?.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      }, 350)
    }
  }

  const loadGMScript = (url: string) => {
    // TODO - Use load script from utag script ts
    if (!document.getElementById('gm-script')) {
      const script = document.createElement('script')
      script.type = 'text/javascript'

      script.src = url
      script.id = 'gm-script'
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
  // TODO - create separate component for suggestions
  const Suggestions = () => {
    return (
      <ul
        className={
          'react-address-suggestion-list ui-menu ui-widget ui-widget-content ui-autocomplete ui-front bindComplete ui-input-street-address-input google-addressList ' +
          (suggestions && suggestions.length > 0 ? '' : 'd-none')
        }
        style={{ top: suggestionPosition.offsetTop + 60 + 'px', left: suggestionPosition.offsetLeft - 50 + 'px' }}
      >
        {suggestions?.map((suggestion, index) => {
          return (
            <li
              className={index === suggestionIndex ? 'ui-menu-item highlight' : 'ui-menu-item'}
              key={index}
              onMouseDown={(event) => suggestionClick(event, index)}
              onTouchStart={(event) => suggestionClick(event, index)}
            >
              <div id={'ui-id-' + index} className='ui-menu-item-wrapper google-addressList-item'>
                <span className='google-Marker'></span>
                <span className='google-highlight'>{suggestion.boldPart}</span>
                {suggestion.normalPart}
              </div>
            </li>
          )
        })}
        <li className='google-powered-icon'></li>
      </ul>
    )
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    switch (e.key) {
      case 'Down': // IE/Edge specific value
      case 'ArrowDown':
        if (suggestionIndex - 1 === suggestions.length) {
          return
        }
        setSuggestionIndex(suggestionIndex + 1)
        break
      case 'Up': // IE/Edge specific value
      case 'ArrowUp':
        if (suggestionIndex === 0) {
          return
        }
        setSuggestionIndex(suggestionIndex - 1)
        break
      case 'Enter':
        // eslint-disable-next-line no-case-declarations
        const selectedPlaceId = suggestions[suggestionIndex].id
        setValue(suggestions[suggestionIndex].label)
        setSuggestionIndex(0)
        setSuggestionsActive(false)
        triggerGeocodeCall(selectedPlaceId)
        break
      default:
        return
    }
  }

  const triggerGeocodeCall = (addressPlaceId: string): void => {
    const geocoder = new google.maps.Geocoder()

    geocoder.geocode(
      { placeId: addressPlaceId },
      (placeResult: google.maps.GeocoderResult[] | null, placeServiceStatus: google.maps.GeocoderStatus) => {
        if (placeResult != undefined && placeServiceStatus === google.maps.GeocoderStatus.OK) {
          const components = placeResult && placeResult[0] ? placeResult[0].address_components : []
          let street = '',
            number = '',
            zip = '',
            unit = '',
            state = '',
            city = ''
          components.forEach((component: google.maps.GeocoderAddressComponent) => {
            if (component.types[0] == 'route') {
              street = component['long_name']
            }
            if (component.types[0] == 'street_number') {
              number = component['long_name']
            }
            if (component.types[0] == 'postal_code') {
              zip = component['long_name']
            }
            if (component.types[0] == 'subpremise') {
              unit = component['long_name']
            }
            if (component.types[0] == 'administrative_area_level_1') {
              state = component['short_name']
            }
            if (component.types[0] == 'locality') {
              city = component['long_name']
            }
          })
          if (number && street) {
            setAddressSearchValue(number + ' ' + street)
            setValue(number + ' ' + street)
            setSuggestionsActive(false)
          }
          setZipcode(zip ? zip : '')
          setUnit(unit)
          setCity(city)
          setState(state)
          setHouseNumber('')
          if (isBusiness) {
            setUnitType('undefined')
            const unit_Number = unit === '' ? 'undefined' : unit
            setUnitNumber(unit_Number)
          }
        }
      }
    )
  }
  const suggestionClick = (event: MouseEvent | TouchEvent, index: number) => {
    event.preventDefault()

    setSuggestionIndex(index)
    const selectedPlaceId = suggestions[index].id
    setValue(suggestions[index].label)
    setSuggestionIndex(0)
    setSuggestionsActive(false)
    triggerGeocodeCall(selectedPlaceId)
    setSuggestionsActive(false)
  }
  const updateValue = (event: ChangeEvent<HTMLInputElement>) => {
    handleResize()
    setValue(event.target.value)
    setAddressSearchValue(event.target.value)
    const customValue = {
      target: {
        name: 'searchValue',
        value: event.target.value
      }
    }
    debouncedChangeHandler(customValue) //event
  }

  const debouncedChangeHandler = useCallback(
    debounce((event) => handleChange(event), 650),
    []
  )
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value
    setValue(query)
    processAutoCompleteService(query)
  }
  const processAutoCompleteService = (inputValue: string) => {
    if (inputValue) {
      if (inputValue.length < 1) {
        setUnit('')
        setZipcode('')
        setDisplayInputs(false)
      } else {
        setDisplayInputs(true)
      }
      const service = new google.maps.places.AutocompleteService()
      const config = {
        input: inputValue,
        componentRestrictions: {
          country: 'us'
        }
      } as google.maps.places.AutocompletionRequest
      service.getPlacePredictions(
        config,
        function (
          predictions: google.maps.places.AutocompletePrediction[] | null,
          status: google.maps.places.PlacesServiceStatus
        ) {
          if (status != google.maps.places.PlacesServiceStatus.OK) {
            return
          }
          const predictionList = []
          if (predictions) {
            for (const element of predictions) {
              predictionList.push({
                label: element.description,
                value: element.description,
                boldPart: element.description.substring(0, element.description.indexOf(',')),
                normalPart: element.description.substring(element.description.indexOf(',') + 1),
                id: element.place_id
              } as defaultSuggestion)
            }
          }
          setSuggestions(predictionList)
          setSuggestionsActive(true)
        }
      )
    } else {
      setDisplayInputs(false)
      setSuggestions([])
      setSuggestionsActive(false)
    }
  }

  // Map addressMatch(CB) values to AddressServiceabilityStatus values
  const mapAddressMatchToStatus = (addressMatch: string): AddressServiceabilityStatus => {
    const mapping: { [key: string]: AddressServiceabilityStatus } = {
      MultipleMatch: AddressServiceabilityStatus.multiple,
      NoMatch: AddressServiceabilityStatus.nonServiceable,
      ExactMatch: AddressServiceabilityStatus.serviceable
    }

    if (addressMatch === '') {
      return AddressServiceabilityStatus.nonServiceable
    }

    return mapping[addressMatch] || ''
  }

  const onSubmit = () => {
    // TODO - Use callback hook for submit
    let apiRequest = {}
    let inputElement: Element | null = null
    if (selectedAddress) {
      apiRequest = {
        address: selectedAddress.address,
        apt: selectedAddress.apt,
        zipCode: selectedAddress.zipCode
      }
      if (isBusiness) {
        apiRequest = {
          ...apiRequest,
          city: selectedAddress.city,
          state: selectedAddress.state,
          houseNumber: selectedAddress.houseNumber,
          unitType: selectedAddress.unitType,
          unitNumber: selectedAddress.apt
        }
      } else {
        apiRequest = {
          ...apiRequest,
          serviceabilityType: selectedAddress.serviceabilityType
        }
      }
    } else {
      setSuggestionsActive(false)
      if (addressSearchValue.trim() === '' && zipcode.trim() === '') {
        return false
      }

      inputElement = ReactDOM.findDOMNode(inputRef.current) as Element
      setSearchProgress(true)
      apiRequest = {
        address: addressSearchValue || value,
        apt: unit,
        zipCode: zipcode
      }
      if (isBusiness) {
        apiRequest = {
          ...apiRequest,
          city: city,
          state: state,
          houseNumber: houseNumber,
          unitNumber: unitNumber,
          unitType: unitType
        }
      } else {
        apiRequest = {
          ...apiRequest,
          serviceabilityType: configuredFor
        }
      }
    }

    const apiEndpoint = isBusiness ? WEB_APIS.ADDRESS_SERVICEABILITY_CB_API : WEB_APIS.ADDRESS_SERVICEABILITY_API

    axios.post(apiEndpoint, apiRequest, { withCredentials: true }).then(
      (response) => {
        const addressResponse = response?.data ?? null
        const addressStatus = isBusiness
          ? mapAddressMatchToStatus(addressResponse?.addressMatch)
          : addressResponse?.addressStatus || ''
        const addrList = addressResponse?.addresses?.length > 0 ? addressResponse.addresses : null
        setResponseKey(addressStatus)
        setSearchCompleted(true)
        inputElement?.classList?.add('read-only')
        clearLocalStorage('cbOnlyAddressSearch')
        if (addressStatus == AddressServiceabilityStatus.multiple) {
          updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, AddressServiceabilityStatus.multiple)
          setIsMultipleAddress(true)
          const allAddrsHaveApt = addrList?.every((addr: any) => 'apt' in addr)
          let formattedList = addrList
          if (!isBusiness || (isBusiness && allAddrsHaveApt)) {
            formattedList = sortArrayByKey(addrList, 'apt')
          }
          setAddressList(formattedList)
          preSelectDropdown(formattedList)
          bindEditAddressEvent(addressStatus, formattedList)
          setLocalStorage('addressCaptureFocus', 'true')
        } else if (
          addressStatus == AddressServiceabilityStatus.nonServiceable ||
          (isBusiness && !addressResponse?.success)
        ) {
          handleNonServiceable()
        } else if (
          addressStatus == AddressServiceabilityStatus.serviceableActive ||
          addressStatus == AddressServiceabilityStatus.serviceable
        ) {
          updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, AddressServiceabilityStatus.serviceable)
          setLocalStorage('addressCaptureFocus', 'true')
          const streetAddress = selectedAddress
            ? `${selectedAddress.address}, ${selectedAddress.apt}`
            : `${value}${unit ? `, ${unit}` : ''}`

          const addressZip = selectedAddress ? selectedAddress.zipCode : zipcode
          setFullAddress(`${streetAddress}, ${addressZip}`)
          setIsExactMatchCBAddress(addressResponse?.addressMatch === 'ExactMatch')
          setIsMultipleAddress(false)
          setAddressList(null)
          if (isBusiness) {
            setLocalStorage('initSF', 'true')
          }
        } else if (addressStatus == AddressServiceabilityStatus.cbonlyaddress) {
          updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, addressStatus)
          setLocalStorage('cbOnlyAddressSearch', JSON.stringify(apiRequest))
          setLocalStorage('addressCaptureFocus', 'true')
          setIsMultipleAddress(false)
          setAddressList(null)
          displayCbOnlyAddressModal()
        } else {
          updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, addressStatus)
          setIsMultipleAddress(false)
          setAddressList(null)
        }
      },
      (error: Error) => {
        console.error('Address Capture Serviceability error: ', error)
        setSearchProgress(false)
        setSearchCompleted(false)
        if (isBusiness) {
          handleNonServiceable()
          updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, AddressServiceabilityStatus.nonServiceable)
        }
      }
    )
  }
  const handleNonServiceable = () => {
    updateTealiumStatus(ReactComponents.REACT_ADDRESSCAPTURE, AddressServiceabilityStatus.nonServiceable)
    setIsMultipleAddress(false)
    setAddressList(null)
    if (!isBusiness) {
      displayCtamModal()
    } else {
      triggerMCPEvent(MCP_ADDRESS_CAPTURE_MISMATCH)
    }
  }
  const displayCbOnlyAddressModal = () => {
    setShowModal(true)
  }
  const displayCtamModal = () => {
    axios.get(WEB_APIS.CTAM_API, { withCredentials: true }).then(
      (result) => {
        const { data } = result
        const tokens = data && data.tokens ? data.tokens : []
        if (tokens.length > 0 && tokens[0]) {
          setShowModal(true)
          ctamScript(SMART_MOVE_SEARCH_API, tokens[0])
        }
      },
      (error: Error) => {
        console.error('displayCtamModal ', error)
      }
    )
  }
  const ctamScript = (url: string, tokenValue: string) => {
    if (!document.getElementById('ctam-script')) {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.id = 'ctam-script'

      script.onload = () => {
        setTimeout(() => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          ctam_widget_initialization(tokenValue, '#smartmove_container', 1010, addressSearchValue, unit, zipcode)
        }, 650)
      }

      document.head.appendChild(script)
    } else {
      setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        ctam_widget_initialization(tokenValue, '#smartmove_container', 1010, addressSearchValue, unit, zipcode)
      }, 650)
    }
  }

  const onEdit = (isPersist?: boolean) => {
    setDisplayInputs(true)
    if (isPersist) {
      const {
        pAddressCapture = false,
        pAddressStreet = '',
        pAddressZip = '',
        pAddressApartment = '',
        moveAddressCapture = false,
        moveToStreet = '',
        moveToApt = '',
        moveToZip = '',
        pCBAddressCapture = false,
        pCBAddressStreet = '',
        pCBAddressApartment = '',
        pCBAddressCity = '',
        pCBAddressState = '',
        pCBAddressZip = '',
        pCBAddressHouseNumber = '',
        pCBAddressUnitType = '',
        hfcServiceabilityColorRating = '',
        fiberServiceabilityColorRating = ''
      } = udo

      setSearchProgress(true)
      setSearchCompleted(true)

      if (pAddressCapture) {
        setValue(pAddressStreet)
        setUnit(pAddressApartment?.trim())
        setZipcode(pAddressZip?.trim())
      } else if (moveAddressCapture) {
        setValue(moveToStreet)
        setUnit(moveToApt?.trim())
        setZipcode(moveToZip?.trim())
      } else if (pCBAddressCapture) {
        setValue(pCBAddressStreet)
        setUnit(pCBAddressApartment)
        setCity(pCBAddressCity)
        setState(pCBAddressState)
        setZipcode(pCBAddressZip)
        setHouseNumber(pCBAddressHouseNumber)
        setUnitType(pCBAddressUnitType)
        if (hfcServiceabilityColorRating === 'RED' && fiberServiceabilityColorRating === 'RED') {
          setResponseKey(AddressServiceabilityStatus.nonServiceable)
          handleNonServiceable()
        }
        // To Do - Testing in progress by MCP team
        const initSF = getLocalStorage('initSF')
        if (initSF === 'true') {
          clearLocalStorage('initSF')
          console.info('AEM: Before Salesforce Interactions reinit')
          window?.SalesforceInteractions?.reinit()
          console.info('AEM: After Salesforce Interactions reinit')
        }
        triggerMCPEvent(MCP_ADDRESS_CAPTURE_EXACTMATCH)
      } else {
        // replace add, unit, zip from local str
      }
    } else {
      setSearchProgress(true)
      setSearchCompleted(false)
      setSearchProgress(false)
      setResponseKey(AddressServiceabilityStatus.default)
    }
    handleResize()
  }
  const checkAvilabilitynProps: ReactButtonProps = {
    buttonTypes: ButtonTypes.PRIMARY,
    id: 'test-primary-button',
    isInEditor: false,
    linkType: LinkTypes.PAGE,
    linkUrl: '',
    text: 'Check availability',
    simplified: SimplifiedIcons.CHEVRON
  }
  /* Initialize autocomplete - end */

  const btnOneProps: ReactButtonProps = {
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.PRIMARY,
    id: 'test-primary-button-one',
    linkType: LinkTypes.PAGE
  }
  const btnTwoProps: ReactButtonProps = {
    buttonStates: ButtonStates.ACTIVE,
    buttonTypes: ButtonTypes.SECONDARY,
    id: 'test-primary-button-two',
    linkType: LinkTypes.PAGE
  }

  const preSelectDropdown = (addressList: any) => {
    //pre-select first item in multiple address dropdown
    if (addressList && addressList.length > 0) {
      const {
        address = '',
        apt = '',
        zipCode = '',
        serviceabilityType = '',
        city = '',
        state = '',
        houseNumber = '',
        unitType = ''
      }: addressFormat = addressList[0]
      handleSelect(address, apt, zipCode, serviceabilityType, city, state, houseNumber, unitType)
    }
  }

  const bindEditAddressEvent = (response: string, multipleAddressList: any | null) => {
    if (response == AddressServiceabilityStatus.multiple) {
      const { address: editAddressText, zipCode: editZipcode } =
        multipleAddressList && multipleAddressList.length > 0 ? multipleAddressList[0] : { address: '', zipCode: '' }
      setTimeout(() => {
        const descriptionSection = ReactDOM.findDOMNode(descriptionRef.current) as HTMLDivElement
        if (descriptionSection) {
          const childNode = id && document.querySelector('a[title=' + id + ']')
          if (childNode) {
            childNode.addEventListener('click', (event) => {
              event.preventDefault()
              retainDefaultState(editAddressText, '', editZipcode)
            })
          }
        }
      }, 350)
    }
  }

  const retainDefaultState = (address: string, apt: string, zipcode: string) => {
    setResponseKey(AddressServiceabilityStatus.default)
    setIsMultipleAddress(false)

    setSearchProgress(false)
    setSearchCompleted(false)
    setDisplayInputs(true)

    setValue(address)
    setUnit(apt)
    setZipcode(zipcode)
    setSelectedAddress(null)
  }

  useEffect(() => {
    getProcessedResponse(responseKey)
  }, [responseKey])

  const getProcessedResponse = (key: string): void => {
    if (messages) {
      try {
        const filterJson = JSON.parse(messages)
        if (filterJson[key]) {
          setHeading(
            filterJson[key].heading
              ? (getTokenValues(filterJson[key].heading, tokenProperties, udo?.localeName) as string)
              : ''
          )
          setDescription(
            filterJson[key].description
              ? (getTokenValues(filterJson[key].description, tokenProperties, udo?.localeName) as string)
              : ''
          )
          setButtonOneLabel(filterJson[key].btn1lbl ? filterJson[key].btn1lbl : '')
          setButtonOneLink(filterJson[key].btn1link ? filterJson[key].btn1link : '')
          setButtonTwoLabel(filterJson[key].btn2lbl ? filterJson[key].btn2lbl : '')
          setButtonTwoLink(filterJson[key].btn2link ? filterJson[key].btn2link : '')
          setDisplayButtons(filterJson[key].btn1lbl || filterJson[key].btn2lbl)
          setDisclaimer(
            filterJson[key].disclaimer
              ? (getTokenValues(filterJson[key].disclaimer, tokenProperties, udo?.localeName) as string)
              : ''
          )
          return filterJson[key]
        } else {
          fallbackResponse()
        }
      } catch (error) {
        fallbackResponse()
        throw Error('Issue in custom message parsing')
      }
    } else {
      fallbackResponse()
    }
  }
  const fallbackResponse = () => {
    setHeading('')
    setDescription('')
    setButtonOneLabel('')
    setButtonOneLink('')
    setButtonTwoLabel('')
    setButtonTwoLink('')
    setDisclaimer('')
  }
  const handleClose = () => {
    setResponseKey(AddressServiceabilityStatus.default)
    setSearchProgress(false)
    setSearchCompleted(false)
    setDisplayInputs(true)
    setShowModal(false)
  }

  const handleSelect = (
    address: string,
    apartment: string,
    zipCode: string,
    serviceabilityType: string,
    city: string,
    state: string,
    houseNumber: string,
    unitType: string
  ) => {
    let addressSelected: any = {
      address: address,
      apt: apartment,
      zipCode: zipCode,
      serviceabilityType: serviceabilityType
    }
    if (isBusiness) {
      addressSelected = {
        ...addressSelected,
        city: city || '',
        state: state || '',
        houseNumber: houseNumber || '',
        unitType: unitType
      }
    }
    setSelectedAddress(addressSelected)
  }

  // Checks if hideMCP flag is true and that display is in dispatcher, not in author:
  const mcpClass = hideMCP && isInEditor == false ? 'd-none' : ''

  return (
    <div
      data-testid={`react-address-capture`}
      className={`react-address-capture ${getSpacing(top, right, bottom, left)} ${lob} ${mcpClass}`}
      id={id}
    >
      {IsInEditorText(isInEditor, 'React Address Capture')}
      <div
        className={`react-ac-container ${inLayoutContainer ? '' : 'default-container-spacing'} ${theme}`}
        data-testid={`react-ac-container`}
      >
        <div className='react-ac-search-header'>
          <ReactText text={heading} isParsed={true} className='react-ac-heading' id={'react-ac-heading'} />
          {description && (
            <div ref={descriptionRef}>
              <ReactText text={description} isParsed={true} className='react-ac-description' id={'react-ac-description'} />
            </div>
          )}
          {displayButtons && (
            <div className={'react-ac-buttons'} data-testid={`react-ac-buttons`}>
              <ReactButton {...btnOneProps} text={buttonOneLabel} linkUrl={buttonOneLink}></ReactButton>
              <ReactButton {...btnTwoProps} text={buttonTwoLabel} linkUrl={buttonTwoLink}></ReactButton>
            </div>
          )}
        </div>
        <div className='react-ac-search-block'>
          {!isMultipleAddress && (
            <div className={'react-ac-search-container ' + theme}>
              <div className='react-address-search-input'>
                <div className='search-icon'>
                  <img src={locationPin} className='cox-icon' alt={'location icon'} />
                </div>
                {!searchProgress && (
                  <input
                    type='text'
                    placeholder={streetInputPlaceholder}
                    className={!displayInputs ? 'full-width' : ''}
                    ref={inputRef}
                    value={value}
                    onChange={(event) => updateValue(event)}
                    onKeyDown={handleKeyDown}
                    onBlur={() => setSuggestionsActive(false)}
                    onMouseUp={() => setSuggestionsActive(true)}
                    onTouchEnd={() => setSuggestionsActive(true)}
                  />
                )}
                {searchProgress && (
                  <div className='react-address-single-field px-2'>{value + (unit && ', ' + unit) + ', ' + zipcode}</div>
                )}
              </div>
              <div className='react-search-input-button'>
                {!searchProgress && !searchCompleted && displayInputs && (
                  <>
                    <input
                      className='additional-input'
                      type='text'
                      ref={unitRef}
                      value={unit}
                      placeholder='Unit #'
                      onChange={(e) => {
                        setUnit(e.target.value.trim())
                        if (isBusiness) {
                          setUnitNumber(e.target.value.trim())
                          setHouseNumber('')
                        }
                      }}
                    />
                    <input
                      className='additional-input'
                      type='text'
                      ref={zipRef}
                      value={zipcode}
                      placeholder='Zip Code'
                      onChange={(e) => setZipcode(e.target.value.trim())}
                    />
                  </>
                )}
                {!searchProgress && !searchCompleted && (
                  <ReactButton
                    {...checkAvilabilitynProps}
                    activeIcon={simplifiedIcon}
                    customClickEvent={onSubmit}
                  ></ReactButton>
                )}
                {searchProgress && !searchCompleted && (
                  <div className='search-spinner-container'>
                    <div className='search-spinner-loader'></div>
                  </div>
                )}
                {searchProgress && searchCompleted && (
                  <div className='react-ac-edit-address' onClick={() => onEdit()}>
                    Edit address
                  </div>
                )}
              </div>
            </div>
          )}
          {isMultipleAddress && (
            <div className='react-ac-multiple-container form-basic-dropdown'>
              <div className='react-ac-multiple-dropdown'>
                <Dropdown>
                  <Dropdown.Toggle variant='ghost' id='dropdown-basic' className={`input responsive-size`}>
                    <div className='react-ac-multiple-selected'>
                      {selectedAddress
                        ? selectedAddress?.address +
                          ', ' +
                          (selectedAddress?.apt ? selectedAddress?.apt + ', ' : '') +
                          selectedAddress?.zipCode
                        : 'Select address'}
                    </div>
                    <div className={'icon'}>
                      <img src={multipleAddressChevron} alt={multipleAddressChevronAlt} />
                    </div>
                  </Dropdown.Toggle>

                  <Dropdown.Menu>
                    {addressList &&
                      addressList?.length > 0 &&
                      addressList?.map(
                        ({
                          address = '',
                          apt = '',
                          zipCode = '',
                          houseNumber = '',
                          serviceabilityType = '',
                          city = '',
                          state = '',
                          unitType = ''
                        }: addressFormat) => {
                          return (
                            <Dropdown.Item
                              key={houseNumber}
                              eventKey={houseNumber}
                              onClick={() =>
                                handleSelect(address, apt, zipCode, serviceabilityType, city, state, houseNumber, unitType)
                              }
                            >
                              {address + ', ' + (apt ? apt + ', ' : '') + zipCode}
                            </Dropdown.Item>
                          )
                        }
                      )}
                  </Dropdown.Menu>
                </Dropdown>
              </div>
              <div className='react-ac-multiple-button'>
                <ReactButton {...checkAvilabilitynProps} simplified={undefined} customClickEvent={onSubmit}></ReactButton>
              </div>
            </div>
          )}

          <ReactText text={disclaimer} isParsed={true} className='react-ac-disclaimer' id={'react-ac-disclaimer'} />
        </div>
        {suggestionsActive && suggestions && suggestions.length > 0 && <Suggestions />}
        <Modal
          modalId='react-address-capture'
          show={showModal}
          componentName={'address-capture'}
          title={responseKey == AddressServiceabilityStatus.nonServiceable ? 'Out of Market' : heading}
          handleClose={handleClose}
          modalType={ModalTypes.custom}
          primaryBtnText={responseKey == AddressServiceabilityStatus.nonServiceable ? 'Change location' : buttonOneLabel}
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          primaryBtnClick={
            responseKey == AddressServiceabilityStatus.nonServiceable || buttonOneLink == '#' ? handleClose : undefined
          }
          primaryBtnLink={buttonOneLink}
          secondaryBtnText={buttonTwoLabel}
          secondaryBtnLink={buttonTwoLink}
          secondaryBtnClick={buttonTwoLink == '#' ? handleClose : undefined}
        >
          {responseKey == AddressServiceabilityStatus.nonServiceable && (
            <>
              {heading && <div className='react-ac-modal-heading'>{heading}</div>}
              <div className='react-ac-modal-address'>
                {value} {unit} {zipcode}
              </div>
              <div id='smartmove_container'></div>
            </>
          )}
          {responseKey == AddressServiceabilityStatus.cbonlyaddress && (
            <div className='cb-content'>
              <Banner
                bannerType={BannerType.DYNAMIC}
                message={`${disclaimer && renderToString(disclaimer as any)} ${value}, ${unit && unit + ', '}${zipcode}`}
                variation={BannerVariaion.WARNING}
                iconPath={ADDRESS_BANNER_WARNING}
              ></Banner>
              <div className='cb-description'>{description}</div>
            </div>
          )}
        </Modal>
      </div>
    </div>
  )
}
export default ReactAddressCapture
