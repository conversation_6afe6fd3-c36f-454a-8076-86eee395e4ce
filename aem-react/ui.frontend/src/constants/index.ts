export const {
  NODE_ENV = '',
  REACT_APP_AEM_AUTHORIZATION_HEADER = '',
  REACT_APP_ROOT = '',
  REACT_APP_API_HOST
} = process.env

export const COMPONENTS_PATH = 'cox-cms-react/components'

export const AEM_CONSTANTS = {
  TYPE_PROP: ':type',
  ITEMS_PROP: ':items',
  ITEMS_ORDER_PROP: ':itemsOrder',
  COMPONENTS_PATH: 'cox-cms-react/components'
}

export const PATHNAME = window?.location?.pathname
export const HOST = window?.location?.host
export const ORIGIN = window?.location?.origin
export const HREF = window?.location?.href
export const HASH = window?.location?.hash
export const SEARCH = window?.location?.search
export const APP_HOST = (NODE_ENV === 'development' ? 'http://localhost:4502' : ORIGIN) || ''

export const AUTHOR_DOMAINS = {
  DEV: `https://author-p47652-e526536.adobeaemcloud.com`,
  QA1: `https://author-p47652-e412725.adobeaemcloud.com`,
  QA2: `https://author-p47652-e412678.adobeaemcloud.com`,
  QALOAD: `https://author-p47652-e543711.adobeaemcloud.com`,
  STAGE: `https://author-p47652-e412724.adobeaemcloud.com`,
  PROD: `https://author-p47652-e412677.adobeaemcloud.com`
}

export const PUBLISHER_DOMAINS = {
  DEV: `https://publish-p47652-e526536.adobeaemcloud.com`,
  QA1: `https://publish-p47652-e412725.adobeaemcloud.com`,
  QA2: `https://publish-p47652-e412678.adobeaemcloud.com`,
  QALOAD: `https://publish-p47652-e543711.adobeaemcloud.com`,
  STAGE: `https://publish-p47652-e412724.adobeaemcloud.com`,
  PROD: `https://publish-p47652-e412677.adobeaemcloud.com`
}

export const COX_DOMAINS = {
  DEV: 'https://www.int.dev.cox.com',
  QA1: 'https://www.one.qa.cox.com',
  QA2: 'https://www.two.qa.cox.com',
  QALOAD: 'https://www.one.staging.cox.com',
  STAGE: 'https://test.cox.com',
  PROD: 'https://www.cox.com'
}

export const SPANISH_COX_DOMAINS = {
  DEV: 'https://www.int.dev.cox.com',
  QA1: 'https://www.one.qa.cox.com',
  QA2: 'https://www.two.qa.cox.com',
  QALOAD: 'https://www.one.staging.cox.com',
  STAGE: 'https://espanol.test.cox.com',
  PROD: 'https://espanol.cox.com'
}

export const COX_DOMAIN_REGEX = {
  DEV: /.*int\.dev\.cox\.com/,
  QA1: /.*one\.qa\.cox\.com/,
  QA2: /.*two\.qa\.cox\.com/,
  QALOAD: /.*staging\.cox\.com/,
  STAGE: /.*test\.cox\.com/,
  PROD: /.*.cox\.com/
}

export const isLocalHost = () => window.location.href.includes('localhost')
export const isDev = [AUTHOR_DOMAINS.DEV, PUBLISHER_DOMAINS.DEV, COX_DOMAINS.DEV, SPANISH_COX_DOMAINS.DEV]
export const isQA1 = [AUTHOR_DOMAINS.QA1, PUBLISHER_DOMAINS.QA1, COX_DOMAINS.QA1, SPANISH_COX_DOMAINS.QA1]
export const isQA2 = [AUTHOR_DOMAINS.QA2, PUBLISHER_DOMAINS.QA2, COX_DOMAINS.QA2, SPANISH_COX_DOMAINS.QA2]
export const isQALoad = [AUTHOR_DOMAINS.QALOAD, PUBLISHER_DOMAINS.QALOAD, COX_DOMAINS.QALOAD, SPANISH_COX_DOMAINS.QALOAD]
export const isStage = [AUTHOR_DOMAINS.STAGE, PUBLISHER_DOMAINS.STAGE, COX_DOMAINS.STAGE, SPANISH_COX_DOMAINS.STAGE]
export const isProd = [AUTHOR_DOMAINS.PROD, PUBLISHER_DOMAINS.PROD, COX_DOMAINS.PROD, SPANISH_COX_DOMAINS.PROD]

const getCoxDomainUrl = (origin: string) => {
  if (isDev.includes(origin)) return COX_DOMAINS.DEV
  else if (isQA1.includes(origin)) return COX_DOMAINS.QA1
  else if (isQA2.includes(origin)) return COX_DOMAINS.QA2
  else if (isQALoad.includes(origin)) return COX_DOMAINS.QALOAD
  else if (isStage.includes(origin)) return COX_DOMAINS.STAGE
  else if (isProd.includes(origin)) return COX_DOMAINS.PROD
  else return COX_DOMAINS.STAGE
}

export const COX_DOMAIN_URL = getCoxDomainUrl(ORIGIN)

/** WEB APIS */
export const WEB_APIS = {
  CTAM_API: `${COX_DOMAIN_URL}/webapi/aem/ctam-token`,
  FLEX_OFFERS_API: `${COX_DOMAIN_URL}/webapi/aem/channelsales/getflexoffers`,
  FLEX_OFFER_ADDRESS_API: `${COX_DOMAIN_URL}/webapi/aem/channelsales/getflexoffers/address`,
  ADDRESS_SERVICEABILITY_API: `${COX_DOMAIN_URL}/webapi-wafr3/aem/addressserviceability`,
  ADDRESS_SERVICEABILITY_CB_API: `${COX_DOMAIN_URL}/webcbapi/aem/v2/addressserviceability?uiv8=true`,
  HEADER_PROFILE_API: `${COX_DOMAIN_URL}/webapi/aem/headerprofile?webp=true`,
  BELL_NOTIFICATION_API: `${COX_DOMAIN_URL}/webapi-wafr3/aem/user-alerts?&contextID=WEB_SWAP_CONTEXT2&count=5`,
  SET_DISPOSITION_API: `${COX_DOMAIN_URL}/webapi/aem/setdisposition`,
  YOUTUBE_API: `${COX_DOMAIN_URL}/webapi/aem/youtube-service`,
  CURRENT_SUBSCRIPTION: `${COX_DOMAIN_URL}/webapi/aem/currentsubscription`,
  SET_DISPOSITION: `${COX_DOMAIN_URL}/webapi/aem/setdisposition`
}

export const MODEL_JSON = `.model.json`
/** AEM PATHS */
export const DAM_ICON_PATH = '/content/dam/cox/common/icons/ui_components/'
export const VDF_MODEL_PATH = `/vdf/api/customruleengine${MODEL_JSON}`

/**Geo Location */
export const SERVICABLE_CITIES_API = 'https://framework.cox.com/presentation/rest/3.0/locations/cities/'
export const COX_BUSINESS_HOME_URL = 'https://www.cox.com/business/home.html'
export const FIND_NEW_PROVIDER_URL = 'https://www.smartmove.us/find?ccode=MRCX1010&zip='
export const COX_RESIDENTIAL_SIGN_IN_URL =
  'https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresaccount%2Fhome.html'

export const SAVINGS_CALCULATOR_PATH = `/content/dam/cox/residential/savings-calculator/savingsCalculator.json`
/** Dynamic Media Paths */
export const DYNAMIC_MEDIA_STAGING = 'https://assets.cox.com/is/image/coxstage/'
export const DYNAMIC_MEDIA_PROD = 'https://assets.cox.com/is/image/cox/'

/**Address Capture */
export const SMART_MOVE_SEARCH_API = 'https://www.smartmove.us/Widget/SmartMove/smartmovesearch.js'
export const GOOGLE_MAP_KEY =
  'https://maps.googleapis.com/maps/api/js?client=gme-coxenterprises&v=3.exp&channel=cci-cox-centers&libraries=places'

/**List items on skeleton */
export const SKELETON_ITEMS = ['1', '2', '3']

/* zindex const in tsx files */
export const STICKY_OVERLAY_ZINDEX = 1040

export const ADOBE_TARGET_VISITOR_INSTANCE = '8C6767C25245AD1A0A490D4C@AdobeOrg'

export const offerElementsClasses = {
  eyebrow: 'plan-name',
  heading: 'heading',
  headingValue: 'heading-value',
  headingUnit: 'heading-unit',
  additionalNote: 'additional-note',
  description: 'description'
}

export const MCP_ADDRESS_CAPTURE_MISMATCH = 'MCP_MISMATCH_EVENT'
export const MCP_ADDRESS_CAPTURE_EXACTMATCH = 'MCP_EXACTMATCH_EVENT'
