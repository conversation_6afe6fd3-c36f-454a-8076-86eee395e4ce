/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useEffect, useState } from 'react'

import { useAddressContext } from '@cox/core-ui8/dist/AddressContext'
import { useAppDataContext } from '@cox/core-ui8/dist/AppDataContext'
import { Fallback } from '@cox/core-ui8/dist/Fallback'
import { PageLoader } from '@cox/core-ui8/dist/PageLoader'
import { useUdoContext } from '@cox/core-ui8/dist/UdoContext'

import AppContent from './AppContent'
import { HOST } from './constants'
import { getModelData } from './services'
import { setNewRelicCustomAttributes } from './utils/newrelic-util'
import { setUtagData } from './utils/utag-util'

const App = () => {
  const { fullAddress } = useAddressContext()
  const { appData, setAppData } = useAppDataContext()
  const { setUdoData } = useUdoContext()

  const [isPageLoading, setIsPageLoading] = useState(false)
  const [isError, setIsError] = useState(false)

  useEffect(() => {
    setIsPageLoading(true)
    setAppData({
      page: {},
      udo: {},
      success: false
    })
    setUdoData({})
    getModelData()
      .then((dataModelRes: any) => {
        if (dataModelRes?.udo && Object.keys(dataModelRes?.udo).length > 0) {
          setAppData({ ...dataModelRes, success: true })
          setUdoData(dataModelRes?.udo)
          setUtagData(dataModelRes?.udo, dataModelRes?.page)
        } else {
          setAppData({
            page: dataModelRes,
            udo: {},
            success: true
          })
          setUdoData({})
        }
      })
      .catch((error: any) => {
        console.error(error)
        setIsError(true)
      })
      .finally(() => {
        setIsPageLoading(false)
        setNewRelicCustomAttributes()
      })
  }, [fullAddress])

  if (isPageLoading && !HOST.includes('author')) {
    return <PageLoader />
  }
  if (Object.keys(appData?.page).length > 0) {
    return <AppContent />
  } else if (isError) {
    return <Fallback />
  }
  return <></>
}

export default App
