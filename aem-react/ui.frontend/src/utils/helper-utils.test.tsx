import parse from 'html-react-parser'

import { CoxThemeClass } from './types'

describe('helper-util', () => {
  describe('getFormattedDate', () => {
    test('should return the correct date format', async () => {
      const { getFormattedDate } = await import('./helper-util')
      const dateInMillisecond = new Date('2025-02-02').getTime()
      const expectedDate = '2025-02-01'
      expect(getFormattedDate(dateInMillisecond)).toEqual(expectedDate)
    })
    test('should return the correct date format when the month is single digit', async () => {
      const { getFormattedDate } = await import('./helper-util')
      const dateInMillisecond = new Date('2025-3-03').getTime()
      const expectedDate = '2025-03-03'
      expect(getFormattedDate(dateInMillisecond)).toEqual(expectedDate)
    })
    test('should return the correct date format when the day is single digit', async () => {
      const { getFormattedDate } = await import('./helper-util')
      const dateInMillisecond = new Date('2025-04-4').getTime()
      const expectedDate = '2025-04-04'
      expect(getFormattedDate(dateInMillisecond)).toEqual(expectedDate)
    })
    test('should return the correct date format when the day and month are single digits', async () => {
      const { getFormattedDate } = await import('./helper-util')
      const dateInMillisecond = new Date('2025-5-5').getTime()
      const expectedDate = '2025-05-05'
      expect(getFormattedDate(dateInMillisecond)).toEqual(expectedDate)
    })
  })
  describe('getThemeClass', () => {
    beforeEach(() => {
      jest.resetModules()
      jest.clearAllMocks()
    })

    test('returns correct theme for fiber URL', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.cox.com/fiber/residential'
      }))

      const { getThemeClass } = await import('./helper-util')
      expect(getThemeClass()).toEqual(CoxThemeClass.RESIDENTIAL)
    })

    test('returns correct theme for mobile URL', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.cox.com/mobile/residential'
      }))

      const { getThemeClass } = await import('./helper-util')
      expect(getThemeClass()).toEqual(CoxThemeClass.RESIDENTIAL)
    })

    test('returns correct theme for business URL', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.cox.com/business'
      }))

      const { getThemeClass } = await import('./helper-util')
      expect(getThemeClass()).toEqual(CoxThemeClass.BUSINESS)
    })

    test('returns residential theme for other URLs', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.cox.com/other'
      }))

      const { getThemeClass } = await import('./helper-util')
      expect(getThemeClass()).toEqual(CoxThemeClass.RESIDENTIAL)
    })
  })
  describe('appendHash', () => {
    beforeEach(() => {
      jest.resetModules()
    })
    test('should append a hash to a string', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.example.com',
        SEARCH: '?query=test'
      }))
      const { appendHash } = await import('./helper-util')
      expect(appendHash('example.com', 'anchor')).toEqual('example.com?query=test#anchor')
    })
    test('should handle hashes in URL correctly', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.example.com#test',
        SEARCH: ''
      }))
      const { appendHash } = await import('./helper-util')
      expect(appendHash('example.com', 'anchor')).toEqual('example.com#anchor')
    })
    test('should handle empty URL correctly', async () => {
      jest.doMock('../constants', () => ({
        HREF: 'https://www.example.com',
        SEARCH: ''
      }))
      const { appendHash } = await import('./helper-util')
      expect(appendHash('', 'anchor')).toEqual('#anchor')
    })
  })
  describe('customParse', () => {
    test('should return the empty string when text is empty', async () => {
      const { customParse } = await import('./helper-util')
      const text = ''
      const result = customParse(text)
      expect(result).toEqual('')
    })
    test('should parse the text and return the result when parseScript is false', async () => {
      const { customParse } = await import('./helper-util')
      const text = '<div>Hello, world!</div>'
      const result = customParse(text)
      expect(result).toEqual(parse(text))
    })
    test('should parse the text and return the result when parseScript is true', async () => {
      const { customParse } = await import('./helper-util')
      const text = '<script src="https://example.com/script.js">console.log("Hello, world!")</script>'
      const result = customParse(text, true)
      expect(result).toMatchInlineSnapshot(`
      <HelmetWrapper
        defer={true}
        encodeSpecialCharacters={true}
      >
        <script
          src="https://example.com/script.js"
        >
          console.log("Hello, world!")
        </script>
      </HelmetWrapper>
    `)
    })
  })
  describe('sortArrayByKey', () => {
    test('should return the sorted array when the array is not empty', async () => {
      const { sortArrayByKey } = await import('./helper-util')
      const array = [
        { name: 'Item 3', value: 3 },
        { name: 'Item 1', value: 1 },
        { name: 'Item 2', value: 2 }
      ]
      const result = sortArrayByKey(array, 'name')
      expect(result).toEqual([
        { name: 'Item 1', value: 1 },
        { name: 'Item 2', value: 2 },
        { name: 'Item 3', value: 3 }
      ])
    })
    test('should return null when the array is empty', async () => {
      const { sortArrayByKey } = await import('./helper-util')
      const result = sortArrayByKey([], 'name')
      expect(result).toBeNull()
    })
  })
  describe('scrollToCarouselElementWithOffset', () => {
    test('should handle scrolling to the anchor on the current page', async () => {
      window.scrollTo = jest.fn()
      Object.defineProperty(window, 'scrollY', { value: 100 })
      const targetElement = {
        getBoundingClientRect: () => ({
          top: 100
        })
      }
      const stickyNav = {
        getBoundingClientRect: () => ({
          height: 50
        })
      }
      document.querySelector = jest.fn().mockReturnValueOnce(stickyNav).mockReturnValueOnce(targetElement)

      const { scrollToCarouselElementWithOffset } = await import('./helper-util')
      scrollToCarouselElementWithOffset({ preventDefault: jest.fn() }, '/#anchor', 'anchor')
      expect(window.scrollTo).toHaveBeenCalledWith({
        top: 100,
        behavior: 'smooth'
      })
    })
    test('should not scroll if the anchor is not found', async () => {
      window.scrollTo = jest.fn()
      document.querySelector = jest.fn().mockReturnValueOnce(null)
      const { scrollToCarouselElementWithOffset } = await import('./helper-util')
      scrollToCarouselElementWithOffset({ preventDefault: jest.fn() }, '/#anchor', 'anchor')
      expect(window.scrollTo).not.toHaveBeenCalled()
    })
  })
  describe('groupBy', () => {
    test('should group an array of objects by a specific key', async () => {
      const array = [
        { name: 'John', age: 25 },
        { name: 'Alice', age: 30 },
        { name: 'Bob', age: 20 }
      ]
      const getKeyMock = jest.fn((item: any) => item.age)
      const { groupBy } = await import('./helper-util')
      const result = groupBy(array, getKeyMock)
      expect(result).toEqual({
        25: [{ name: 'John', age: 25 }],
        30: [{ name: 'Alice', age: 30 }],
        20: [{ name: 'Bob', age: 20 }]
      })
    })
    test('should return an empty object when the array is empty', async () => {
      const getKeyMock = jest.fn((item: any) => item.age)
      const { groupBy } = await import('./helper-util')
      const result = groupBy([], getKeyMock)
      expect(result).toEqual({})
    })
    test('should group the array by the specified key when multiple items have the same key', async () => {
      const array = [
        { name: 'John', age: 25 },
        { name: 'Alice', age: 30 },
        { name: 'Bob', age: 20 },
        { name: 'Charlie', age: 25 }
      ]
      const getKeyMock = jest.fn((item: any) => item.age)
      const { groupBy } = await import('./helper-util')
      const result = groupBy(array, getKeyMock)
      expect(result).toEqual({
        25: [
          { name: 'John', age: 25 },
          { name: 'Charlie', age: 25 }
        ],
        30: [{ name: 'Alice', age: 30 }],
        20: [{ name: 'Bob', age: 20 }]
      })
    })
  })
  describe('extractByPropertyNames', () => {
    test('should return an empty array when the data array is empty', async () => {
      const { extractByPropertyNames } = await import('./helper-util')
      const result = extractByPropertyNames([], 'propertyName')
      expect(result).toEqual([])
    })
    test('should return an array of objects with the specified property names and custom properties', async () => {
      const data = [
        { propertyName: [{ a: 'value1' }, { b: 'value2' }], otherProperty: 'otherValue1' },
        { propertyName: [{ a: 'value3' }, { b: 'value4' }], otherProperty: 'otherValue2' }
      ]
      const customProperties = { customProperty: 'customValue' }
      const { extractByPropertyNames } = await import('./helper-util')
      const result = extractByPropertyNames(data, 'propertyName', customProperties)
      expect(result).toEqual([
        { a: 'value1', customProperty: 'customValue' },
        { b: 'value2', customProperty: 'customValue' },
        { a: 'value3', customProperty: 'customValue' },
        { b: 'value4', customProperty: 'customValue' }
      ])
    })
  })
  describe('getCommonPropertyValue', () => {
    test('should return the common property value when all objects have the same value', async () => {
      const { getCommonPropertyValue } = await import('./helper-util')
      const array = [{ propertyName: 'Item 1' }, { propertyName: 'Item 1' }, { propertyName: 'Item 1' }]
      const result = getCommonPropertyValue(array, 'propertyName')
      expect(result).toEqual('Item 1')
    })
    test('should return null when objects have different values', async () => {
      const { getCommonPropertyValue } = await import('./helper-util')
      const array = [{ propertyName: 'Item 1' }, { propertyName: 'Item 2' }, { propertyName: 'Item 3' }]
      const result = getCommonPropertyValue(array, 'propertyName')
      expect(result).toBeNull()
    })
    test('should return null when the array is empty', async () => {
      const { getCommonPropertyValue } = await import('./helper-util')
      const result = getCommonPropertyValue([], 'propertyName')
      expect(result).toBeNull()
    })
  })
})
